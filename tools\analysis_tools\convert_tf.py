import os
import argparse
import json
from collections import defaultdict

from torch.utils.tensorboard import SummaryWriter


def parse_args():
    parser = argparse.ArgumentParser(description='parse json log')
    parser.add_argument('--log-dir', type=str, help='path to json log')
    parser.add_argument('--out-dir', type=str, default='./work_dirs/analysis', help='output dir')
    parser.add_argument('--key-indicator', type=str, nargs='+', default=['accuracy_top-1', 'mAP'], help='best metric')
    parser.add_argument('--max-epoch', type=int, default=200, help='help to check the incompleteness of model training')
    args = parser.parse_args()
    return args


def parse_json(filepath, max_epoch):
    log_dict = {'train': defaultdict(list), 'val': defaultdict(list)}
    with open(filepath, 'r') as f:
        lines = f.read().splitlines()
    for line_str in lines:
        line_dict = json.loads(line_str)
        if 'mode' not in line_dict:
            continue
        line_mode = line_dict.pop('mode')
        mode_dict = log_dict[line_mode]
        for k, v in line_dict.items():
            mode_dict[k].append(v)
    if log_dict['val']['epoch'] != range(1, max_epoch + 1):
        for epoch in range(1, max_epoch + 1):
            if epoch not in log_dict['val']['epoch']:
                print(f'missing messages for {epoch} epoch')
                for k, v in log_dict['val'].items():
                    v.insert(epoch - 1, v[epoch])
    for k, v in log_dict['val'].items():
        if len(v) < max_epoch:
            assert k == 'confusion_matrix', f'invalid indicator {k} with length {len(v)}'
            v[0:0] = [None] * (max_epoch - len(v))
    return log_dict


def calculate_best(log_dict, key_indicators, out_dir):
    mode_dict = log_dict['val']
    indicator_dict = dict()
    for key_indicator in key_indicators:
        indicator_vals = mode_dict.get(key_indicator)
        if indicator_vals is None:
            continue
        indicator_dict[key_indicator] = indicator_vals
    assert len(indicator_dict.keys()) == 1, 'invalid key indicator'
    key_indicator = list(indicator_dict.keys())[0]
    indicator_vals = indicator_dict[key_indicator]
    best_val = max(indicator_vals)
    best_i = indicator_vals.index(best_val)
    best_dict = dict(key_indicator=key_indicator)
    for k, v in mode_dict.items():
        best_dict[k] = v[best_i]
    with open(f'{out_dir}/best.json', 'w') as f:
        json.dump(best_dict, f)
    best_path = f'{out_dir}/best.pth'
    if os.path.islink(best_path):
        os.remove(best_path)
    os.symlink(f'epoch_{best_i + 1}.pth', best_path)
    return best_dict


def save_tf_log(log_dict, rootpath, out_dir):
    log_dir = os.path.join(out_dir, rootpath.rsplit('/', 2)[-2])
    writer = SummaryWriter(log_dir=log_dir)
    for mode, mode_dict in log_dict.items():
        del mode_dict['epoch'], mode_dict['iter']
        if mode == 'train':
            del mode_dict['memory'], mode_dict['data_time'], mode_dict['time']
        else: # val
            del mode_dict['lr']
        for k, v in mode_dict.items():
            if k == 'confusion_matrix':
                continue
            tag = mode + '/' + k
            print('Saving tf scalar: {}{}'.format(tag, ' ' * 20), end='\r')
            for i, vi in enumerate(v):
                if not isinstance(vi, list):
                    writer.add_scalar(tag=tag, scalar_value=vi, global_step=i)
                    continue
                for j, vij in enumerate(vi):
                    writer.add_scalar(tag=f'{tag}[{j}]', scalar_value=vij, global_step=i)
    writer.close()
    return None


def main():
    args = parse_args()
    assert os.path.isdir(args.log_dir), 'invalid log folder'
    for rootpath, dirnames, filenames in os.walk(args.log_dir):
        relpath = rootpath.replace(args.log_dir + '/', '')
        if f'epoch_{args.max_epoch}.pth' not in filenames:
            print(f'\n{relpath} is skipped for incompleteness')
            continue
        print(f'\nprocessing {relpath}')
        for filename in filenames:
            if not filename.endswith('.log.json'):
                continue
            filepath = os.path.join(rootpath, filename)
            log_dict = parse_json(filepath, args.max_epoch)
            best_dict = calculate_best(log_dict, args.key_indicator, rootpath)
            print(f'Best for {relpath}: {best_dict}')
            save_tf_log(log_dict, rootpath, args.out_dir)
            print('Saved tf log: {}'.format(rootpath))
    return None

if __name__ == '__main__':
    main()
