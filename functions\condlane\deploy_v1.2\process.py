import mmcv
import numpy as np

from core.post_processing.condlane_post_processor import CondLanePostProcessor, sigmoid


def parse_roi(W, H, l=0.0, t=0.0, r=1.0, b=1.0):
    l = W * l if l <= 1 else l
    t = H * t if t <= 1 else t
    r = W * r if r <= 1 else r
    b = H * b if b <= 1 else b
    l, t, r, b = map(int, [l, t, r, b])
    assert 0 <= l and r <= W
    assert 0 <= t and b <= H
    return l, t, r, b


def pre_process(
    imfile,
    bbox=None,
    break_point_range=(0, 1),
    img_scale=(1024, 576),
    mean=[75.3, 76.6, 77.6],
    std=[50.5, 53.8, 54.3],
    crop=True,
):
    if isinstance(imfile, str):
        img = mmcv.imread(imfile)
    else:
        img = imfile
    H, W = img.shape[:2]
    bk_top = break_point_range[0] * H
    bk_bottom = (break_point_range[1] - break_point_range[0]) * H
    bk_roi = {
        "left": 0,
        "top": int(bk_top),
        "width": W,
        "height": int(bk_bottom),
        "#keson_code": "RCT",
    }
    w, h = img_scale
    if bbox is not None:
        bbox = parse_roi(W, H, **bbox)
        roi = {
            "left": bbox[0],
            "top": bbox[1],
            "width": bbox[2] - bbox[0],
            "height": bbox[3] - bbox[1],
            "#keson_code": "RCT",
        }
        if crop:
            img = mmcv.imcrop(img, np.array(bbox))
        else:
            w = w * roi["width"] / W
            h = h * roi["height"] / H
    else:
        roi = {
            "left": 0,
            "top": 0,
            "width": W,
            "height": H,
            "#keson_code": "RCT",
        }
    #img = mmcv.imresize(img, (img_scale[0], img_scale[1]))
    img = mmcv.imresize(img, (img_scale[0], img_scale[1]))
    #img = img.astype(np.float32)
    #img = mmcv.imresize(img, (896, 504))
    u8 = img.transpose(2, 0, 1)
    """
    img = mmcv.imnormalize(
        img,
        mean=np.array(mean),
        std=np.array(std),
        to_rgb=False,
    )
 
    img = img.transpose(2, 0, 1)
    """
    info = [roi, bk_roi, w, h, u8]
    return img, info


def mid_process(
    proposal_heatmap,
    maxpool,
    parameter_map=None,
    feat_map=None,
    th_proposal=0.1,
):
    # NMS & propose proposal_indices
    # N 2 H W -> M 4
    proposal_heatmap = (proposal_heatmap >= maxpool) * proposal_heatmap
    proposal_indices = np.where(proposal_heatmap > float(th_proposal))
    pn, pc, ph, pw = proposal_indices
    proposal_indices = np.stack(proposal_indices, -1)
    # gather cls_score
    # N 2 H W -> M
    cls_score = proposal_heatmap[pn, pc, ph, pw]
    if parameter_map is None:
        parameter = None
    else:
        # gather parameter
        # N C H W -> M C/2
        N, C, H, W = parameter_map.shape
        parameter = parameter_map.reshape(N, 2, -1, H, W)[pn, pc, :, ph, pw]
    if feat_map is None:
        feat = None
    else:
        # gather feat
        # N C H W -> M C/2 H W
        N, C, H, W = feat_map.shape
        feat = feat_map.reshape(N, 2, -1, H, W)[pn, pc]
    return proposal_indices, cls_score, parameter, feat


def dump_results(
    data_info,
    outputs,
    config,
    proposal_heatmap_thr=0.1,
):
    proposal_heatmap = outputs["proposal_heatmap"]
    maxpool = outputs["maxpool"]
    if config.range_method == "field":
        proposal_heatmap = sigmoid(proposal_heatmap)
        maxpool = sigmoid(maxpool)
    proposal_indices, cls_score, parameter, feat = mid_process(
        proposal_heatmap,
        maxpool,
        outputs.get("parameter_map", None),
        outputs.get("feat_map", None),
        th_proposal=proposal_heatmap_thr,
    )
    post_processor = CondLanePostProcessor(**config.post_processor)
    preds = post_processor(proposal_indices, cls_score, outputs)
    scale = config.mask_down_scale
    batch_results = []
    for i, (roi, bk_roi, w, h, _, image_path) in enumerate(data_info):
        image_results = []
        for j, label in enumerate(["laneline", "roadside"]):
            dts = [
                _ for _ in preds if _["image_id"] == i and _["label_id"] == j
            ]
            dts = post_processor.fmt_pred(dts, roi, bk_roi, w, h, label, [],
                                          "", scale)
            for _ in dts:
                _["image_path"] = image_path
                image_results.append(_)
        batch_results.append(image_results)
    return batch_results
