from mmcls.datasets.pipelines import to_tensor

from ..builder import PIPELINES


@PIPELINES.register_module()
class AttributeToTensor(object):
    def __init__(self, key):
        self.key = key

    # attributes is a nest dict
    def convert(self, attributes):
        for attr_name, value in attributes.items():
            if isinstance(value, dict):
                attributes[attr_name] = self.convert(value)
            else:
                attributes[attr_name] = to_tensor(value)
        return attributes

    def __call__(self, results):
        results[self.key] = self.convert(results[self.key])
        return results

    def __repr__(self):
        return self.__class__.__name__ + f'(key={self.key})'
