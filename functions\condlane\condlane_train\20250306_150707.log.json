{"env_info": "sys.platform: linux\nPython: 3.6.9 |Anaconda, Inc.| (default, Jul 30 2019, 19:07:31) [GCC 7.3.0]\nCUDA available: True\nGPU 0,1: NVIDIA GeForce RTX 3090\nCUDA_HOME: /usr/local/cuda\nNVCC: Build cuda_12.1.r12.1/compiler.32415258_0\nGCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0\nPyTorch: 1.8.1+cu111\nPyTorch compiling details: PyTorch built with:\n  - GCC 7.3\n  - C++ Version: 201402\n  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications\n  - Intel(R) MKL-DNN v1.7.0 (Git Hash 7aed236906b1f7a05c0917e5257a1af05e9ff683)\n  - OpenMP 201511 (a.k.a. OpenMP 4.5)\n  - NNPACK is enabled\n  - CPU capability usage: AVX2\n  - CUDA Runtime 11.1\n  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86\n  - CuDNN 8.0.5\n  - Magma 2.5.2\n  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.1, CUDNN_VERSION=8.0.5, CXX_COMPILER=/opt/rh/devtoolset-7/root/usr/bin/c++, CXX_FLAGS= -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-sign-compare -Wno-unused-parameter -Wno-unused-variable -Wno-unused-function -Wno-unused-result -Wno-unused-local-typedefs -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.8.1, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, \n\nTorchVision: 0.9.1+cu111\nOpenCV: 4.5.1\nMMCV: 1.4.8\nMMCV Compiler: GCC 7.3\nMMCV CUDA Compiler: 11.1\nMMEnterprise: 0.2.1+", "config": "custom_imports = dict(imports=[\n    'condlane.datasets', 'condlane.datasets.pipelines',\n    'condlane.models.backbones', 'condlane.models.necks',\n    'condlane.models.dense_heads', 'condlane.models.losses',\n    'condlane.models.predictors'\n])\ntest_setting = dict(\n    repo='pillar',\n    single_gpu_test=dict(show=False),\n    multi_gpu_test=dict(gpu_collect=True))\nfind_unused_parameters = True\ndist_params = dict(port='29507')\nbatch_size = 8\nnum_workers = 4\ntokestrel = dict(\n    model_name='ep20_r331_data277w_single_stage_relu_sm1_mirror_soft0.5',\n    version='3.3.1',\n    batch_size=dict(image=1, image2=1))\ndataset_type = 'pillar.LkdCropClsDataset'\nceph_cfg = ''\ntrain_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'\ntrain_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'\nval_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'\nval_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'\nnum_lane_classes = 2\ncategories = dict(\n    group=['lane_type', 'lane_color', 'roadside'],\n    attr=dict(\n        lane_type=[\n            'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n            'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n            'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n            'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n            'FISHBONE_DASHED'\n        ],\n        lane_color=[\n            'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE', 'BLUE'\n        ],\n        roadside=[\n            'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n            'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n        ]))\nview_distance = 5\ninv_view_distance = 5\nnum_ref_point = 2\nlength_scale = 0\nfield_scale = None\nmaskAct = 'Hsigmoid'\nlaneline_indices = dict(length=27, offset=29, field=30, view=(33, 37))\nroadside_indices = dict(length=28, offset=31, field=32, view=(37, 41))\ngt_indices = dict(\n    offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23))\nimg_scale = (864, 288)\nh_crop = (0, 288)\ncamera_roi = dict(\n    front_fov30=dict(t=0, b=1, l=0, r=1),\n    front_fov120=dict(t=0, b=1, l=0, r=1))\nimg_norm_cfg = dict(\n    mean=[75.3, 76.6, 77.6], std=[50.5, 53.8, 54.3], to_rgb=False)\nproposal_heatmap_thr = 0.6\nrange_method = 'field'\npost_processor = dict(\n    key_points_size_thr=35,\n    side_key_points_size_thr=35,\n    range_method='field',\n    cls_method='row_att',\n    with_breakpoints=True,\n    breakpoints_thr=0.6,\n    range_thr=0,\n    range_continue_tole=0,\n    use_offset=True,\n    nms_thr=0.0,\n    categories=dict(\n        group=['lane_type', 'lane_color', 'roadside'],\n        attr=dict(\n            lane_type=[\n                'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                'FISHBONE_DASHED'\n            ],\n            lane_color=[\n                'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                'BLUE'\n            ],\n            roadside=[\n                'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n            ])),\n    laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),\n    roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),\n    mask_upsample_rate=4,\n    num_row_ref_point=2,\n    inv_view_distance=5,\n    length_scale=0,\n    field_scale=None,\n    with_hm=True,\n    mask_sigma=0.5,\n    mask_max_thr=0.0,\n    th_len=10,\n    v4_simplify=True,\n    mask_ignores=30)\nmask_down_scale = 4\nhm_down_scale = 16\nline_width = 4\nradius = 3\nmax_mask_sample = 4\ntrain_cfg = dict(out_scale=4)\ntest_cfg = dict(out_scale=4)\nloss_weights = dict(\n    hm_weight=2.5,\n    kps_weight=0.4,\n    row_weight=1.5,\n    range_weight=2.0,\n    lane_type_weight=[\n        1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0\n    ],\n    row_ce_weight=1.0,\n    len_loss_weight=1.0,\n    auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],\n    d2_loss_weight=0.5,\n    smooth_weight=0.0,\n    view_distance=5,\n    inv_view_distance=5,\n    num_ref_point=2,\n    length_scale=0,\n    field_scale=None,\n    field_bg_weight=[-1, 0.1],\n    attr_bg_weight=0.1,\n    mask_bg_weight=[-1, 0.1],\n    maskAct='Hsigmoid',\n    reg_smooth_l1=True)\nnorm_cfg = dict(type='BN2d', requires_grad=True)\nmodel = dict(\n    type='pillar.FieldLaneNet',\n    train_cfg=dict(out_scale=4),\n    test_cfg=dict(out_scale=4),\n    num_classes=2,\n    loss_condlane=dict(\n        type='pillar.FieldLaneLoss',\n        weights=dict(\n            hm_weight=2.5,\n            kps_weight=0.4,\n            row_weight=1.5,\n            range_weight=2.0,\n            lane_type_weight=[\n                1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0\n            ],\n            row_ce_weight=1.0,\n            len_loss_weight=1.0,\n            auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],\n            d2_loss_weight=0.5,\n            smooth_weight=0.0,\n            view_distance=5,\n            inv_view_distance=5,\n            num_ref_point=2,\n            length_scale=0,\n            field_scale=None,\n            field_bg_weight=[-1, 0.1],\n            attr_bg_weight=0.1,\n            mask_bg_weight=[-1, 0.1],\n            maskAct='Hsigmoid',\n            reg_smooth_l1=True),\n        property_group=[13, 6, 6]),\n    backbone=dict(\n        type='pillar.ConvNeXtTI',\n        depths=[2, 3, 9, 3],\n        dims=[64, 64, 128, 128],\n        drop_path_rate=0.2,\n        norm_cfg=dict(type='BN2d', requires_grad=True),\n        ActType='ReLU',\n        layer_scale_init_value=0),\n    neck=dict(\n        type='pillar.TransConvFPN',\n        in_channels=[64, 64, 128, 128],\n        out_channels=64,\n        num_outs=4,\n        trans_idx=-1,\n        trans_cfg=None,\n        next_conv_cfg=dict(\n            in_channel=128,\n            channels=[128, 128],\n            kernels=[[7, 7], [7, 7]],\n            expand_ratios=[2, 2],\n            is_plus=True,\n            ActType='ReLU'),\n        attention=False,\n        kernel_size=7,\n        deconv=False,\n        norm_cfg=dict(type='BN2d', requires_grad=True),\n        lateral_norm_cfg=None,\n        fpn_norm_cfg=None),\n    decoder=dict(\n        type='pillar.SegFormerHead',\n        in_channels=[64, 64, 128, 128],\n        in_index=[0, 1, 2, 3],\n        feature_strides=[4, 8, 16, 32],\n        channels=128,\n        dropout_ratio=0.1,\n        num_classes=150,\n        align_corners=False,\n        decoder_params=dict(embed_dim=768),\n        loss_decode=dict(\n            type='CrossEntropyLoss', use_sigmoid=False, loss_weight=1.0)),\n    spatial_head=dict(\n        type='pillar.DepthRegressionModel', in_channels=768, out_channels=1),\n    head=dict(\n        type='pillar.FieldLaneHead',\n        in_channel=64,\n        ActType='ReLU',\n        maskAct='Hsigmoid',\n        mask_property_group=dict(\n            line_mask=(2, 0, 1),\n            laneline_type=(13, 2, 1),\n            laneline_color=(6, 2, 1),\n            roadside_type=(6, 2, 1),\n            roadside_length=(1, 0, 2),\n            laneline_length=(1, 0, 2),\n            roadside_offset=(2, 0, 1),\n            laneline_offset=(2, 0, 1),\n            roadside_field=(2, 0, 2),\n            laneline_field=(2, 0, 2),\n            roadside_view=(8, 0, 2),\n            laneline_view=(8, 0, 2),\n            roadside_inv_vector_field=(10, 0, 2),\n            laneline_inv_vector_field=(10, 0, 2),\n            roadside_inv_length=(1, 0, 1),\n            laneline_inv_length=(1, 0, 1)),\n        num_classes=2,\n        with_break_point=False,\n        hm_idx=2,\n        mask_idx=0,\n        proposal_heatmap_thr=0.6,\n        range_method='field',\n        expand=1,\n        with_start_point_off=True,\n        with_start_point_feat_group=True,\n        with_nms_weight=True,\n        norm_cfg=dict(type='BN2d', requires_grad=True),\n        v4_simplify=True),\n    post_processor=dict(\n        key_points_size_thr=35,\n        side_key_points_size_thr=35,\n        range_method='field',\n        cls_method='row_att',\n        with_breakpoints=True,\n        breakpoints_thr=0.6,\n        range_thr=0,\n        range_continue_tole=0,\n        use_offset=True,\n        nms_thr=0.0,\n        categories=dict(\n            group=['lane_type', 'lane_color', 'roadside'],\n            attr=dict(\n                lane_type=[\n                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                    'FISHBONE_DASHED'\n                ],\n                lane_color=[\n                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                    'BLUE'\n                ],\n                roadside=[\n                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n                ])),\n        laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),\n        roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),\n        mask_upsample_rate=4,\n        num_row_ref_point=2,\n        inv_view_distance=5,\n        length_scale=0,\n        field_scale=None,\n        with_hm=True,\n        mask_sigma=0.5,\n        mask_max_thr=0.0,\n        th_len=10,\n        v4_simplify=True,\n        mask_ignores=30),\n    gt_indices=dict(\n        offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23)))\ncompose_params = dict(bboxes=False, keypoints=True, masks=True)\nDIAMOND_KERNEL_5 = [[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],\n                    [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]]\ntrain_al_pipeline = [\n    dict(\n        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),\n    dict(type='Resize', height=288, width=864, p=1)\n]\nval_al_pipeline = [\n    dict(\n        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),\n    dict(type='Resize', height=288, width=864, p=1)\n]\ntrain_pipeline = [\n    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n    dict(\n        type='pillar.Alaug',\n        transforms=[\n            dict(\n                type='Compose',\n                params=dict(bboxes=False, keypoints=True, masks=True)),\n            dict(type='Resize', height=288, width=864, p=1)\n        ]),\n    dict(\n        type='mmcls.Normalize',\n        mean=[75.3, 76.6, 77.6],\n        std=[50.5, 53.8, 54.3],\n        to_rgb=False),\n    dict(\n        type='pillar.DepthCompletion',\n        max_depth=100.0,\n        custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],\n                       [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],\n        extrapolate=False,\n        blur_type='bilateral'),\n    dict(\n        type='pillar.CollectBilateralFiledLane',\n        down_scale=4,\n        hm_down_scale=16,\n        property_down_rate=4,\n        num_drop_remote_point=0,\n        num_start_rows=3,\n        bg_weight=0.01,\n        soft_weight_len_alpha=0.5,\n        soft_weight_off_alpha=0.5,\n        soft_weight_x=True,\n        soft_weight_y=True,\n        draw_r=4,\n        num_ref_point=2,\n        inv_view_distance=5,\n        max_mask_sample=4,\n        line_width=4,\n        radius=3,\n        interval=5,\n        filter_pts=1,\n        endpoint='down',\n        keys=[\n            'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',\n            'roadside_gt_hm', 'lane_masks', 'lane_label_mask',\n            'lane_color_mask', 'roadside_masks', 'roadside_label_mask',\n            'roadside_field', 'roadside_attribute_target', 'depth_map'\n        ],\n        meta_keys=[\n            'filename', 'sub_img_name', 'mask_shape', 'hm_shape', 'ori_shape',\n            'img_shape', 'down_scale', 'hm_down_scale', 'img_norm_cfg'\n        ]),\n    dict(type='pillar.DefaultFormatBundle')\n]\nval_pipeline = [\n    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n    dict(\n        type='pillar.Alaug',\n        transforms=[\n            dict(\n                type='Compose',\n                params=dict(bboxes=False, keypoints=True, masks=True)),\n            dict(type='Resize', height=288, width=864, p=1)\n        ]),\n    dict(\n        type='mmcls.Normalize',\n        mean=[75.3, 76.6, 77.6],\n        std=[50.5, 53.8, 54.3],\n        to_rgb=False),\n    dict(\n        type='pillar.CollectLkdClsLane',\n        down_scale=4,\n        hm_down_scale=16,\n        radius=3),\n    dict(type='pillar.DefaultFormatBundle')\n]\ndata = dict(\n    samples_per_gpu=8,\n    workers_per_gpu=4,\n    train=dict(\n        type='pillar.LkdCropClsDataset',\n        ann_file=\n        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',\n        img_prefix='/home/<USER>/data/once_3d_lanes_data/',\n        pipeline=[\n            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n            dict(\n                type='pillar.Alaug',\n                transforms=[\n                    dict(\n                        type='Compose',\n                        params=dict(bboxes=False, keypoints=True, masks=True)),\n                    dict(type='Resize', height=288, width=864, p=1)\n                ]),\n            dict(\n                type='mmcls.Normalize',\n                mean=[75.3, 76.6, 77.6],\n                std=[50.5, 53.8, 54.3],\n                to_rgb=False),\n            dict(\n                type='pillar.DepthCompletion',\n                max_depth=100.0,\n                custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1,\n                                                 0], [1, 1, 1, 1, 1],\n                               [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],\n                extrapolate=False,\n                blur_type='bilateral'),\n            dict(\n                type='pillar.CollectBilateralFiledLane',\n                down_scale=4,\n                hm_down_scale=16,\n                property_down_rate=4,\n                num_drop_remote_point=0,\n                num_start_rows=3,\n                bg_weight=0.01,\n                soft_weight_len_alpha=0.5,\n                soft_weight_off_alpha=0.5,\n                soft_weight_x=True,\n                soft_weight_y=True,\n                draw_r=4,\n                num_ref_point=2,\n                inv_view_distance=5,\n                max_mask_sample=4,\n                line_width=4,\n                radius=3,\n                interval=5,\n                filter_pts=1,\n                endpoint='down',\n                keys=[\n                    'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',\n                    'roadside_gt_hm', 'lane_masks', 'lane_label_mask',\n                    'lane_color_mask', 'roadside_masks', 'roadside_label_mask',\n                    'roadside_field', 'roadside_attribute_target', 'depth_map'\n                ],\n                meta_keys=[\n                    'filename', 'sub_img_name', 'mask_shape', 'hm_shape',\n                    'ori_shape', 'img_shape', 'down_scale', 'hm_down_scale',\n                    'img_norm_cfg'\n                ]),\n            dict(type='pillar.DefaultFormatBundle')\n        ],\n        test_mode=False,\n        camera_roi=dict(\n            front_fov30=dict(t=0, b=1, l=0, r=1),\n            front_fov120=dict(t=0, b=1, l=0, r=1)),\n        categories=dict(\n            group=['lane_type', 'lane_color', 'roadside'],\n            attr=dict(\n                lane_type=[\n                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                    'FISHBONE_DASHED'\n                ],\n                lane_color=[\n                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                    'BLUE'\n                ],\n                roadside=[\n                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n                ])),\n        camera_filter=True),\n    val=dict(\n        type='pillar.LkdCropClsDataset',\n        ann_file=\n        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',\n        img_prefix='/home/<USER>/data/once_3d_lanes_data/',\n        pipeline=[\n            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n            dict(\n                type='pillar.Alaug',\n                transforms=[\n                    dict(\n                        type='Compose',\n                        params=dict(bboxes=False, keypoints=True, masks=True)),\n                    dict(type='Resize', height=288, width=864, p=1)\n                ]),\n            dict(\n                type='mmcls.Normalize',\n                mean=[75.3, 76.6, 77.6],\n                std=[50.5, 53.8, 54.3],\n                to_rgb=False),\n            dict(\n                type='pillar.CollectLkdClsLane',\n                down_scale=4,\n                hm_down_scale=16,\n                radius=3),\n            dict(type='pillar.DefaultFormatBundle')\n        ],\n        test_mode=True,\n        camera_roi=dict(\n            front_fov30=dict(t=0, b=1, l=0, r=1),\n            front_fov120=dict(t=0, b=1, l=0, r=1)),\n        categories=dict(\n            group=['lane_type', 'lane_color', 'roadside'],\n            attr=dict(\n                lane_type=[\n                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                    'FISHBONE_DASHED'\n                ],\n                lane_color=[\n                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                    'BLUE'\n                ],\n                roadside=[\n                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n                ])),\n        camera_filter=True),\n    test=dict(\n        type='pillar.LkdCropClsDataset',\n        ann_file=\n        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',\n        img_prefix='/home/<USER>/data/once_3d_lanes_data/',\n        pipeline=[\n            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n            dict(\n                type='pillar.Alaug',\n                transforms=[\n                    dict(\n                        type='Compose',\n                        params=dict(bboxes=False, keypoints=True, masks=True)),\n                    dict(type='Resize', height=288, width=864, p=1)\n                ]),\n            dict(\n                type='mmcls.Normalize',\n                mean=[75.3, 76.6, 77.6],\n                std=[50.5, 53.8, 54.3],\n                to_rgb=False),\n            dict(\n                type='pillar.CollectLkdClsLane',\n                down_scale=4,\n                hm_down_scale=16,\n                radius=3),\n            dict(type='pillar.DefaultFormatBundle')\n        ],\n        test_mode=True,\n        camera_roi=dict(\n            front_fov30=dict(t=0, b=1, l=0, r=1),\n            front_fov120=dict(t=0, b=1, l=0, r=1)),\n        categories=dict(\n            group=['lane_type', 'lane_color', 'roadside'],\n            attr=dict(\n                lane_type=[\n                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                    'FISHBONE_DASHED'\n                ],\n                lane_color=[\n                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                    'BLUE'\n                ],\n                roadside=[\n                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n                ])),\n        camera_filter=True))\nevaluation = dict(\n    type='mme.EvalHook',\n    dataset=dict(\n        type='pillar.LkdCropClsDataset',\n        ann_file=\n        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',\n        img_prefix='/home/<USER>/data/once_3d_lanes_data/',\n        pipeline=[\n            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),\n            dict(\n                type='pillar.Alaug',\n                transforms=[\n                    dict(\n                        type='Compose',\n                        params=dict(bboxes=False, keypoints=True, masks=True)),\n                    dict(type='Resize', height=288, width=864, p=1)\n                ]),\n            dict(\n                type='mmcls.Normalize',\n                mean=[75.3, 76.6, 77.6],\n                std=[50.5, 53.8, 54.3],\n                to_rgb=False),\n            dict(\n                type='pillar.CollectLkdClsLane',\n                down_scale=4,\n                hm_down_scale=16,\n                radius=3),\n            dict(type='pillar.DefaultFormatBundle')\n        ],\n        test_mode=True,\n        camera_roi=dict(\n            front_fov30=dict(t=0, b=1, l=0, r=1),\n            front_fov120=dict(t=0, b=1, l=0, r=1)),\n        categories=dict(\n            group=['lane_type', 'lane_color', 'roadside'],\n            attr=dict(\n                lane_type=[\n                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',\n                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',\n                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',\n                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',\n                    'FISHBONE_DASHED'\n                ],\n                lane_color=[\n                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',\n                    'BLUE'\n                ],\n                roadside=[\n                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',\n                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'\n                ])),\n        camera_filter=True),\n    dataloader=dict(samples_per_gpu=16, workers_per_gpu=2),\n    test_setting=dict(\n        repo='pillar',\n        single_gpu_test=dict(show=False),\n        multi_gpu_test=dict(gpu_collect=True)),\n    metric='accuracy',\n    interval=2,\n    by_epoch=True)\noptimizer = dict(\n    type='AdamW',\n    lr=0.0001,\n    weight_decay=0.0001,\n    paramwise_cfg=dict(\n        custom_keys=dict(\n            backbone=dict(lr_mult=0.1),\n            neck=dict(lr_mult=1.0),\n            head=dict(lr_mult=1.0)),\n        bias_decay_mult=0.0,\n        norm_decay_mult=0.0))\noptimizer_config = dict(grad_clip=dict(max_norm=3))\nlr_config = dict(\n    policy='CosineAnnealing',\n    min_lr=1e-06,\n    by_epoch=False,\n    warmup='linear',\n    warmup_iters=500,\n    warmup_ratio=0.01,\n    warmup_by_epoch=False)\nrunner = dict(type='mmcv.EpochBasedRunner', max_epochs=30)\ncheckpoint_config = dict(interval=1, by_epoch=True, max_keep_ckpts=1)\nlog_config = dict(interval=1, hooks=[dict(type='mmcv.TextLoggerHook')])\nlog_level = 'INFO'\nauto_resume = False\nresume_from = ''\nload_from = ''\nworkflow = [('train', 200)]\nwork_dir = './condlane_train/'\ngpu_ids = range(0, 1)\n", "seed": null, "exp_name": "config_r331_one_stage_11.6MB_finetuning.py"}
{"mode": "train", "epoch": 1, "iter": 1, "lr": 0.0, "memory": 6816, "data_time": 2.99192, "laneline_hm_loss": 4.10274, "roadside_hm_loss": 6.33858, "laneline_st_off_loss": 13.95234, "roadside_st_off_loss": 20.42561, "mask_loss": 1.21988, "lane_type_mask_loss": 1.32228, "lane_color_mask_loss": 0.5937, "side_type_mask_loss": 0.92239, "laneline_len_loss": 11.66173, "laneline_field_loss": 2.56916, "laneline_view_loss": 8.51059, "laneline_inv_len_loss": 8.53796, "laneline_inv_field_loss": 6.22605, "roadside_len_loss": 11.58947, "roadside_field_loss": 1.70329, "roadside_view_loss": 5.09814, "roadside_inv_len_loss": 7.75818, "roadside_inv_field_loss": 2.95603, "depth_loss": 0.15685, "loss": 115.64497, "grad_norm": 480.56073, "time": 3.88362}
{"mode": "train", "epoch": 1, "iter": 2, "lr": 0.0, "memory": 6893, "data_time": 0.00711, "laneline_hm_loss": 4.0769, "roadside_hm_loss": 6.31084, "laneline_st_off_loss": 17.49706, "roadside_st_off_loss": 21.07497, "mask_loss": 1.16269, "lane_type_mask_loss": 1.33288, "lane_color_mask_loss": 0.60782, "side_type_mask_loss": 0.93283, "laneline_len_loss": 11.63886, "laneline_field_loss": 2.63853, "laneline_view_loss": 8.79778, "laneline_inv_len_loss": 8.50617, "laneline_inv_field_loss": 6.47829, "roadside_len_loss": 11.42654, "roadside_field_loss": 1.73579, "roadside_view_loss": 5.06948, "roadside_inv_len_loss": 7.79528, "roadside_inv_field_loss": 3.00212, "depth_loss": 0.14854, "loss": 120.23338, "grad_norm": 550.69537, "time": 0.41124}
{"mode": "train", "epoch": 1, "iter": 3, "lr": 0.0, "memory": 6893, "data_time": 0.0046, "laneline_hm_loss": 3.08645, "roadside_hm_loss": 4.69641, "laneline_st_off_loss": 15.2047, "roadside_st_off_loss": 18.25456, "mask_loss": 0.8825, "lane_type_mask_loss": 1.30569, "lane_color_mask_loss": 0.58521, "side_type_mask_loss": 0.93283, "laneline_len_loss": 8.44724, "laneline_field_loss": 2.93458, "laneline_view_loss": 10.10785, "laneline_inv_len_loss": 6.03689, "laneline_inv_field_loss": 7.48247, "roadside_len_loss": 8.29116, "roadside_field_loss": 1.77435, "roadside_view_loss": 5.3834, "roadside_inv_len_loss": 5.50542, "roadside_inv_field_loss": 3.11204, "depth_loss": 0.23043, "loss": 104.25417, "grad_norm": 473.84485, "time": 0.39437}
{"mode": "train", "epoch": 1, "iter": 4, "lr": 0.0, "memory": 6893, "data_time": 0.00405, "laneline_hm_loss": 3.47968, "roadside_hm_loss": 5.35145, "laneline_st_off_loss": 16.55689, "roadside_st_off_loss": 18.23274, "mask_loss": 0.99155, "lane_type_mask_loss": 1.30931, "lane_color_mask_loss": 0.59771, "side_type_mask_loss": 0.92299, "laneline_len_loss": 9.67658, "laneline_field_loss": 2.69795, "laneline_view_loss": 8.89488, "laneline_inv_len_loss": 6.95423, "laneline_inv_field_loss": 6.55311, "roadside_len_loss": 9.55076, "roadside_field_loss": 1.73241, "roadside_view_loss": 4.91591, "roadside_inv_len_loss": 6.3613, "roadside_inv_field_loss": 2.88872, "depth_loss": 0.18576, "loss": 107.85393, "grad_norm": 518.61975, "time": 0.39469}
{"mode": "train", "epoch": 1, "iter": 5, "lr": 0.0, "memory": 6893, "data_time": 0.01235, "laneline_hm_loss": 4.58458, "roadside_hm_loss": 7.22878, "laneline_st_off_loss": 18.47882, "roadside_st_off_loss": 21.12621, "mask_loss": 1.4338, "lane_type_mask_loss": 1.30646, "lane_color_mask_loss": 0.59167, "side_type_mask_loss": 0.93023, "laneline_len_loss": 14.83643, "laneline_field_loss": 2.45816, "laneline_view_loss": 7.97706, "laneline_inv_len_loss": 11.08213, "laneline_inv_field_loss": 5.8637, "roadside_len_loss": 14.66368, "roadside_field_loss": 1.65811, "roadside_view_loss": 4.78015, "roadside_inv_len_loss": 10.02062, "roadside_inv_field_loss": 2.86507, "depth_loss": 0.14612, "loss": 132.0318, "grad_norm": 600.44873, "time": 0.40775}
{"mode": "train", "epoch": 1, "iter": 6, "lr": 0.0, "memory": 6893, "data_time": 0.0046, "laneline_hm_loss": 4.49789, "roadside_hm_loss": 7.15063, "laneline_st_off_loss": 13.87336, "roadside_st_off_loss": 16.94607, "mask_loss": 1.25165, "lane_type_mask_loss": 1.30352, "lane_color_mask_loss": 0.60497, "side_type_mask_loss": 0.90814, "laneline_len_loss": 11.9333, "laneline_field_loss": 2.28671, "laneline_view_loss": 6.84671, "laneline_inv_len_loss": 8.72563, "laneline_inv_field_loss": 5.19698, "roadside_len_loss": 11.69956, "roadside_field_loss": 1.51632, "roadside_view_loss": 4.1183, "roadside_inv_len_loss": 7.9912, "roadside_inv_field_loss": 2.56956, "depth_loss": 0.12886, "loss": 109.54935, "grad_norm": 470.81256, "time": 0.39909}
{"mode": "train", "epoch": 1, "iter": 7, "lr": 0.0, "memory": 6893, "data_time": 0.00778, "laneline_hm_loss": 5.88551, "roadside_hm_loss": 9.47398, "laneline_st_off_loss": 13.7004, "roadside_st_off_loss": 16.07, "mask_loss": 1.57381, "lane_type_mask_loss": 1.30469, "lane_color_mask_loss": 0.59879, "side_type_mask_loss": 0.92273, "laneline_len_loss": 16.18314, "laneline_field_loss": 2.27818, "laneline_view_loss": 7.41736, "laneline_inv_len_loss": 12.01561, "laneline_inv_field_loss": 5.5382, "roadside_len_loss": 15.93379, "roadside_field_loss": 1.66533, "roadside_view_loss": 4.90796, "roadside_inv_len_loss": 11.0606, "roadside_inv_field_loss": 2.99018, "depth_loss": 0.13961, "loss": 129.65988, "grad_norm": 610.64185, "time": 0.40098}
{"mode": "train", "epoch": 1, "iter": 8, "lr": 0.0, "memory": 6893, "data_time": 0.00607, "laneline_hm_loss": 4.2005, "roadside_hm_loss": 6.61315, "laneline_st_off_loss": 14.93253, "roadside_st_off_loss": 16.16867, "mask_loss": 1.18445, "lane_type_mask_loss": 1.31397, "lane_color_mask_loss": 0.59069, "side_type_mask_loss": 0.9259, "laneline_len_loss": 11.79968, "laneline_field_loss": 2.48882, "laneline_view_loss": 8.34619, "laneline_inv_len_loss": 8.65009, "laneline_inv_field_loss": 6.31714, "roadside_len_loss": 11.674, "roadside_field_loss": 1.64207, "roadside_view_loss": 4.911, "roadside_inv_len_loss": 7.90784, "roadside_inv_field_loss": 2.99258, "depth_loss": 0.17978, "loss": 112.83904, "grad_norm": 518.18555, "time": 0.41989}
{"mode": "train", "epoch": 1, "iter": 9, "lr": 0.0, "memory": 6893, "data_time": 0.00588, "laneline_hm_loss": 4.15107, "roadside_hm_loss": 6.63346, "laneline_st_off_loss": 13.96538, "roadside_st_off_loss": 17.81201, "mask_loss": 1.18763, "lane_type_mask_loss": 1.28463, "lane_color_mask_loss": 0.58947, "side_type_mask_loss": 0.91677, "laneline_len_loss": 11.89704, "laneline_field_loss": 2.62297, "laneline_view_loss": 7.79941, "laneline_inv_len_loss": 8.76278, "laneline_inv_field_loss": 5.73532, "roadside_len_loss": 11.68355, "roadside_field_loss": 1.80358, "roadside_view_loss": 4.56766, "roadside_inv_len_loss": 7.95929, "roadside_inv_field_loss": 2.68862, "depth_loss": 0.18864, "loss": 112.24929, "grad_norm": 419.35556, "time": 0.39736}
{"mode": "train", "epoch": 1, "iter": 10, "lr": 0.0, "memory": 6893, "data_time": 0.00643, "laneline_hm_loss": 5.29123, "roadside_hm_loss": 8.45636, "laneline_st_off_loss": 13.38927, "roadside_st_off_loss": 16.82143, "mask_loss": 1.41013, "lane_type_mask_loss": 1.2849, "lane_color_mask_loss": 0.60213, "side_type_mask_loss": 0.90458, "laneline_len_loss": 14.77864, "laneline_field_loss": 2.36089, "laneline_view_loss": 7.31572, "laneline_inv_len_loss": 10.93823, "laneline_inv_field_loss": 5.46514, "roadside_len_loss": 14.62147, "roadside_field_loss": 1.61035, "roadside_view_loss": 4.48912, "roadside_inv_len_loss": 10.02032, "roadside_inv_field_loss": 2.80777, "depth_loss": 0.10435, "loss": 122.67204, "grad_norm": 479.48279, "time": 0.39401}
{"mode": "train", "epoch": 1, "iter": 11, "lr": 0.0, "memory": 6893, "data_time": 0.00548, "laneline_hm_loss": 3.56126, "roadside_hm_loss": 5.45466, "laneline_st_off_loss": 15.92951, "roadside_st_off_loss": 17.79702, "mask_loss": 1.03124, "lane_type_mask_loss": 1.32068, "lane_color_mask_loss": 0.58662, "side_type_mask_loss": 0.88976, "laneline_len_loss": 10.07862, "laneline_field_loss": 2.8854, "laneline_view_loss": 9.79995, "laneline_inv_len_loss": 7.40461, "laneline_inv_field_loss": 7.23197, "roadside_len_loss": 9.9825, "roadside_field_loss": 1.81473, "roadside_view_loss": 5.27724, "roadside_inv_len_loss": 6.73021, "roadside_inv_field_loss": 3.1184, "depth_loss": 0.2161, "loss": 111.11049, "grad_norm": 426.86566, "time": 0.39199}
{"mode": "train", "epoch": 1, "iter": 12, "lr": 0.0, "memory": 6893, "data_time": 0.00533, "laneline_hm_loss": 3.23732, "roadside_hm_loss": 4.8573, "laneline_st_off_loss": 16.56504, "roadside_st_off_loss": 17.7149, "mask_loss": 0.89275, "lane_type_mask_loss": 1.31268, "lane_color_mask_loss": 0.58703, "side_type_mask_loss": 0.90697, "laneline_len_loss": 8.22195, "laneline_field_loss": 2.91767, "laneline_view_loss": 10.35591, "laneline_inv_len_loss": 5.95819, "laneline_inv_field_loss": 7.71848, "roadside_len_loss": 8.16524, "roadside_field_loss": 1.84516, "roadside_view_loss": 5.67947, "roadside_inv_len_loss": 5.4299, "roadside_inv_field_loss": 3.26651, "depth_loss": 0.24288, "loss": 105.87533, "grad_norm": 499.30304, "time": 0.39529}
{"mode": "train", "epoch": 1, "iter": 13, "lr": 0.0, "memory": 6893, "data_time": 0.00505, "laneline_hm_loss": 3.29489, "roadside_hm_loss": 4.98991, "laneline_st_off_loss": 16.06429, "roadside_st_off_loss": 16.28411, "mask_loss": 1.04526, "lane_type_mask_loss": 1.32065, "lane_color_mask_loss": 0.59503, "side_type_mask_loss": 0.90417, "laneline_len_loss": 10.2153, "laneline_field_loss": 2.62286, "laneline_view_loss": 8.74465, "laneline_inv_len_loss": 7.39748, "laneline_inv_field_loss": 6.56631, "roadside_len_loss": 9.98427, "roadside_field_loss": 1.66938, "roadside_view_loss": 4.86069, "roadside_inv_len_loss": 6.79945, "roadside_inv_field_loss": 2.89596, "depth_loss": 0.19229, "loss": 106.44696, "grad_norm": 437.27902, "time": 0.39176}
{"mode": "train", "epoch": 1, "iter": 14, "lr": 0.0, "memory": 6893, "data_time": 0.01524, "laneline_hm_loss": 3.64977, "roadside_hm_loss": 5.58598, "laneline_st_off_loss": 16.66995, "roadside_st_off_loss": 18.64601, "mask_loss": 1.01252, "lane_type_mask_loss": 1.33086, "lane_color_mask_loss": 0.58673, "side_type_mask_loss": 0.90135, "laneline_len_loss": 10.05303, "laneline_field_loss": 2.75954, "laneline_view_loss": 9.46045, "laneline_inv_len_loss": 7.38125, "laneline_inv_field_loss": 6.97183, "roadside_len_loss": 9.9633, "roadside_field_loss": 1.75481, "roadside_view_loss": 5.34605, "roadside_inv_len_loss": 6.70052, "roadside_inv_field_loss": 3.15374, "depth_loss": 0.17694, "loss": 112.10464, "grad_norm": 432.55981, "time": 0.41054}
{"mode": "train", "epoch": 1, "iter": 15, "lr": 0.0, "memory": 6893, "data_time": 0.00565, "laneline_hm_loss": 3.94157, "roadside_hm_loss": 6.02125, "laneline_st_off_loss": 17.24539, "roadside_st_off_loss": 17.62181, "mask_loss": 1.1011, "lane_type_mask_loss": 1.27998, "lane_color_mask_loss": 0.58831, "side_type_mask_loss": 0.91514, "laneline_len_loss": 11.11022, "laneline_field_loss": 2.62555, "laneline_view_loss": 7.77524, "laneline_inv_len_loss": 8.12906, "laneline_inv_field_loss": 5.63412, "roadside_len_loss": 11.02958, "roadside_field_loss": 1.69051, "roadside_view_loss": 4.2873, "roadside_inv_len_loss": 7.4497, "roadside_inv_field_loss": 2.54537, "depth_loss": 0.17024, "loss": 111.16145, "grad_norm": 443.25241, "time": 0.39814}
{"mode": "train", "epoch": 1, "iter": 16, "lr": 0.0, "memory": 6893, "data_time": 0.00537, "laneline_hm_loss": 3.93521, "roadside_hm_loss": 6.0907, "laneline_st_off_loss": 14.24429, "roadside_st_off_loss": 18.08996, "mask_loss": 1.06536, "lane_type_mask_loss": 1.30338, "lane_color_mask_loss": 0.57959, "side_type_mask_loss": 0.92, "laneline_len_loss": 10.16777, "laneline_field_loss": 2.55454, "laneline_view_loss": 8.19052, "laneline_inv_len_loss": 7.48337, "laneline_inv_field_loss": 6.26116, "roadside_len_loss": 10.1683, "roadside_field_loss": 1.64558, "roadside_view_loss": 4.73737, "roadside_inv_len_loss": 6.82962, "roadside_inv_field_loss": 2.94283, "depth_loss": 0.17157, "loss": 107.38113, "grad_norm": 449.79526, "time": 0.39977}
{"mode": "train", "epoch": 1, "iter": 17, "lr": 0.0, "memory": 6893, "data_time": 0.00699, "laneline_hm_loss": 4.66388, "roadside_hm_loss": 7.3099, "laneline_st_off_loss": 15.12463, "roadside_st_off_loss": 17.70142, "mask_loss": 1.23284, "lane_type_mask_loss": 1.29384, "lane_color_mask_loss": 0.57604, "side_type_mask_loss": 0.88225, "laneline_len_loss": 12.67699, "laneline_field_loss": 2.70947, "laneline_view_loss": 8.86982, "laneline_inv_len_loss": 9.36556, "laneline_inv_field_loss": 6.48374, "roadside_len_loss": 12.49343, "roadside_field_loss": 1.83575, "roadside_view_loss": 5.16198, "roadside_inv_len_loss": 8.63374, "roadside_inv_field_loss": 3.0528, "depth_loss": 0.14769, "loss": 120.21576, "grad_norm": 494.87308, "time": 0.41823}
{"mode": "train", "epoch": 1, "iter": 18, "lr": 0.0, "memory": 6893, "data_time": 0.0052, "laneline_hm_loss": 3.82361, "roadside_hm_loss": 5.92266, "laneline_st_off_loss": 15.42953, "roadside_st_off_loss": 15.71995, "mask_loss": 1.14503, "lane_type_mask_loss": 1.29704, "lane_color_mask_loss": 0.57867, "side_type_mask_loss": 0.88764, "laneline_len_loss": 11.65023, "laneline_field_loss": 2.7775, "laneline_view_loss": 9.06227, "laneline_inv_len_loss": 8.55263, "laneline_inv_field_loss": 6.40879, "roadside_len_loss": 11.53835, "roadside_field_loss": 1.78139, "roadside_view_loss": 4.93595, "roadside_inv_len_loss": 7.82407, "roadside_inv_field_loss": 2.80273, "depth_loss": 0.13018, "loss": 112.2682, "grad_norm": 414.23526, "time": 0.39636}
{"mode": "train", "epoch": 1, "iter": 19, "lr": 0.0, "memory": 6893, "data_time": 0.00571, "laneline_hm_loss": 3.14234, "roadside_hm_loss": 4.71325, "laneline_st_off_loss": 16.39196, "roadside_st_off_loss": 17.46638, "mask_loss": 0.93362, "lane_type_mask_loss": 1.32762, "lane_color_mask_loss": 0.58055, "side_type_mask_loss": 0.86552, "laneline_len_loss": 8.73807, "laneline_field_loss": 2.81831, "laneline_view_loss": 9.49569, "laneline_inv_len_loss": 6.31771, "laneline_inv_field_loss": 7.24027, "roadside_len_loss": 8.62162, "roadside_field_loss": 1.7677, "roadside_view_loss": 5.24593, "roadside_inv_len_loss": 5.76242, "roadside_inv_field_loss": 3.12799, "depth_loss": 0.22743, "loss": 104.78439, "grad_norm": 429.57803, "time": 0.39867}
{"mode": "train", "epoch": 1, "iter": 20, "lr": 0.0, "memory": 6893, "data_time": 0.01515, "laneline_hm_loss": 2.85708, "roadside_hm_loss": 4.20228, "laneline_st_off_loss": 17.34444, "roadside_st_off_loss": 18.06752, "mask_loss": 0.86421, "lane_type_mask_loss": 1.30501, "lane_color_mask_loss": 0.57747, "side_type_mask_loss": 0.88783, "laneline_len_loss": 7.74875, "laneline_field_loss": 3.05011, "laneline_view_loss": 10.44662, "laneline_inv_len_loss": 5.61477, "laneline_inv_field_loss": 7.84167, "roadside_len_loss": 7.69902, "roadside_field_loss": 1.91768, "roadside_view_loss": 5.58709, "roadside_inv_len_loss": 5.08714, "roadside_inv_field_loss": 3.28585, "depth_loss": 0.24199, "loss": 104.62653, "grad_norm": 397.79468, "time": 0.40898}
{"mode": "train", "epoch": 1, "iter": 21, "lr": 0.0, "memory": 6893, "data_time": 0.00593, "laneline_hm_loss": 3.03532, "roadside_hm_loss": 4.59601, "laneline_st_off_loss": 15.8986, "roadside_st_off_loss": 17.53983, "mask_loss": 0.84891, "lane_type_mask_loss": 1.29127, "lane_color_mask_loss": 0.58885, "side_type_mask_loss": 0.87356, "laneline_len_loss": 7.54196, "laneline_field_loss": 2.76555, "laneline_view_loss": 9.49607, "laneline_inv_len_loss": 5.46521, "laneline_inv_field_loss": 7.22518, "roadside_len_loss": 7.52742, "roadside_field_loss": 1.74636, "roadside_view_loss": 5.32472, "roadside_inv_len_loss": 4.94988, "roadside_inv_field_loss": 3.19058, "depth_loss": 0.23643, "loss": 100.14172, "grad_norm": 436.81635, "time": 0.40185}
{"mode": "train", "epoch": 1, "iter": 22, "lr": 0.0, "memory": 6893, "data_time": 0.02014, "laneline_hm_loss": 3.40462, "roadside_hm_loss": 5.04779, "laneline_st_off_loss": 15.28563, "roadside_st_off_loss": 16.34303, "mask_loss": 0.95201, "lane_type_mask_loss": 1.28516, "lane_color_mask_loss": 0.57081, "side_type_mask_loss": 0.88243, "laneline_len_loss": 9.07453, "laneline_field_loss": 2.67259, "laneline_view_loss": 8.89664, "laneline_inv_len_loss": 6.6239, "laneline_inv_field_loss": 6.62037, "roadside_len_loss": 8.95032, "roadside_field_loss": 1.71927, "roadside_view_loss": 4.97746, "roadside_inv_len_loss": 6.06769, "roadside_inv_field_loss": 2.94006, "depth_loss": 0.20072, "loss": 102.51501, "grad_norm": 404.44794, "time": 0.42974}
{"mode": "train", "epoch": 1, "iter": 23, "lr": 0.0, "memory": 6893, "data_time": 0.00661, "laneline_hm_loss": 3.92469, "roadside_hm_loss": 6.12056, "laneline_st_off_loss": 15.29612, "roadside_st_off_loss": 17.52788, "mask_loss": 1.02453, "lane_type_mask_loss": 1.28619, "lane_color_mask_loss": 0.56855, "side_type_mask_loss": 0.86805, "laneline_len_loss": 10.05881, "laneline_field_loss": 2.64461, "laneline_view_loss": 8.13591, "laneline_inv_len_loss": 7.34896, "laneline_inv_field_loss": 5.96143, "roadside_len_loss": 10.0297, "roadside_field_loss": 1.73392, "roadside_view_loss": 4.70676, "roadside_inv_len_loss": 6.68631, "roadside_inv_field_loss": 2.76565, "depth_loss": 0.19983, "loss": 106.88845, "grad_norm": 428.22311, "time": 0.41025}
{"mode": "train", "epoch": 1, "iter": 24, "lr": 0.0, "memory": 6893, "data_time": 0.00713, "laneline_hm_loss": 4.51059, "roadside_hm_loss": 7.14921, "laneline_st_off_loss": 14.19796, "roadside_st_off_loss": 14.38108, "mask_loss": 1.17069, "lane_type_mask_loss": 1.25106, "lane_color_mask_loss": 0.57766, "side_type_mask_loss": 0.86716, "laneline_len_loss": 11.88601, "laneline_field_loss": 2.61744, "laneline_view_loss": 8.02134, "laneline_inv_len_loss": 8.84555, "laneline_inv_field_loss": 5.94532, "roadside_len_loss": 11.74994, "roadside_field_loss": 1.7873, "roadside_view_loss": 4.77172, "roadside_inv_len_loss": 8.084, "roadside_inv_field_loss": 2.89684, "depth_loss": 0.15973, "loss": 110.8706, "grad_norm": 447.85587, "time": 0.40837}
{"mode": "train", "epoch": 1, "iter": 25, "lr": 0.0, "memory": 6893, "data_time": 0.00696, "laneline_hm_loss": 3.38078, "roadside_hm_loss": 5.09648, "laneline_st_off_loss": 15.28225, "roadside_st_off_loss": 15.61179, "mask_loss": 0.93343, "lane_type_mask_loss": 1.25651, "lane_color_mask_loss": 0.5565, "side_type_mask_loss": 0.87388, "laneline_len_loss": 9.25277, "laneline_field_loss": 2.86087, "laneline_view_loss": 9.90636, "laneline_inv_len_loss": 6.80485, "laneline_inv_field_loss": 7.29231, "roadside_len_loss": 9.19748, "roadside_field_loss": 1.83449, "roadside_view_loss": 5.43574, "roadside_inv_len_loss": 6.1745, "roadside_inv_field_loss": 3.1585, "depth_loss": 0.27709, "loss": 105.18658, "grad_norm": 386.33054, "time": 0.40488}
{"mode": "train", "epoch": 1, "iter": 26, "lr": 0.0, "memory": 6893, "data_time": 0.00665, "laneline_hm_loss": 3.67098, "roadside_hm_loss": 5.44862, "laneline_st_off_loss": 14.02242, "roadside_st_off_loss": 16.69426, "mask_loss": 0.97561, "lane_type_mask_loss": 1.24805, "lane_color_mask_loss": 0.56096, "side_type_mask_loss": 0.86682, "laneline_len_loss": 9.64095, "laneline_field_loss": 2.54367, "laneline_view_loss": 8.07886, "laneline_inv_len_loss": 7.14136, "laneline_inv_field_loss": 6.07555, "roadside_len_loss": 9.56673, "roadside_field_loss": 1.67403, "roadside_view_loss": 4.58499, "roadside_inv_len_loss": 6.48576, "roadside_inv_field_loss": 2.78876, "depth_loss": 0.17506, "loss": 102.24344, "grad_norm": 462.72073, "time": 0.42684}
{"mode": "train", "epoch": 1, "iter": 27, "lr": 0.0, "memory": 6893, "data_time": 0.00733, "laneline_hm_loss": 3.62609, "roadside_hm_loss": 5.49842, "laneline_st_off_loss": 18.43645, "roadside_st_off_loss": 18.81677, "mask_loss": 0.96851, "lane_type_mask_loss": 1.29165, "lane_color_mask_loss": 0.56005, "side_type_mask_loss": 0.84796, "laneline_len_loss": 9.31232, "laneline_field_loss": 2.71476, "laneline_view_loss": 9.65972, "laneline_inv_len_loss": 6.89136, "laneline_inv_field_loss": 7.29185, "roadside_len_loss": 9.25184, "roadside_field_loss": 1.77673, "roadside_view_loss": 5.64401, "roadside_inv_len_loss": 6.2394, "roadside_inv_field_loss": 3.36519, "depth_loss": 0.23313, "loss": 112.42621, "grad_norm": 480.22839, "time": 0.40744}
{"mode": "train", "epoch": 1, "iter": 28, "lr": 0.0, "memory": 6893, "data_time": 0.00678, "laneline_hm_loss": 3.12188, "roadside_hm_loss": 4.59949, "laneline_st_off_loss": 12.98445, "roadside_st_off_loss": 18.64153, "mask_loss": 0.87665, "lane_type_mask_loss": 1.2555, "lane_color_mask_loss": 0.54447, "side_type_mask_loss": 0.84722, "laneline_len_loss": 8.43521, "laneline_field_loss": 3.17959, "laneline_view_loss": 10.53941, "laneline_inv_len_loss": 6.23268, "laneline_inv_field_loss": 7.65471, "roadside_len_loss": 8.44529, "roadside_field_loss": 2.01743, "roadside_view_loss": 5.82198, "roadside_inv_len_loss": 5.70394, "roadside_inv_field_loss": 3.30038, "depth_loss": 0.18217, "loss": 104.38399, "grad_norm": 494.38934, "time": 0.40123}
{"mode": "train", "epoch": 1, "iter": 29, "lr": 0.0, "memory": 6893, "data_time": 0.00535, "laneline_hm_loss": 3.40065, "roadside_hm_loss": 5.2282, "laneline_st_off_loss": 11.81771, "roadside_st_off_loss": 14.33523, "mask_loss": 0.87175, "lane_type_mask_loss": 1.2565, "lane_color_mask_loss": 0.55996, "side_type_mask_loss": 0.84715, "laneline_len_loss": 8.6235, "laneline_field_loss": 2.61298, "laneline_view_loss": 8.4719, "laneline_inv_len_loss": 6.28663, "laneline_inv_field_loss": 6.39806, "roadside_len_loss": 8.57687, "roadside_field_loss": 1.61652, "roadside_view_loss": 4.58429, "roadside_inv_len_loss": 5.69696, "roadside_inv_field_loss": 2.83003, "depth_loss": 0.17251, "loss": 94.18739, "grad_norm": 358.17105, "time": 0.39656}
{"mode": "train", "epoch": 1, "iter": 30, "lr": 0.0, "memory": 6893, "data_time": 0.00574, "laneline_hm_loss": 3.51176, "roadside_hm_loss": 5.4143, "laneline_st_off_loss": 13.3888, "roadside_st_off_loss": 14.69495, "mask_loss": 0.96379, "lane_type_mask_loss": 1.25057, "lane_color_mask_loss": 0.56483, "side_type_mask_loss": 0.8348, "laneline_len_loss": 9.53388, "laneline_field_loss": 2.63119, "laneline_view_loss": 8.67302, "laneline_inv_len_loss": 7.11952, "laneline_inv_field_loss": 6.48613, "roadside_len_loss": 9.47661, "roadside_field_loss": 1.73548, "roadside_view_loss": 4.92589, "roadside_inv_len_loss": 6.46044, "roadside_inv_field_loss": 2.94039, "depth_loss": 0.15629, "loss": 100.76265, "grad_norm": 374.91873, "time": 0.39595}
{"mode": "train", "epoch": 1, "iter": 31, "lr": 0.0, "memory": 6893, "data_time": 0.00553, "laneline_hm_loss": 4.03343, "roadside_hm_loss": 6.28647, "laneline_st_off_loss": 13.19765, "roadside_st_off_loss": 16.5328, "mask_loss": 0.95778, "lane_type_mask_loss": 1.24082, "lane_color_mask_loss": 0.54483, "side_type_mask_loss": 0.83749, "laneline_len_loss": 10.2381, "laneline_field_loss": 2.65974, "laneline_view_loss": 8.90657, "laneline_inv_len_loss": 7.65549, "laneline_inv_field_loss": 6.62092, "roadside_len_loss": 10.22333, "roadside_field_loss": 1.72627, "roadside_view_loss": 5.16192, "roadside_inv_len_loss": 6.89148, "roadside_inv_field_loss": 3.10278, "depth_loss": 0.15903, "loss": 106.97688, "grad_norm": 426.76294, "time": 0.39222}
{"mode": "train", "epoch": 1, "iter": 32, "lr": 0.0, "memory": 6893, "data_time": 0.00567, "laneline_hm_loss": 3.56678, "roadside_hm_loss": 5.37181, "laneline_st_off_loss": 13.54048, "roadside_st_off_loss": 17.30758, "mask_loss": 0.9666, "lane_type_mask_loss": 1.25635, "lane_color_mask_loss": 0.55338, "side_type_mask_loss": 0.82296, "laneline_len_loss": 9.9587, "laneline_field_loss": 2.79918, "laneline_view_loss": 9.54338, "laneline_inv_len_loss": 7.41608, "laneline_inv_field_loss": 6.94387, "roadside_len_loss": 9.85499, "roadside_field_loss": 1.73508, "roadside_view_loss": 5.24079, "roadside_inv_len_loss": 6.78504, "roadside_inv_field_loss": 3.05076, "depth_loss": 0.17736, "loss": 106.89117, "grad_norm": 416.95609, "time": 0.39907}
{"mode": "train", "epoch": 1, "iter": 33, "lr": 0.0, "memory": 6893, "data_time": 0.0053, "laneline_hm_loss": 4.07622, "roadside_hm_loss": 6.24714, "laneline_st_off_loss": 13.02976, "roadside_st_off_loss": 14.24341, "mask_loss": 1.00936, "lane_type_mask_loss": 1.21843, "lane_color_mask_loss": 0.54684, "side_type_mask_loss": 0.84998, "laneline_len_loss": 10.40836, "laneline_field_loss": 2.49124, "laneline_view_loss": 7.93047, "laneline_inv_len_loss": 7.77395, "laneline_inv_field_loss": 6.00685, "roadside_len_loss": 10.41443, "roadside_field_loss": 1.63839, "roadside_view_loss": 4.58301, "roadside_inv_len_loss": 7.05651, "roadside_inv_field_loss": 2.84517, "depth_loss": 0.18224, "loss": 102.55174, "grad_norm": 362.68338, "time": 0.39711}
{"mode": "train", "epoch": 1, "iter": 34, "lr": 0.0, "memory": 6893, "data_time": 0.00551, "laneline_hm_loss": 2.72099, "roadside_hm_loss": 4.039, "laneline_st_off_loss": 13.89518, "roadside_st_off_loss": 15.77209, "mask_loss": 0.76698, "lane_type_mask_loss": 1.22755, "lane_color_mask_loss": 0.55607, "side_type_mask_loss": 0.82509, "laneline_len_loss": 7.64193, "laneline_field_loss": 3.36839, "laneline_view_loss": 11.04595, "laneline_inv_len_loss": 5.68226, "laneline_inv_field_loss": 8.16636, "roadside_len_loss": 7.66807, "roadside_field_loss": 2.03381, "roadside_view_loss": 5.60313, "roadside_inv_len_loss": 5.16424, "roadside_inv_field_loss": 3.25579, "depth_loss": 0.26166, "loss": 99.69455, "grad_norm": 367.61221, "time": 0.39088}
{"mode": "train", "epoch": 1, "iter": 35, "lr": 0.0, "memory": 6893, "data_time": 0.00573, "laneline_hm_loss": 3.07784, "roadside_hm_loss": 4.57042, "laneline_st_off_loss": 13.83405, "roadside_st_off_loss": 15.47406, "mask_loss": 0.84481, "lane_type_mask_loss": 1.2358, "lane_color_mask_loss": 0.53493, "side_type_mask_loss": 0.83277, "laneline_len_loss": 8.6432, "laneline_field_loss": 2.75872, "laneline_view_loss": 8.97073, "laneline_inv_len_loss": 6.38151, "laneline_inv_field_loss": 6.58407, "roadside_len_loss": 8.57032, "roadside_field_loss": 1.70524, "roadside_view_loss": 4.74477, "roadside_inv_len_loss": 5.77856, "roadside_inv_field_loss": 2.75596, "depth_loss": 0.12589, "loss": 97.42363, "grad_norm": 385.91782, "time": 0.39559}
{"mode": "train", "epoch": 1, "iter": 36, "lr": 0.0, "memory": 6893, "data_time": 0.00554, "laneline_hm_loss": 3.75453, "roadside_hm_loss": 5.77102, "laneline_st_off_loss": 13.89812, "roadside_st_off_loss": 13.17692, "mask_loss": 0.94168, "lane_type_mask_loss": 1.21925, "lane_color_mask_loss": 0.54801, "side_type_mask_loss": 0.81321, "laneline_len_loss": 9.56293, "laneline_field_loss": 2.67522, "laneline_view_loss": 8.38363, "laneline_inv_len_loss": 7.24658, "laneline_inv_field_loss": 6.16027, "roadside_len_loss": 9.60862, "roadside_field_loss": 1.74614, "roadside_view_loss": 4.64507, "roadside_inv_len_loss": 6.43809, "roadside_inv_field_loss": 2.77644, "depth_loss": 0.14407, "loss": 99.5098, "grad_norm": 366.58096, "time": 0.40233}
{"mode": "train", "epoch": 1, "iter": 37, "lr": 0.0, "memory": 6893, "data_time": 0.006, "laneline_hm_loss": 2.96566, "roadside_hm_loss": 4.36239, "laneline_st_off_loss": 13.5178, "roadside_st_off_loss": 14.87221, "mask_loss": 0.78469, "lane_type_mask_loss": 1.2221, "lane_color_mask_loss": 0.52666, "side_type_mask_loss": 0.83126, "laneline_len_loss": 7.73886, "laneline_field_loss": 2.78885, "laneline_view_loss": 9.57536, "laneline_inv_len_loss": 5.76341, "laneline_inv_field_loss": 7.30473, "roadside_len_loss": 7.75944, "roadside_field_loss": 1.70662, "roadside_view_loss": 5.15188, "roadside_inv_len_loss": 5.17527, "roadside_inv_field_loss": 3.12685, "depth_loss": 0.27949, "loss": 95.45354, "grad_norm": 342.35941, "time": 0.40182}
{"mode": "train", "epoch": 1, "iter": 38, "lr": 0.0, "memory": 6893, "data_time": 0.00593, "laneline_hm_loss": 4.00896, "roadside_hm_loss": 6.12594, "laneline_st_off_loss": 11.83221, "roadside_st_off_loss": 14.11095, "mask_loss": 1.02388, "lane_type_mask_loss": 1.18802, "lane_color_mask_loss": 0.54203, "side_type_mask_loss": 0.81462, "laneline_len_loss": 10.9989, "laneline_field_loss": 2.34411, "laneline_view_loss": 7.16261, "laneline_inv_len_loss": 8.40065, "laneline_inv_field_loss": 5.41932, "roadside_len_loss": 11.04903, "roadside_field_loss": 1.56348, "roadside_view_loss": 4.17416, "roadside_inv_len_loss": 7.56729, "roadside_inv_field_loss": 2.60598, "depth_loss": 0.10753, "loss": 101.03966, "grad_norm": 355.82791, "time": 0.39726}
{"mode": "train", "epoch": 1, "iter": 39, "lr": 0.0, "memory": 6893, "data_time": 0.00768, "laneline_hm_loss": 4.42191, "roadside_hm_loss": 6.86674, "laneline_st_off_loss": 11.47024, "roadside_st_off_loss": 13.35846, "mask_loss": 1.04543, "lane_type_mask_loss": 1.1967, "lane_color_mask_loss": 0.53946, "side_type_mask_loss": 0.79454, "laneline_len_loss": 11.29434, "laneline_field_loss": 2.30059, "laneline_view_loss": 7.27279, "laneline_inv_len_loss": 8.69284, "laneline_inv_field_loss": 5.3435, "roadside_len_loss": 11.39832, "roadside_field_loss": 1.64161, "roadside_view_loss": 4.57428, "roadside_inv_len_loss": 7.74808, "roadside_inv_field_loss": 2.74473, "depth_loss": 0.14172, "loss": 102.84627, "grad_norm": 368.25012, "time": 0.40238}
{"mode": "train", "epoch": 1, "iter": 40, "lr": 0.0, "memory": 6893, "data_time": 0.00569, "laneline_hm_loss": 4.44454, "roadside_hm_loss": 6.93113, "laneline_st_off_loss": 12.10191, "roadside_st_off_loss": 13.86479, "mask_loss": 1.19919, "lane_type_mask_loss": 1.17584, "lane_color_mask_loss": 0.51595, "side_type_mask_loss": 0.81651, "laneline_len_loss": 13.38881, "laneline_field_loss": 2.29344, "laneline_view_loss": 6.38943, "laneline_inv_len_loss": 10.31872, "laneline_inv_field_loss": 4.64879, "roadside_len_loss": 13.43945, "roadside_field_loss": 1.62785, "roadside_view_loss": 3.85834, "roadside_inv_len_loss": 9.27898, "roadside_inv_field_loss": 2.31855, "depth_loss": 0.10306, "loss": 108.71531, "grad_norm": 403.68912, "time": 0.39759}
{"mode": "train", "epoch": 1, "iter": 41, "lr": 0.0, "memory": 6893, "data_time": 0.00687, "laneline_hm_loss": 3.71216, "roadside_hm_loss": 5.44413, "laneline_st_off_loss": 14.04592, "roadside_st_off_loss": 15.26579, "mask_loss": 0.96512, "lane_type_mask_loss": 1.21461, "lane_color_mask_loss": 0.50411, "side_type_mask_loss": 0.81023, "laneline_len_loss": 9.71004, "laneline_field_loss": 2.6832, "laneline_view_loss": 9.01737, "laneline_inv_len_loss": 7.4621, "laneline_inv_field_loss": 6.66647, "roadside_len_loss": 9.78566, "roadside_field_loss": 1.73266, "roadside_view_loss": 5.14616, "roadside_inv_len_loss": 6.67618, "roadside_inv_field_loss": 3.06197, "depth_loss": 0.16283, "loss": 104.06671, "grad_norm": 370.25333, "time": 0.40086}
{"mode": "train", "epoch": 1, "iter": 42, "lr": 0.0, "memory": 6893, "data_time": 0.00596, "laneline_hm_loss": 3.64745, "roadside_hm_loss": 5.52351, "laneline_st_off_loss": 13.47555, "roadside_st_off_loss": 14.67443, "mask_loss": 0.89996, "lane_type_mask_loss": 1.20731, "lane_color_mask_loss": 0.51001, "side_type_mask_loss": 0.79174, "laneline_len_loss": 9.32009, "laneline_field_loss": 2.7859, "laneline_view_loss": 8.78403, "laneline_inv_len_loss": 7.00989, "laneline_inv_field_loss": 6.26746, "roadside_len_loss": 9.24972, "roadside_field_loss": 1.82582, "roadside_view_loss": 4.95918, "roadside_inv_len_loss": 6.26398, "roadside_inv_field_loss": 2.85092, "depth_loss": 0.17839, "loss": 100.22533, "grad_norm": 405.86603, "time": 0.3931}
{"mode": "train", "epoch": 1, "iter": 43, "lr": 0.0, "memory": 6893, "data_time": 0.00523, "laneline_hm_loss": 3.82502, "roadside_hm_loss": 5.83399, "laneline_st_off_loss": 13.35177, "roadside_st_off_loss": 15.04281, "mask_loss": 0.97228, "lane_type_mask_loss": 1.16625, "lane_color_mask_loss": 0.51079, "side_type_mask_loss": 0.80705, "laneline_len_loss": 11.4814, "laneline_field_loss": 3.28404, "laneline_view_loss": 10.77484, "laneline_inv_len_loss": 8.86354, "laneline_inv_field_loss": 7.13157, "roadside_len_loss": 11.50262, "roadside_field_loss": 2.08126, "roadside_view_loss": 5.75136, "roadside_inv_len_loss": 7.89312, "roadside_inv_field_loss": 3.0206, "depth_loss": 0.15221, "loss": 113.44651, "grad_norm": 404.53867, "time": 0.40145}
{"mode": "train", "epoch": 1, "iter": 44, "lr": 0.0, "memory": 6893, "data_time": 0.00525, "laneline_hm_loss": 3.18169, "roadside_hm_loss": 4.54712, "laneline_st_off_loss": 15.30292, "roadside_st_off_loss": 15.85031, "mask_loss": 0.85282, "lane_type_mask_loss": 1.18961, "lane_color_mask_loss": 0.50328, "side_type_mask_loss": 0.77297, "laneline_len_loss": 8.26769, "laneline_field_loss": 2.7167, "laneline_view_loss": 9.06141, "laneline_inv_len_loss": 6.30191, "laneline_inv_field_loss": 6.66809, "roadside_len_loss": 8.37963, "roadside_field_loss": 1.71854, "roadside_view_loss": 4.97178, "roadside_inv_len_loss": 5.60225, "roadside_inv_field_loss": 2.9352, "depth_loss": 0.17482, "loss": 98.99876, "grad_norm": 333.55109, "time": 0.39749}
{"mode": "train", "epoch": 1, "iter": 45, "lr": 0.0, "memory": 6893, "data_time": 0.00588, "laneline_hm_loss": 3.33477, "roadside_hm_loss": 4.91752, "laneline_st_off_loss": 13.36976, "roadside_st_off_loss": 15.30417, "mask_loss": 0.83668, "lane_type_mask_loss": 1.19707, "lane_color_mask_loss": 0.50374, "side_type_mask_loss": 0.76958, "laneline_len_loss": 8.46706, "laneline_field_loss": 2.65956, "laneline_view_loss": 8.76714, "laneline_inv_len_loss": 6.52088, "laneline_inv_field_loss": 6.50571, "roadside_len_loss": 8.62992, "roadside_field_loss": 1.71529, "roadside_view_loss": 4.89108, "roadside_inv_len_loss": 5.79902, "roadside_inv_field_loss": 2.90043, "depth_loss": 0.21526, "loss": 97.30464, "grad_norm": 340.36038, "time": 0.38966}
{"mode": "train", "epoch": 1, "iter": 46, "lr": 0.0, "memory": 6893, "data_time": 0.01131, "laneline_hm_loss": 4.31015, "roadside_hm_loss": 6.56136, "laneline_st_off_loss": 11.72087, "roadside_st_off_loss": 13.33196, "mask_loss": 1.08092, "lane_type_mask_loss": 1.17781, "lane_color_mask_loss": 0.52271, "side_type_mask_loss": 0.76132, "laneline_len_loss": 11.40541, "laneline_field_loss": 2.28458, "laneline_view_loss": 6.74769, "laneline_inv_len_loss": 8.9255, "laneline_inv_field_loss": 5.04275, "roadside_len_loss": 11.5302, "roadside_field_loss": 1.57402, "roadside_view_loss": 4.05481, "roadside_inv_len_loss": 7.92938, "roadside_inv_field_loss": 2.53174, "depth_loss": 0.12103, "loss": 101.6142, "grad_norm": 382.9155, "time": 0.40249}
{"mode": "train", "epoch": 1, "iter": 47, "lr": 0.0, "memory": 6893, "data_time": 0.00556, "laneline_hm_loss": 3.77426, "roadside_hm_loss": 5.58183, "laneline_st_off_loss": 13.58848, "roadside_st_off_loss": 13.41219, "mask_loss": 0.91164, "lane_type_mask_loss": 1.16769, "lane_color_mask_loss": 0.5038, "side_type_mask_loss": 0.75434, "laneline_len_loss": 9.21645, "laneline_field_loss": 2.30156, "laneline_view_loss": 7.38857, "laneline_inv_len_loss": 7.23087, "laneline_inv_field_loss": 5.54598, "roadside_len_loss": 9.40046, "roadside_field_loss": 1.58484, "roadside_view_loss": 4.50919, "roadside_inv_len_loss": 6.36419, "roadside_inv_field_loss": 2.69807, "depth_loss": 0.17405, "loss": 96.10844, "grad_norm": 371.58115, "time": 0.40339}
{"mode": "train", "epoch": 1, "iter": 48, "lr": 0.0, "memory": 6893, "data_time": 0.00631, "laneline_hm_loss": 3.18858, "roadside_hm_loss": 4.5763, "laneline_st_off_loss": 12.48852, "roadside_st_off_loss": 13.53577, "mask_loss": 0.78927, "lane_type_mask_loss": 1.13771, "lane_color_mask_loss": 0.49286, "side_type_mask_loss": 0.76906, "laneline_len_loss": 7.86273, "laneline_field_loss": 2.64577, "laneline_view_loss": 8.94365, "laneline_inv_len_loss": 6.11018, "laneline_inv_field_loss": 6.60575, "roadside_len_loss": 7.98442, "roadside_field_loss": 1.73333, "roadside_view_loss": 5.17776, "roadside_inv_len_loss": 5.39319, "roadside_inv_field_loss": 2.9946, "depth_loss": 0.19928, "loss": 92.6287, "grad_norm": 344.24588, "time": 0.39257}
{"mode": "train", "epoch": 1, "iter": 49, "lr": 0.0, "memory": 6893, "data_time": 0.01415, "laneline_hm_loss": 4.31742, "roadside_hm_loss": 6.37962, "laneline_st_off_loss": 12.54777, "roadside_st_off_loss": 14.60716, "mask_loss": 1.07375, "lane_type_mask_loss": 1.12578, "lane_color_mask_loss": 0.47201, "side_type_mask_loss": 0.76992, "laneline_len_loss": 12.84291, "laneline_field_loss": 2.47772, "laneline_view_loss": 8.43088, "laneline_inv_len_loss": 10.153, "laneline_inv_field_loss": 6.11839, "roadside_len_loss": 12.89082, "roadside_field_loss": 1.75419, "roadside_view_loss": 5.2565, "roadside_inv_len_loss": 9.00349, "roadside_inv_field_loss": 3.1034, "depth_loss": 0.13595, "loss": 113.46067, "grad_norm": 466.02365, "time": 0.40752}
{"mode": "train", "epoch": 1, "iter": 50, "lr": 0.0, "memory": 6893, "data_time": 0.00576, "laneline_hm_loss": 2.70116, "roadside_hm_loss": 3.80824, "laneline_st_off_loss": 13.89165, "roadside_st_off_loss": 14.03325, "mask_loss": 0.71815, "lane_type_mask_loss": 1.14518, "lane_color_mask_loss": 0.48445, "side_type_mask_loss": 0.74726, "laneline_len_loss": 6.92155, "laneline_field_loss": 3.03096, "laneline_view_loss": 10.37725, "laneline_inv_len_loss": 5.38217, "laneline_inv_field_loss": 7.55134, "roadside_len_loss": 7.04559, "roadside_field_loss": 1.9028, "roadside_view_loss": 5.56221, "roadside_inv_len_loss": 4.77515, "roadside_inv_field_loss": 3.11636, "depth_loss": 0.28015, "loss": 93.47487, "grad_norm": 298.09509, "time": 0.3907}
{"mode": "train", "epoch": 1, "iter": 51, "lr": 0.0, "memory": 6893, "data_time": 0.0127, "laneline_hm_loss": 3.07627, "roadside_hm_loss": 4.36899, "laneline_st_off_loss": 12.61201, "roadside_st_off_loss": 14.92031, "mask_loss": 0.77923, "lane_type_mask_loss": 1.15351, "lane_color_mask_loss": 0.48668, "side_type_mask_loss": 0.73248, "laneline_len_loss": 7.50247, "laneline_field_loss": 2.72968, "laneline_view_loss": 9.03118, "laneline_inv_len_loss": 5.84245, "laneline_inv_field_loss": 6.63046, "roadside_len_loss": 7.66728, "roadside_field_loss": 1.7175, "roadside_view_loss": 5.03011, "roadside_inv_len_loss": 5.17008, "roadside_inv_field_loss": 2.92828, "depth_loss": 0.25905, "loss": 92.63802, "grad_norm": 357.85199, "time": 0.40805}
{"mode": "train", "epoch": 1, "iter": 52, "lr": 0.0, "memory": 6893, "data_time": 0.00549, "laneline_hm_loss": 2.71846, "roadside_hm_loss": 3.75883, "laneline_st_off_loss": 12.40848, "roadside_st_off_loss": 14.70196, "mask_loss": 0.69727, "lane_type_mask_loss": 1.13621, "lane_color_mask_loss": 0.46821, "side_type_mask_loss": 0.74281, "laneline_len_loss": 6.60939, "laneline_field_loss": 2.8704, "laneline_view_loss": 10.40893, "laneline_inv_len_loss": 5.16217, "laneline_inv_field_loss": 7.92487, "roadside_len_loss": 6.71331, "roadside_field_loss": 1.78096, "roadside_view_loss": 5.74108, "roadside_inv_len_loss": 4.53207, "roadside_inv_field_loss": 3.4031, "depth_loss": 0.21874, "loss": 91.99724, "grad_norm": 329.04352, "time": 0.39131}
{"mode": "train", "epoch": 1, "iter": 53, "lr": 0.0, "memory": 6893, "data_time": 0.0053, "laneline_hm_loss": 3.39811, "roadside_hm_loss": 4.76814, "laneline_st_off_loss": 13.44648, "roadside_st_off_loss": 15.71557, "mask_loss": 0.86661, "lane_type_mask_loss": 1.10249, "lane_color_mask_loss": 0.47219, "side_type_mask_loss": 0.74172, "laneline_len_loss": 9.23042, "laneline_field_loss": 2.79197, "laneline_view_loss": 9.63394, "laneline_inv_len_loss": 7.37315, "laneline_inv_field_loss": 7.0251, "roadside_len_loss": 9.43676, "roadside_field_loss": 1.8305, "roadside_view_loss": 5.24431, "roadside_inv_len_loss": 6.37285, "roadside_inv_field_loss": 3.05893, "depth_loss": 0.15788, "loss": 102.66712, "grad_norm": 352.07806, "time": 0.39268}
{"mode": "train", "epoch": 1, "iter": 54, "lr": 0.0, "memory": 6893, "data_time": 0.00547, "laneline_hm_loss": 3.12143, "roadside_hm_loss": 4.60012, "laneline_st_off_loss": 11.5109, "roadside_st_off_loss": 12.79459, "mask_loss": 0.72454, "lane_type_mask_loss": 1.11225, "lane_color_mask_loss": 0.48775, "side_type_mask_loss": 0.72109, "laneline_len_loss": 7.20339, "laneline_field_loss": 2.71654, "laneline_view_loss": 9.39439, "laneline_inv_len_loss": 5.6817, "laneline_inv_field_loss": 6.89204, "roadside_len_loss": 7.31916, "roadside_field_loss": 1.76406, "roadside_view_loss": 5.36958, "roadside_inv_len_loss": 5.02926, "roadside_inv_field_loss": 3.12149, "depth_loss": 0.26723, "loss": 89.83153, "grad_norm": 317.18073, "time": 0.39807}
{"mode": "train", "epoch": 1, "iter": 55, "lr": 0.0, "memory": 6893, "data_time": 0.00537, "laneline_hm_loss": 3.25522, "roadside_hm_loss": 4.62416, "laneline_st_off_loss": 13.04491, "roadside_st_off_loss": 15.05158, "mask_loss": 0.7722, "lane_type_mask_loss": 1.1004, "lane_color_mask_loss": 0.46877, "side_type_mask_loss": 0.72083, "laneline_len_loss": 8.29973, "laneline_field_loss": 2.70086, "laneline_view_loss": 8.63849, "laneline_inv_len_loss": 6.5415, "laneline_inv_field_loss": 6.37232, "roadside_len_loss": 8.45907, "roadside_field_loss": 1.70134, "roadside_view_loss": 4.64395, "roadside_inv_len_loss": 5.67248, "roadside_inv_field_loss": 2.76102, "depth_loss": 0.17163, "loss": 95.00045, "grad_norm": 380.39813, "time": 0.39087}
{"mode": "train", "epoch": 1, "iter": 56, "lr": 0.0, "memory": 6893, "data_time": 0.01309, "laneline_hm_loss": 3.33159, "roadside_hm_loss": 4.88663, "laneline_st_off_loss": 12.01629, "roadside_st_off_loss": 13.28877, "mask_loss": 0.83949, "lane_type_mask_loss": 1.12288, "lane_color_mask_loss": 0.461, "side_type_mask_loss": 0.70869, "laneline_len_loss": 8.87938, "laneline_field_loss": 2.40464, "laneline_view_loss": 7.79127, "laneline_inv_len_loss": 7.14149, "laneline_inv_field_loss": 5.69938, "roadside_len_loss": 9.14441, "roadside_field_loss": 1.61111, "roadside_view_loss": 4.58169, "roadside_inv_len_loss": 6.17479, "roadside_inv_field_loss": 2.66876, "depth_loss": 0.15157, "loss": 92.90381, "grad_norm": 356.85147, "time": 0.40708}
{"mode": "train", "epoch": 1, "iter": 57, "lr": 0.0, "memory": 6893, "data_time": 0.00591, "laneline_hm_loss": 3.61245, "roadside_hm_loss": 5.35465, "laneline_st_off_loss": 11.48359, "roadside_st_off_loss": 12.53732, "mask_loss": 0.81131, "lane_type_mask_loss": 1.08747, "lane_color_mask_loss": 0.4644, "side_type_mask_loss": 0.69984, "laneline_len_loss": 8.74318, "laneline_field_loss": 2.50116, "laneline_view_loss": 8.47917, "laneline_inv_len_loss": 7.03279, "laneline_inv_field_loss": 6.2955, "roadside_len_loss": 9.01006, "roadside_field_loss": 1.63456, "roadside_view_loss": 4.98943, "roadside_inv_len_loss": 6.01886, "roadside_inv_field_loss": 2.96663, "depth_loss": 0.18769, "loss": 93.91004, "grad_norm": 350.27789, "time": 0.39988}
{"mode": "train", "epoch": 1, "iter": 58, "lr": 0.0, "memory": 6893, "data_time": 0.00551, "laneline_hm_loss": 2.76785, "roadside_hm_loss": 3.90636, "laneline_st_off_loss": 11.86357, "roadside_st_off_loss": 14.3108, "mask_loss": 0.65058, "lane_type_mask_loss": 1.08938, "lane_color_mask_loss": 0.45486, "side_type_mask_loss": 0.71311, "laneline_len_loss": 5.97922, "laneline_field_loss": 2.82178, "laneline_view_loss": 9.87915, "laneline_inv_len_loss": 4.80545, "laneline_inv_field_loss": 7.19284, "roadside_len_loss": 6.17604, "roadside_field_loss": 1.84187, "roadside_view_loss": 5.60884, "roadside_inv_len_loss": 4.13845, "roadside_inv_field_loss": 3.1546, "depth_loss": 0.25809, "loss": 87.61286, "grad_norm": 321.33823, "time": 0.3927}
{"mode": "train", "epoch": 1, "iter": 59, "lr": 0.0, "memory": 6893, "data_time": 0.01383, "laneline_hm_loss": 3.43561, "roadside_hm_loss": 4.98363, "laneline_st_off_loss": 9.65989, "roadside_st_off_loss": 10.90084, "mask_loss": 0.74087, "lane_type_mask_loss": 1.07706, "lane_color_mask_loss": 0.46703, "side_type_mask_loss": 0.70611, "laneline_len_loss": 7.38693, "laneline_field_loss": 2.3492, "laneline_view_loss": 7.45724, "laneline_inv_len_loss": 5.84158, "laneline_inv_field_loss": 5.7475, "roadside_len_loss": 7.60096, "roadside_field_loss": 1.5466, "roadside_view_loss": 4.3654, "roadside_inv_len_loss": 5.08632, "roadside_inv_field_loss": 2.7651, "depth_loss": 0.14282, "loss": 82.26068, "grad_norm": 364.35458, "time": 0.39889}
{"mode": "train", "epoch": 1, "iter": 60, "lr": 0.0, "memory": 6893, "data_time": 0.01248, "laneline_hm_loss": 2.73328, "roadside_hm_loss": 3.8129, "laneline_st_off_loss": 11.82008, "roadside_st_off_loss": 13.25261, "mask_loss": 0.63953, "lane_type_mask_loss": 1.07221, "lane_color_mask_loss": 0.44388, "side_type_mask_loss": 0.68832, "laneline_len_loss": 5.61797, "laneline_field_loss": 2.57455, "laneline_view_loss": 8.6105, "laneline_inv_len_loss": 4.42157, "laneline_inv_field_loss": 6.59646, "roadside_len_loss": 5.84429, "roadside_field_loss": 1.66143, "roadside_view_loss": 4.79851, "roadside_inv_len_loss": 3.7906, "roadside_inv_field_loss": 2.92916, "depth_loss": 0.21565, "loss": 81.52349, "grad_norm": 328.70993, "time": 0.39784}
{"mode": "train", "epoch": 1, "iter": 61, "lr": 0.0, "memory": 6893, "data_time": 0.00522, "laneline_hm_loss": 3.51849, "roadside_hm_loss": 5.03846, "laneline_st_off_loss": 10.43158, "roadside_st_off_loss": 12.57731, "mask_loss": 0.74336, "lane_type_mask_loss": 1.07525, "lane_color_mask_loss": 0.44887, "side_type_mask_loss": 0.6853, "laneline_len_loss": 7.5483, "laneline_field_loss": 2.56889, "laneline_view_loss": 8.67278, "laneline_inv_len_loss": 6.08946, "laneline_inv_field_loss": 6.50051, "roadside_len_loss": 7.83676, "roadside_field_loss": 1.64854, "roadside_view_loss": 5.01441, "roadside_inv_len_loss": 5.20907, "roadside_inv_field_loss": 3.06087, "depth_loss": 0.10768, "loss": 88.77588, "grad_norm": 396.07343, "time": 0.40122}
{"mode": "train", "epoch": 1, "iter": 62, "lr": 0.0, "memory": 6893, "data_time": 0.00524, "laneline_hm_loss": 2.82116, "roadside_hm_loss": 3.8096, "laneline_st_off_loss": 12.97097, "roadside_st_off_loss": 13.91637, "mask_loss": 0.69747, "lane_type_mask_loss": 1.07128, "lane_color_mask_loss": 0.44381, "side_type_mask_loss": 0.68367, "laneline_len_loss": 6.4055, "laneline_field_loss": 3.00626, "laneline_view_loss": 8.91279, "laneline_inv_len_loss": 5.19327, "laneline_inv_field_loss": 6.51583, "roadside_len_loss": 6.65719, "roadside_field_loss": 1.89877, "roadside_view_loss": 4.5758, "roadside_inv_len_loss": 4.44315, "roadside_inv_field_loss": 2.66052, "depth_loss": 0.19894, "loss": 86.88237, "grad_norm": 350.76529, "time": 0.39999}
{"mode": "train", "epoch": 1, "iter": 63, "lr": 0.0, "memory": 6893, "data_time": 0.00563, "laneline_hm_loss": 2.81927, "roadside_hm_loss": 3.89845, "laneline_st_off_loss": 12.78543, "roadside_st_off_loss": 13.81394, "mask_loss": 0.68971, "lane_type_mask_loss": 1.06162, "lane_color_mask_loss": 0.44208, "side_type_mask_loss": 0.66695, "laneline_len_loss": 6.35466, "laneline_field_loss": 2.67166, "laneline_view_loss": 9.58854, "laneline_inv_len_loss": 5.20449, "laneline_inv_field_loss": 7.37401, "roadside_len_loss": 6.64303, "roadside_field_loss": 1.69241, "roadside_view_loss": 5.42268, "roadside_inv_len_loss": 4.43844, "roadside_inv_field_loss": 3.27914, "depth_loss": 0.19678, "loss": 89.0433, "grad_norm": 294.41956, "time": 0.3984}
{"mode": "train", "epoch": 1, "iter": 64, "lr": 0.0, "memory": 6893, "data_time": 0.00569, "laneline_hm_loss": 2.78813, "roadside_hm_loss": 3.93381, "laneline_st_off_loss": 10.59565, "roadside_st_off_loss": 11.29795, "mask_loss": 0.65573, "lane_type_mask_loss": 1.0551, "lane_color_mask_loss": 0.44704, "side_type_mask_loss": 0.67283, "laneline_len_loss": 6.1773, "laneline_field_loss": 2.71368, "laneline_view_loss": 9.21453, "laneline_inv_len_loss": 5.1893, "laneline_inv_field_loss": 6.87288, "roadside_len_loss": 6.47289, "roadside_field_loss": 1.76998, "roadside_view_loss": 5.11711, "roadside_inv_len_loss": 4.44882, "roadside_inv_field_loss": 3.01568, "depth_loss": 0.25788, "loss": 82.6963, "grad_norm": 287.91736, "time": 0.39954}
{"mode": "train", "epoch": 1, "iter": 65, "lr": 0.0, "memory": 6893, "data_time": 0.00575, "laneline_hm_loss": 3.72815, "roadside_hm_loss": 5.37809, "laneline_st_off_loss": 10.18456, "roadside_st_off_loss": 12.5538, "mask_loss": 0.8009, "lane_type_mask_loss": 1.03989, "lane_color_mask_loss": 0.43791, "side_type_mask_loss": 0.64962, "laneline_len_loss": 7.91111, "laneline_field_loss": 2.41331, "laneline_view_loss": 8.03189, "laneline_inv_len_loss": 6.69553, "laneline_inv_field_loss": 5.98708, "roadside_len_loss": 8.26579, "roadside_field_loss": 1.6602, "roadside_view_loss": 4.9453, "roadside_inv_len_loss": 5.6442, "roadside_inv_field_loss": 2.99645, "depth_loss": 0.13429, "loss": 89.45805, "grad_norm": 352.60977, "time": 0.39871}
{"mode": "train", "epoch": 1, "iter": 66, "lr": 0.0, "memory": 6893, "data_time": 0.0074, "laneline_hm_loss": 3.11981, "roadside_hm_loss": 4.29891, "laneline_st_off_loss": 11.56429, "roadside_st_off_loss": 13.34184, "mask_loss": 0.71944, "lane_type_mask_loss": 1.00582, "lane_color_mask_loss": 0.42609, "side_type_mask_loss": 0.67052, "laneline_len_loss": 6.81326, "laneline_field_loss": 2.60286, "laneline_view_loss": 8.86967, "laneline_inv_len_loss": 5.63954, "laneline_inv_field_loss": 6.54582, "roadside_len_loss": 7.09853, "roadside_field_loss": 1.72753, "roadside_view_loss": 5.12402, "roadside_inv_len_loss": 4.75067, "roadside_inv_field_loss": 3.02222, "depth_loss": 0.19979, "loss": 87.54062, "grad_norm": 355.71976, "time": 0.40132}
{"mode": "train", "epoch": 1, "iter": 67, "lr": 0.0, "memory": 6893, "data_time": 0.00731, "laneline_hm_loss": 4.68483, "roadside_hm_loss": 6.96092, "laneline_st_off_loss": 10.0908, "roadside_st_off_loss": 11.16274, "mask_loss": 0.95481, "lane_type_mask_loss": 1.00555, "lane_color_mask_loss": 0.44733, "side_type_mask_loss": 0.6555, "laneline_len_loss": 10.9512, "laneline_field_loss": 2.08389, "laneline_view_loss": 5.8815, "laneline_inv_len_loss": 9.31116, "laneline_inv_field_loss": 4.34024, "roadside_len_loss": 11.50077, "roadside_field_loss": 1.53585, "roadside_view_loss": 3.74207, "roadside_inv_len_loss": 7.66973, "roadside_inv_field_loss": 2.3415, "depth_loss": 0.08143, "loss": 95.40182, "grad_norm": 441.81061, "time": 0.40191}
{"mode": "train", "epoch": 1, "iter": 68, "lr": 0.0, "memory": 6893, "data_time": 0.00568, "laneline_hm_loss": 2.96503, "roadside_hm_loss": 4.14836, "laneline_st_off_loss": 10.51136, "roadside_st_off_loss": 11.95409, "mask_loss": 0.63434, "lane_type_mask_loss": 0.98772, "lane_color_mask_loss": 0.41668, "side_type_mask_loss": 0.66895, "laneline_len_loss": 5.79527, "laneline_field_loss": 2.58299, "laneline_view_loss": 8.68987, "laneline_inv_len_loss": 4.7557, "laneline_inv_field_loss": 6.54474, "roadside_len_loss": 6.07842, "roadside_field_loss": 1.65236, "roadside_view_loss": 4.97198, "roadside_inv_len_loss": 3.9374, "roadside_inv_field_loss": 3.03696, "depth_loss": 0.22464, "loss": 80.55687, "grad_norm": 342.78983, "time": 0.40136}
{"mode": "train", "epoch": 1, "iter": 69, "lr": 0.0, "memory": 6893, "data_time": 0.00525, "laneline_hm_loss": 2.78111, "roadside_hm_loss": 3.83168, "laneline_st_off_loss": 11.4099, "roadside_st_off_loss": 12.57139, "mask_loss": 0.65767, "lane_type_mask_loss": 0.99528, "lane_color_mask_loss": 0.40842, "side_type_mask_loss": 0.65292, "laneline_len_loss": 6.14025, "laneline_field_loss": 2.83198, "laneline_view_loss": 10.11464, "laneline_inv_len_loss": 5.22721, "laneline_inv_field_loss": 7.47316, "roadside_len_loss": 6.34239, "roadside_field_loss": 1.81047, "roadside_view_loss": 5.60481, "roadside_inv_len_loss": 4.41443, "roadside_inv_field_loss": 3.21769, "depth_loss": 0.27062, "loss": 86.75603, "grad_norm": 343.99228, "time": 0.39665}
{"mode": "train", "epoch": 1, "iter": 70, "lr": 0.0, "memory": 6893, "data_time": 0.00572, "laneline_hm_loss": 2.72361, "roadside_hm_loss": 3.65636, "laneline_st_off_loss": 11.46409, "roadside_st_off_loss": 12.26177, "mask_loss": 0.59429, "lane_type_mask_loss": 0.96656, "lane_color_mask_loss": 0.39521, "side_type_mask_loss": 0.65385, "laneline_len_loss": 4.85634, "laneline_field_loss": 2.51319, "laneline_view_loss": 8.42925, "laneline_inv_len_loss": 4.01334, "laneline_inv_field_loss": 6.5746, "roadside_len_loss": 5.07969, "roadside_field_loss": 1.63025, "roadside_view_loss": 4.8738, "roadside_inv_len_loss": 3.28695, "roadside_inv_field_loss": 2.99357, "depth_loss": 0.25587, "loss": 77.22259, "grad_norm": 342.02979, "time": 0.40319}
{"mode": "train", "epoch": 1, "iter": 71, "lr": 0.0, "memory": 6893, "data_time": 0.00576, "laneline_hm_loss": 4.20753, "roadside_hm_loss": 6.32488, "laneline_st_off_loss": 8.85161, "roadside_st_off_loss": 9.41031, "mask_loss": 0.75554, "lane_type_mask_loss": 0.99894, "lane_color_mask_loss": 0.42515, "side_type_mask_loss": 0.64522, "laneline_len_loss": 7.11419, "laneline_field_loss": 2.1916, "laneline_view_loss": 6.99005, "laneline_inv_len_loss": 6.16888, "laneline_inv_field_loss": 5.31587, "roadside_len_loss": 7.59259, "roadside_field_loss": 1.5673, "roadside_view_loss": 4.62444, "roadside_inv_len_loss": 4.99268, "roadside_inv_field_loss": 2.92948, "depth_loss": 0.13178, "loss": 81.23804, "grad_norm": 373.65411, "time": 0.39929}
{"mode": "train", "epoch": 1, "iter": 72, "lr": 0.0, "memory": 6893, "data_time": 0.00558, "laneline_hm_loss": 4.34418, "roadside_hm_loss": 6.44437, "laneline_st_off_loss": 9.4756, "roadside_st_off_loss": 9.92927, "mask_loss": 0.81147, "lane_type_mask_loss": 0.97291, "lane_color_mask_loss": 0.43072, "side_type_mask_loss": 0.63277, "laneline_len_loss": 8.26546, "laneline_field_loss": 1.92509, "laneline_view_loss": 5.47585, "laneline_inv_len_loss": 7.24909, "laneline_inv_field_loss": 4.1521, "roadside_len_loss": 8.87424, "roadside_field_loss": 1.49457, "roadside_view_loss": 3.76672, "roadside_inv_len_loss": 5.89082, "roadside_inv_field_loss": 2.38126, "depth_loss": 0.15102, "loss": 82.66751, "grad_norm": 370.90106, "time": 0.39091}
{"mode": "train", "epoch": 1, "iter": 73, "lr": 0.0, "memory": 6893, "data_time": 0.01285, "laneline_hm_loss": 2.77517, "roadside_hm_loss": 3.63263, "laneline_st_off_loss": 12.09751, "roadside_st_off_loss": 12.63793, "mask_loss": 0.65139, "lane_type_mask_loss": 0.94825, "lane_color_mask_loss": 0.38586, "side_type_mask_loss": 0.6139, "laneline_len_loss": 5.13904, "laneline_field_loss": 2.6831, "laneline_view_loss": 9.09009, "laneline_inv_len_loss": 4.41093, "laneline_inv_field_loss": 6.74504, "roadside_len_loss": 5.59363, "roadside_field_loss": 1.70881, "roadside_view_loss": 4.91005, "roadside_inv_len_loss": 3.45646, "roadside_inv_field_loss": 2.91008, "depth_loss": 0.25243, "loss": 80.6423, "grad_norm": 305.4144, "time": 0.40855}
{"mode": "train", "epoch": 1, "iter": 74, "lr": 0.0, "memory": 6893, "data_time": 0.00569, "laneline_hm_loss": 3.27494, "roadside_hm_loss": 4.60186, "laneline_st_off_loss": 10.25664, "roadside_st_off_loss": 12.17654, "mask_loss": 0.69628, "lane_type_mask_loss": 0.97945, "lane_color_mask_loss": 0.42665, "side_type_mask_loss": 0.60489, "laneline_len_loss": 6.07639, "laneline_field_loss": 2.67924, "laneline_view_loss": 8.4501, "laneline_inv_len_loss": 5.52754, "laneline_inv_field_loss": 6.0179, "roadside_len_loss": 6.52634, "roadside_field_loss": 1.8029, "roadside_view_loss": 4.86486, "roadside_inv_len_loss": 4.39035, "roadside_inv_field_loss": 2.76299, "depth_loss": 0.19254, "loss": 82.3084, "grad_norm": 374.55185, "time": 0.39843}
{"mode": "train", "epoch": 1, "iter": 75, "lr": 0.0, "memory": 6893, "data_time": 0.0059, "laneline_hm_loss": 2.86984, "roadside_hm_loss": 3.90719, "laneline_st_off_loss": 10.07818, "roadside_st_off_loss": 9.96216, "mask_loss": 0.6211, "lane_type_mask_loss": 0.9506, "lane_color_mask_loss": 0.40395, "side_type_mask_loss": 0.60041, "laneline_len_loss": 5.26777, "laneline_field_loss": 2.54383, "laneline_view_loss": 8.45403, "laneline_inv_len_loss": 4.73345, "laneline_inv_field_loss": 6.3124, "roadside_len_loss": 5.69263, "roadside_field_loss": 1.67766, "roadside_view_loss": 4.80614, "roadside_inv_len_loss": 3.79882, "roadside_inv_field_loss": 2.85791, "depth_loss": 0.25052, "loss": 75.78858, "grad_norm": 364.19296, "time": 0.40398}
{"mode": "train", "epoch": 1, "iter": 76, "lr": 0.0, "memory": 6893, "data_time": 0.00639, "laneline_hm_loss": 3.03138, "roadside_hm_loss": 4.1339, "laneline_st_off_loss": 10.39164, "roadside_st_off_loss": 10.90086, "mask_loss": 0.68373, "lane_type_mask_loss": 0.94002, "lane_color_mask_loss": 0.4103, "side_type_mask_loss": 0.60623, "laneline_len_loss": 5.33089, "laneline_field_loss": 2.39659, "laneline_view_loss": 8.09747, "laneline_inv_len_loss": 4.87967, "laneline_inv_field_loss": 6.1132, "roadside_len_loss": 5.84798, "roadside_field_loss": 1.64367, "roadside_view_loss": 4.92864, "roadside_inv_len_loss": 3.80614, "roadside_inv_field_loss": 2.97184, "depth_loss": 0.15938, "loss": 77.27354, "grad_norm": 323.76285, "time": 0.39201}
{"mode": "train", "epoch": 1, "iter": 77, "lr": 0.0, "memory": 6893, "data_time": 0.01173, "laneline_hm_loss": 3.15281, "roadside_hm_loss": 4.29791, "laneline_st_off_loss": 12.13773, "roadside_st_off_loss": 12.90449, "mask_loss": 0.6834, "lane_type_mask_loss": 0.88875, "lane_color_mask_loss": 0.38123, "side_type_mask_loss": 0.60693, "laneline_len_loss": 6.16976, "laneline_field_loss": 2.70444, "laneline_view_loss": 9.59779, "laneline_inv_len_loss": 5.66909, "laneline_inv_field_loss": 7.11635, "roadside_len_loss": 6.71166, "roadside_field_loss": 1.79141, "roadside_view_loss": 5.44661, "roadside_inv_len_loss": 4.33745, "roadside_inv_field_loss": 3.23691, "depth_loss": 0.18653, "loss": 88.02126, "grad_norm": 386.61557, "time": 0.39737}
{"mode": "train", "epoch": 1, "iter": 78, "lr": 0.0, "memory": 6893, "data_time": 0.00528, "laneline_hm_loss": 2.63366, "roadside_hm_loss": 3.48249, "laneline_st_off_loss": 11.17931, "roadside_st_off_loss": 11.5354, "mask_loss": 0.59572, "lane_type_mask_loss": 0.90134, "lane_color_mask_loss": 0.37546, "side_type_mask_loss": 0.58239, "laneline_len_loss": 4.35471, "laneline_field_loss": 2.74178, "laneline_view_loss": 8.61617, "laneline_inv_len_loss": 3.86195, "laneline_inv_field_loss": 6.48107, "roadside_len_loss": 4.73704, "roadside_field_loss": 1.73941, "roadside_view_loss": 4.70436, "roadside_inv_len_loss": 2.9906, "roadside_inv_field_loss": 2.82573, "depth_loss": 0.18888, "loss": 74.52747, "grad_norm": 329.2663, "time": 0.39322}
{"mode": "train", "epoch": 1, "iter": 79, "lr": 0.0, "memory": 6893, "data_time": 0.01189, "laneline_hm_loss": 2.88134, "roadside_hm_loss": 3.91118, "laneline_st_off_loss": 12.39306, "roadside_st_off_loss": 11.80187, "mask_loss": 0.62159, "lane_type_mask_loss": 0.89496, "lane_color_mask_loss": 0.37599, "side_type_mask_loss": 0.57875, "laneline_len_loss": 4.98123, "laneline_field_loss": 2.60336, "laneline_view_loss": 8.70779, "laneline_inv_len_loss": 4.65855, "laneline_inv_field_loss": 6.39301, "roadside_len_loss": 5.53099, "roadside_field_loss": 1.69705, "roadside_view_loss": 4.85702, "roadside_inv_len_loss": 3.47799, "roadside_inv_field_loss": 2.86477, "depth_loss": 0.13154, "loss": 79.36205, "grad_norm": 344.72958, "time": 0.4059}
{"mode": "train", "epoch": 1, "iter": 80, "lr": 0.0, "memory": 6893, "data_time": 0.00569, "laneline_hm_loss": 3.26551, "roadside_hm_loss": 4.53743, "laneline_st_off_loss": 9.69964, "roadside_st_off_loss": 11.41892, "mask_loss": 0.64707, "lane_type_mask_loss": 0.89014, "lane_color_mask_loss": 0.37704, "side_type_mask_loss": 0.56994, "laneline_len_loss": 5.10783, "laneline_field_loss": 2.54815, "laneline_view_loss": 8.28299, "laneline_inv_len_loss": 4.89994, "laneline_inv_field_loss": 6.11178, "roadside_len_loss": 5.73153, "roadside_field_loss": 1.75181, "roadside_view_loss": 4.95595, "roadside_inv_len_loss": 3.64353, "roadside_inv_field_loss": 2.95716, "depth_loss": 0.19564, "loss": 77.592, "grad_norm": 360.10184, "time": 0.40188}
{"mode": "train", "epoch": 1, "iter": 81, "lr": 0.0, "memory": 6893, "data_time": 0.00546, "laneline_hm_loss": 2.84247, "roadside_hm_loss": 3.75459, "laneline_st_off_loss": 10.63029, "roadside_st_off_loss": 11.19515, "mask_loss": 0.62714, "lane_type_mask_loss": 0.87914, "lane_color_mask_loss": 0.36099, "side_type_mask_loss": 0.57, "laneline_len_loss": 4.47166, "laneline_field_loss": 2.48197, "laneline_view_loss": 8.42768, "laneline_inv_len_loss": 4.1538, "laneline_inv_field_loss": 6.41294, "roadside_len_loss": 5.06158, "roadside_field_loss": 1.59395, "roadside_view_loss": 4.83588, "roadside_inv_len_loss": 3.00759, "roadside_inv_field_loss": 2.93638, "depth_loss": 0.16254, "loss": 74.40574, "grad_norm": 358.69644, "time": 0.39805}
{"mode": "train", "epoch": 1, "iter": 82, "lr": 0.0, "memory": 6893, "data_time": 0.0064, "laneline_hm_loss": 2.4544, "roadside_hm_loss": 3.2683, "laneline_st_off_loss": 10.4563, "roadside_st_off_loss": 11.05599, "mask_loss": 0.605, "lane_type_mask_loss": 0.88158, "lane_color_mask_loss": 0.36728, "side_type_mask_loss": 0.54939, "laneline_len_loss": 3.99466, "laneline_field_loss": 2.79267, "laneline_view_loss": 9.96141, "laneline_inv_len_loss": 3.99083, "laneline_inv_field_loss": 7.35822, "roadside_len_loss": 4.47818, "roadside_field_loss": 1.80079, "roadside_view_loss": 5.50345, "roadside_inv_len_loss": 2.92795, "roadside_inv_field_loss": 3.14302, "depth_loss": 0.25669, "loss": 75.84611, "grad_norm": 306.82596, "time": 0.39588}
{"mode": "train", "epoch": 1, "iter": 83, "lr": 0.0, "memory": 6893, "data_time": 0.01138, "laneline_hm_loss": 2.85474, "roadside_hm_loss": 3.88171, "laneline_st_off_loss": 8.99544, "roadside_st_off_loss": 10.03146, "mask_loss": 0.58031, "lane_type_mask_loss": 0.84354, "lane_color_mask_loss": 0.35233, "side_type_mask_loss": 0.54899, "laneline_len_loss": 4.13515, "laneline_field_loss": 2.64153, "laneline_view_loss": 8.72072, "laneline_inv_len_loss": 3.89091, "laneline_inv_field_loss": 6.38329, "roadside_len_loss": 4.75147, "roadside_field_loss": 1.6973, "roadside_view_loss": 4.8659, "roadside_inv_len_loss": 2.67237, "roadside_inv_field_loss": 2.91222, "depth_loss": 0.1637, "loss": 70.92308, "grad_norm": 340.03952, "time": 0.4025}
{"mode": "train", "epoch": 1, "iter": 84, "lr": 0.0, "memory": 6893, "data_time": 0.0057, "laneline_hm_loss": 3.88397, "roadside_hm_loss": 5.58264, "laneline_st_off_loss": 8.38266, "roadside_st_off_loss": 8.51317, "mask_loss": 0.76086, "lane_type_mask_loss": 0.85068, "lane_color_mask_loss": 0.37473, "side_type_mask_loss": 0.53539, "laneline_len_loss": 6.15971, "laneline_field_loss": 2.07056, "laneline_view_loss": 6.45858, "laneline_inv_len_loss": 6.08919, "laneline_inv_field_loss": 4.79496, "roadside_len_loss": 7.06872, "roadside_field_loss": 1.49461, "roadside_view_loss": 4.17945, "roadside_inv_len_loss": 4.18111, "roadside_inv_field_loss": 2.60873, "depth_loss": 0.08999, "loss": 74.07972, "grad_norm": 413.521, "time": 0.39852}
{"mode": "train", "epoch": 1, "iter": 85, "lr": 0.0, "memory": 6893, "data_time": 0.00689, "laneline_hm_loss": 2.63643, "roadside_hm_loss": 3.36296, "laneline_st_off_loss": 11.52355, "roadside_st_off_loss": 11.30626, "mask_loss": 0.58605, "lane_type_mask_loss": 0.8387, "lane_color_mask_loss": 0.35576, "side_type_mask_loss": 0.51904, "laneline_len_loss": 3.45683, "laneline_field_loss": 2.82386, "laneline_view_loss": 9.21692, "laneline_inv_len_loss": 3.35351, "laneline_inv_field_loss": 6.53507, "roadside_len_loss": 4.05057, "roadside_field_loss": 1.82218, "roadside_view_loss": 4.92321, "roadside_inv_len_loss": 2.28463, "roadside_inv_field_loss": 2.7797, "depth_loss": 0.26316, "loss": 72.63836, "grad_norm": 321.01105, "time": 0.39823}
{"mode": "train", "epoch": 1, "iter": 86, "lr": 0.0, "memory": 6893, "data_time": 0.00561, "laneline_hm_loss": 3.01662, "roadside_hm_loss": 4.12444, "laneline_st_off_loss": 8.55475, "roadside_st_off_loss": 8.31897, "mask_loss": 0.61155, "lane_type_mask_loss": 0.83338, "lane_color_mask_loss": 0.33797, "side_type_mask_loss": 0.53269, "laneline_len_loss": 3.70888, "laneline_field_loss": 2.37005, "laneline_view_loss": 7.72951, "laneline_inv_len_loss": 3.82374, "laneline_inv_field_loss": 5.84046, "roadside_len_loss": 4.33436, "roadside_field_loss": 1.58701, "roadside_view_loss": 4.59018, "roadside_inv_len_loss": 2.72292, "roadside_inv_field_loss": 2.82323, "depth_loss": 0.19483, "loss": 66.05554, "grad_norm": 319.80264, "time": 0.39193}
{"mode": "train", "epoch": 1, "iter": 87, "lr": 0.0, "memory": 6893, "data_time": 0.01342, "laneline_hm_loss": 2.71854, "roadside_hm_loss": 3.6173, "laneline_st_off_loss": 10.66619, "roadside_st_off_loss": 10.21981, "mask_loss": 0.6078, "lane_type_mask_loss": 0.82593, "lane_color_mask_loss": 0.35613, "side_type_mask_loss": 0.54275, "laneline_len_loss": 3.33098, "laneline_field_loss": 2.57041, "laneline_view_loss": 8.2799, "laneline_inv_len_loss": 3.52275, "laneline_inv_field_loss": 6.16122, "roadside_len_loss": 3.79777, "roadside_field_loss": 1.66331, "roadside_view_loss": 4.59721, "roadside_inv_len_loss": 2.41412, "roadside_inv_field_loss": 2.77254, "depth_loss": 0.1175, "loss": 68.78217, "grad_norm": 323.82608, "time": 0.40807}
{"mode": "train", "epoch": 1, "iter": 88, "lr": 0.0, "memory": 6893, "data_time": 0.00553, "laneline_hm_loss": 2.62626, "roadside_hm_loss": 3.46823, "laneline_st_off_loss": 10.64942, "roadside_st_off_loss": 11.23839, "mask_loss": 0.58759, "lane_type_mask_loss": 0.7915, "lane_color_mask_loss": 0.33415, "side_type_mask_loss": 0.52201, "laneline_len_loss": 3.02695, "laneline_field_loss": 2.99935, "laneline_view_loss": 10.2066, "laneline_inv_len_loss": 3.13581, "laneline_inv_field_loss": 7.31367, "roadside_len_loss": 3.59118, "roadside_field_loss": 1.92778, "roadside_view_loss": 5.43909, "roadside_inv_len_loss": 2.06095, "roadside_inv_field_loss": 3.08358, "depth_loss": 0.2001, "loss": 73.20261, "grad_norm": 342.39996, "time": 0.39784}
{"mode": "train", "epoch": 1, "iter": 89, "lr": 0.0, "memory": 6893, "data_time": 0.00843, "laneline_hm_loss": 2.62571, "roadside_hm_loss": 3.37108, "laneline_st_off_loss": 11.16577, "roadside_st_off_loss": 9.94999, "mask_loss": 0.58961, "lane_type_mask_loss": 0.77951, "lane_color_mask_loss": 0.33477, "side_type_mask_loss": 0.51877, "laneline_len_loss": 2.59308, "laneline_field_loss": 2.69196, "laneline_view_loss": 9.22325, "laneline_inv_len_loss": 2.77458, "laneline_inv_field_loss": 6.7995, "roadside_len_loss": 3.11743, "roadside_field_loss": 1.76715, "roadside_view_loss": 5.21629, "roadside_inv_len_loss": 1.76317, "roadside_inv_field_loss": 3.06201, "depth_loss": 0.21153, "loss": 68.55515, "grad_norm": 315.09155, "time": 0.40157}
{"mode": "train", "epoch": 1, "iter": 90, "lr": 0.0, "memory": 6893, "data_time": 0.00574, "laneline_hm_loss": 2.91158, "roadside_hm_loss": 3.78011, "laneline_st_off_loss": 10.27401, "roadside_st_off_loss": 10.20768, "mask_loss": 0.6203, "lane_type_mask_loss": 0.76793, "lane_color_mask_loss": 0.31683, "side_type_mask_loss": 0.49041, "laneline_len_loss": 2.62588, "laneline_field_loss": 2.47647, "laneline_view_loss": 8.59502, "laneline_inv_len_loss": 3.02291, "laneline_inv_field_loss": 6.39186, "roadside_len_loss": 3.22483, "roadside_field_loss": 1.66823, "roadside_view_loss": 5.12154, "roadside_inv_len_loss": 1.99286, "roadside_inv_field_loss": 3.02874, "depth_loss": 0.15993, "loss": 67.67713, "grad_norm": 359.73557, "time": 0.39074}
{"mode": "train", "epoch": 1, "iter": 91, "lr": 0.0, "memory": 6893, "data_time": 0.01367, "laneline_hm_loss": 3.02484, "roadside_hm_loss": 3.98142, "laneline_st_off_loss": 7.31444, "roadside_st_off_loss": 7.90814, "mask_loss": 0.61359, "lane_type_mask_loss": 0.73586, "lane_color_mask_loss": 0.31773, "side_type_mask_loss": 0.49206, "laneline_len_loss": 3.11284, "laneline_field_loss": 2.33106, "laneline_view_loss": 7.2823, "laneline_inv_len_loss": 3.20122, "laneline_inv_field_loss": 5.31898, "roadside_len_loss": 3.77582, "roadside_field_loss": 1.58426, "roadside_view_loss": 4.25562, "roadside_inv_len_loss": 1.9523, "roadside_inv_field_loss": 2.5336, "depth_loss": 0.18193, "loss": 59.918, "grad_norm": 353.96341, "time": 0.40779}
{"mode": "train", "epoch": 1, "iter": 92, "lr": 0.0, "memory": 6893, "data_time": 0.00528, "laneline_hm_loss": 2.52805, "roadside_hm_loss": 3.32972, "laneline_st_off_loss": 8.42864, "roadside_st_off_loss": 8.40874, "mask_loss": 0.55108, "lane_type_mask_loss": 0.77242, "lane_color_mask_loss": 0.32451, "side_type_mask_loss": 0.48491, "laneline_len_loss": 1.93817, "laneline_field_loss": 2.55617, "laneline_view_loss": 8.69308, "laneline_inv_len_loss": 2.28983, "laneline_inv_field_loss": 6.61777, "roadside_len_loss": 2.39797, "roadside_field_loss": 1.61848, "roadside_view_loss": 4.91263, "roadside_inv_len_loss": 1.38165, "roadside_inv_field_loss": 2.9859, "depth_loss": 0.19613, "loss": 60.41586, "grad_norm": 283.37106, "time": 0.40121}
{"mode": "train", "epoch": 1, "iter": 93, "lr": 0.0, "memory": 6893, "data_time": 0.00591, "laneline_hm_loss": 2.63815, "roadside_hm_loss": 3.46154, "laneline_st_off_loss": 8.88198, "roadside_st_off_loss": 9.28663, "mask_loss": 0.57098, "lane_type_mask_loss": 0.74948, "lane_color_mask_loss": 0.32498, "side_type_mask_loss": 0.47768, "laneline_len_loss": 1.92128, "laneline_field_loss": 2.43529, "laneline_view_loss": 8.18667, "laneline_inv_len_loss": 2.29832, "laneline_inv_field_loss": 6.19009, "roadside_len_loss": 2.38736, "roadside_field_loss": 1.58802, "roadside_view_loss": 4.76877, "roadside_inv_len_loss": 1.37332, "roadside_inv_field_loss": 2.91202, "depth_loss": 0.18332, "loss": 60.63586, "grad_norm": 292.03259, "time": 0.40223}
{"mode": "train", "epoch": 1, "iter": 94, "lr": 0.0, "memory": 6893, "data_time": 0.00526, "laneline_hm_loss": 2.78174, "roadside_hm_loss": 3.65284, "laneline_st_off_loss": 9.43732, "roadside_st_off_loss": 8.85827, "mask_loss": 0.607, "lane_type_mask_loss": 0.69162, "lane_color_mask_loss": 0.302, "side_type_mask_loss": 0.46688, "laneline_len_loss": 2.12348, "laneline_field_loss": 2.32833, "laneline_view_loss": 7.29451, "laneline_inv_len_loss": 2.26055, "laneline_inv_field_loss": 5.37583, "roadside_len_loss": 2.62468, "roadside_field_loss": 1.58943, "roadside_view_loss": 4.31993, "roadside_inv_len_loss": 1.2661, "roadside_inv_field_loss": 2.57054, "depth_loss": 0.14675, "loss": 58.6978, "grad_norm": 350.72647, "time": 0.39961}
{"mode": "train", "epoch": 1, "iter": 95, "lr": 0.0, "memory": 6893, "data_time": 0.00722, "laneline_hm_loss": 3.12289, "roadside_hm_loss": 4.10305, "laneline_st_off_loss": 9.78227, "roadside_st_off_loss": 9.30105, "mask_loss": 0.67765, "lane_type_mask_loss": 0.68724, "lane_color_mask_loss": 0.29265, "side_type_mask_loss": 0.45258, "laneline_len_loss": 1.77402, "laneline_field_loss": 2.35811, "laneline_view_loss": 7.58562, "laneline_inv_len_loss": 2.13254, "laneline_inv_field_loss": 5.59916, "roadside_len_loss": 2.32328, "roadside_field_loss": 1.60299, "roadside_view_loss": 4.55896, "roadside_inv_len_loss": 1.22661, "roadside_inv_field_loss": 2.75865, "depth_loss": 0.13009, "loss": 60.46942, "grad_norm": 325.47675, "time": 0.40295}
{"mode": "train", "epoch": 1, "iter": 96, "lr": 0.0, "memory": 6893, "data_time": 0.00629, "laneline_hm_loss": 2.3334, "roadside_hm_loss": 2.74898, "laneline_st_off_loss": 11.22111, "roadside_st_off_loss": 10.40552, "mask_loss": 0.60857, "lane_type_mask_loss": 0.66474, "lane_color_mask_loss": 0.27824, "side_type_mask_loss": 0.4397, "laneline_len_loss": 1.20858, "laneline_field_loss": 2.73594, "laneline_view_loss": 9.7012, "laneline_inv_len_loss": 1.39455, "laneline_inv_field_loss": 7.32251, "roadside_len_loss": 1.51688, "roadside_field_loss": 1.69135, "roadside_view_loss": 5.23649, "roadside_inv_len_loss": 0.83908, "roadside_inv_field_loss": 3.14442, "depth_loss": 0.16106, "loss": 63.65233, "grad_norm": 278.96976, "time": 0.39479}
{"mode": "train", "epoch": 1, "iter": 97, "lr": 0.0, "memory": 6893, "data_time": 0.01416, "laneline_hm_loss": 2.65402, "roadside_hm_loss": 3.42243, "laneline_st_off_loss": 8.32655, "roadside_st_off_loss": 8.58997, "mask_loss": 0.58771, "lane_type_mask_loss": 0.63512, "lane_color_mask_loss": 0.28205, "side_type_mask_loss": 0.44884, "laneline_len_loss": 1.30948, "laneline_field_loss": 2.68824, "laneline_view_loss": 9.29963, "laneline_inv_len_loss": 1.7191, "laneline_inv_field_loss": 6.97494, "roadside_len_loss": 1.65359, "roadside_field_loss": 1.69891, "roadside_view_loss": 5.22402, "roadside_inv_len_loss": 0.91833, "roadside_inv_field_loss": 3.14166, "depth_loss": 0.15856, "loss": 59.73313, "grad_norm": 306.58731, "time": 0.40928}
{"mode": "train", "epoch": 1, "iter": 98, "lr": 0.0, "memory": 6893, "data_time": 0.00496, "laneline_hm_loss": 2.30747, "roadside_hm_loss": 2.69413, "laneline_st_off_loss": 10.11889, "roadside_st_off_loss": 9.84777, "mask_loss": 0.55012, "lane_type_mask_loss": 0.63539, "lane_color_mask_loss": 0.28024, "side_type_mask_loss": 0.42277, "laneline_len_loss": 0.78125, "laneline_field_loss": 2.698, "laneline_view_loss": 8.92484, "laneline_inv_len_loss": 1.06662, "laneline_inv_field_loss": 6.48176, "roadside_len_loss": 1.05539, "roadside_field_loss": 1.78658, "roadside_view_loss": 4.80967, "roadside_inv_len_loss": 0.57314, "roadside_inv_field_loss": 2.74678, "depth_loss": 0.26218, "loss": 58.043, "grad_norm": 277.00604, "time": 0.40279}
{"mode": "train", "epoch": 1, "iter": 99, "lr": 0.0, "memory": 6893, "data_time": 0.00556, "laneline_hm_loss": 2.52399, "roadside_hm_loss": 3.05473, "laneline_st_off_loss": 10.21649, "roadside_st_off_loss": 10.39232, "mask_loss": 0.60503, "lane_type_mask_loss": 0.62583, "lane_color_mask_loss": 0.26485, "side_type_mask_loss": 0.43179, "laneline_len_loss": 0.97279, "laneline_field_loss": 2.68319, "laneline_view_loss": 9.505, "laneline_inv_len_loss": 1.18688, "laneline_inv_field_loss": 7.05578, "roadside_len_loss": 1.28471, "roadside_field_loss": 1.80074, "roadside_view_loss": 5.56884, "roadside_inv_len_loss": 0.63501, "roadside_inv_field_loss": 3.26289, "depth_loss": 0.20906, "loss": 62.27991, "grad_norm": 265.77277, "time": 0.39794}
{"mode": "train", "epoch": 1, "iter": 100, "lr": 0.0, "memory": 6893, "data_time": 0.00576, "laneline_hm_loss": 2.54407, "roadside_hm_loss": 3.20248, "laneline_st_off_loss": 10.52073, "roadside_st_off_loss": 11.82511, "mask_loss": 0.62056, "lane_type_mask_loss": 0.62689, "lane_color_mask_loss": 0.27082, "side_type_mask_loss": 0.40566, "laneline_len_loss": 0.94634, "laneline_field_loss": 2.80019, "laneline_view_loss": 9.749, "laneline_inv_len_loss": 1.24024, "laneline_inv_field_loss": 6.96162, "roadside_len_loss": 1.17357, "roadside_field_loss": 1.91861, "roadside_view_loss": 5.63867, "roadside_inv_len_loss": 0.70048, "roadside_inv_field_loss": 3.17587, "depth_loss": 0.14068, "loss": 64.46162, "grad_norm": 360.76785, "time": 0.39311}
{"mode": "train", "epoch": 1, "iter": 101, "lr": 0.0, "memory": 6893, "data_time": 0.01382, "laneline_hm_loss": 2.54563, "roadside_hm_loss": 3.11914, "laneline_st_off_loss": 10.07977, "roadside_st_off_loss": 10.88065, "mask_loss": 0.60238, "lane_type_mask_loss": 0.59402, "lane_color_mask_loss": 0.25689, "side_type_mask_loss": 0.39473, "laneline_len_loss": 0.98172, "laneline_field_loss": 3.63962, "laneline_view_loss": 10.75144, "laneline_inv_len_loss": 1.19368, "laneline_inv_field_loss": 6.99537, "roadside_len_loss": 1.23187, "roadside_field_loss": 2.34679, "roadside_view_loss": 5.52784, "roadside_inv_len_loss": 0.59319, "roadside_inv_field_loss": 2.84869, "depth_loss": 0.17807, "loss": 64.76151, "grad_norm": 301.15936, "time": 0.40071}
{"mode": "train", "epoch": 1, "iter": 102, "lr": 0.0, "memory": 6893, "data_time": 0.01172, "laneline_hm_loss": 2.5416, "roadside_hm_loss": 2.82981, "laneline_st_off_loss": 10.20082, "roadside_st_off_loss": 9.05703, "mask_loss": 0.61449, "lane_type_mask_loss": 0.54294, "lane_color_mask_loss": 0.23667, "side_type_mask_loss": 0.37213, "laneline_len_loss": 0.77485, "laneline_field_loss": 2.72781, "laneline_view_loss": 8.67358, "laneline_inv_len_loss": 0.85311, "laneline_inv_field_loss": 6.24286, "roadside_len_loss": 0.9921, "roadside_field_loss": 1.77786, "roadside_view_loss": 4.82241, "roadside_inv_len_loss": 0.46198, "roadside_inv_field_loss": 2.715, "depth_loss": 0.19708, "loss": 56.63417, "grad_norm": 288.09964, "time": 0.40803}
{"mode": "train", "epoch": 1, "iter": 103, "lr": 0.0, "memory": 6893, "data_time": 0.00623, "laneline_hm_loss": 2.87016, "roadside_hm_loss": 3.80658, "laneline_st_off_loss": 6.93405, "roadside_st_off_loss": 6.05307, "mask_loss": 0.57865, "lane_type_mask_loss": 0.60144, "lane_color_mask_loss": 0.27615, "side_type_mask_loss": 0.40878, "laneline_len_loss": 0.78357, "laneline_field_loss": 2.27588, "laneline_view_loss": 7.49566, "laneline_inv_len_loss": 1.00201, "laneline_inv_field_loss": 5.5779, "roadside_len_loss": 0.93671, "roadside_field_loss": 1.57108, "roadside_view_loss": 4.64942, "roadside_inv_len_loss": 0.53122, "roadside_inv_field_loss": 2.86242, "depth_loss": 0.09315, "loss": 49.3079, "grad_norm": 262.26935, "time": 0.39949}
{"mode": "train", "epoch": 1, "iter": 104, "lr": 0.0, "memory": 6893, "data_time": 0.00535, "laneline_hm_loss": 2.41985, "roadside_hm_loss": 2.763, "laneline_st_off_loss": 10.38103, "roadside_st_off_loss": 8.44761, "mask_loss": 0.58221, "lane_type_mask_loss": 0.54449, "lane_color_mask_loss": 0.23452, "side_type_mask_loss": 0.36687, "laneline_len_loss": 0.54535, "laneline_field_loss": 2.63742, "laneline_view_loss": 9.21861, "laneline_inv_len_loss": 0.68809, "laneline_inv_field_loss": 6.80841, "roadside_len_loss": 0.66103, "roadside_field_loss": 1.77474, "roadside_view_loss": 5.34751, "roadside_inv_len_loss": 0.40135, "roadside_inv_field_loss": 3.07575, "depth_loss": 0.22203, "loss": 57.11987, "grad_norm": 300.72565, "time": 0.39941}
{"mode": "train", "epoch": 1, "iter": 105, "lr": 0.0, "memory": 6893, "data_time": 0.0053, "laneline_hm_loss": 2.24952, "roadside_hm_loss": 2.46936, "laneline_st_off_loss": 7.91789, "roadside_st_off_loss": 7.63902, "mask_loss": 0.5034, "lane_type_mask_loss": 0.52692, "lane_color_mask_loss": 0.23202, "side_type_mask_loss": 0.35699, "laneline_len_loss": 0.47449, "laneline_field_loss": 2.93897, "laneline_view_loss": 10.70026, "laneline_inv_len_loss": 0.57297, "laneline_inv_field_loss": 7.79424, "roadside_len_loss": 0.59535, "roadside_field_loss": 1.84427, "roadside_view_loss": 5.83984, "roadside_inv_len_loss": 0.33804, "roadside_inv_field_loss": 3.32758, "depth_loss": 0.19124, "loss": 56.51237, "grad_norm": 292.15121, "time": 0.39977}
{"mode": "train", "epoch": 1, "iter": 106, "lr": 0.0, "memory": 6893, "data_time": 0.00566, "laneline_hm_loss": 2.78289, "roadside_hm_loss": 3.42262, "laneline_st_off_loss": 8.88137, "roadside_st_off_loss": 7.34079, "mask_loss": 0.64802, "lane_type_mask_loss": 0.51514, "lane_color_mask_loss": 0.24603, "side_type_mask_loss": 0.34865, "laneline_len_loss": 0.70956, "laneline_field_loss": 2.28743, "laneline_view_loss": 7.43096, "laneline_inv_len_loss": 0.74766, "laneline_inv_field_loss": 5.47334, "roadside_len_loss": 0.86561, "roadside_field_loss": 1.61715, "roadside_view_loss": 4.55711, "roadside_inv_len_loss": 0.39463, "roadside_inv_field_loss": 2.75849, "depth_loss": 0.13565, "loss": 51.1631, "grad_norm": 277.78235, "time": 0.39418}
{"mode": "train", "epoch": 1, "iter": 107, "lr": 0.0, "memory": 6893, "data_time": 0.01595, "laneline_hm_loss": 2.41902, "roadside_hm_loss": 2.54105, "laneline_st_off_loss": 11.19437, "roadside_st_off_loss": 10.99668, "mask_loss": 0.55593, "lane_type_mask_loss": 0.46341, "lane_color_mask_loss": 0.2138, "side_type_mask_loss": 0.32719, "laneline_len_loss": 0.45478, "laneline_field_loss": 3.50478, "laneline_view_loss": 11.17277, "laneline_inv_len_loss": 0.54094, "laneline_inv_field_loss": 7.48527, "roadside_len_loss": 0.55197, "roadside_field_loss": 2.03383, "roadside_view_loss": 5.43187, "roadside_inv_len_loss": 0.30927, "roadside_inv_field_loss": 2.82755, "depth_loss": 0.19697, "loss": 63.22144, "grad_norm": 254.08429, "time": 0.41147}
{"mode": "train", "epoch": 1, "iter": 108, "lr": 0.0, "memory": 6893, "data_time": 0.02155, "laneline_hm_loss": 2.61156, "roadside_hm_loss": 3.16078, "laneline_st_off_loss": 7.77977, "roadside_st_off_loss": 7.41993, "mask_loss": 0.57096, "lane_type_mask_loss": 0.49237, "lane_color_mask_loss": 0.24251, "side_type_mask_loss": 0.34415, "laneline_len_loss": 0.37641, "laneline_field_loss": 2.32227, "laneline_view_loss": 7.68405, "laneline_inv_len_loss": 0.6037, "laneline_inv_field_loss": 5.78506, "roadside_len_loss": 0.44479, "roadside_field_loss": 1.55983, "roadside_view_loss": 4.52101, "roadside_inv_len_loss": 0.35368, "roadside_inv_field_loss": 2.79155, "depth_loss": 0.16226, "loss": 49.22663, "grad_norm": 278.15524, "time": 0.41298}
{"mode": "train", "epoch": 1, "iter": 109, "lr": 0.0, "memory": 6893, "data_time": 0.00691, "laneline_hm_loss": 2.61218, "roadside_hm_loss": 2.95367, "laneline_st_off_loss": 7.9471, "roadside_st_off_loss": 7.57664, "mask_loss": 0.60976, "lane_type_mask_loss": 0.44513, "lane_color_mask_loss": 0.2149, "side_type_mask_loss": 0.31518, "laneline_len_loss": 0.3586, "laneline_field_loss": 2.40072, "laneline_view_loss": 7.44133, "laneline_inv_len_loss": 0.45304, "laneline_inv_field_loss": 5.54822, "roadside_len_loss": 0.45249, "roadside_field_loss": 1.58897, "roadside_view_loss": 4.27146, "roadside_inv_len_loss": 0.26664, "roadside_inv_field_loss": 2.61792, "depth_loss": 0.13498, "loss": 48.20894, "grad_norm": 260.81012, "time": 0.41577}
{"mode": "train", "epoch": 1, "iter": 110, "lr": 0.0, "memory": 6893, "data_time": 0.00679, "laneline_hm_loss": 2.59742, "roadside_hm_loss": 2.90678, "laneline_st_off_loss": 9.17023, "roadside_st_off_loss": 7.97253, "mask_loss": 0.55324, "lane_type_mask_loss": 0.43637, "lane_color_mask_loss": 0.20468, "side_type_mask_loss": 0.3043, "laneline_len_loss": 0.32732, "laneline_field_loss": 2.62348, "laneline_view_loss": 9.0948, "laneline_inv_len_loss": 0.43795, "laneline_inv_field_loss": 6.71028, "roadside_len_loss": 0.37804, "roadside_field_loss": 1.77728, "roadside_view_loss": 5.33645, "roadside_inv_len_loss": 0.24772, "roadside_inv_field_loss": 3.16059, "depth_loss": 0.12706, "loss": 54.36652, "grad_norm": 263.60989, "time": 0.40744}
{"mode": "train", "epoch": 1, "iter": 111, "lr": 0.0, "memory": 6893, "data_time": 0.00685, "laneline_hm_loss": 2.61367, "roadside_hm_loss": 3.01588, "laneline_st_off_loss": 8.14877, "roadside_st_off_loss": 7.72024, "mask_loss": 0.58474, "lane_type_mask_loss": 0.4318, "lane_color_mask_loss": 0.2162, "side_type_mask_loss": 0.31467, "laneline_len_loss": 0.40205, "laneline_field_loss": 2.43303, "laneline_view_loss": 7.88727, "laneline_inv_len_loss": 0.44612, "laneline_inv_field_loss": 5.89286, "roadside_len_loss": 0.46456, "roadside_field_loss": 1.61931, "roadside_view_loss": 4.55249, "roadside_inv_len_loss": 0.26222, "roadside_inv_field_loss": 2.82964, "depth_loss": 0.08298, "loss": 49.91848, "grad_norm": 299.32895, "time": 0.40251}
{"mode": "train", "epoch": 1, "iter": 112, "lr": 0.0, "memory": 6893, "data_time": 0.00924, "laneline_hm_loss": 2.58409, "roadside_hm_loss": 2.91273, "laneline_st_off_loss": 8.4536, "roadside_st_off_loss": 7.63893, "mask_loss": 0.57185, "lane_type_mask_loss": 0.40815, "lane_color_mask_loss": 0.20381, "side_type_mask_loss": 0.29925, "laneline_len_loss": 0.31781, "laneline_field_loss": 2.58756, "laneline_view_loss": 9.17523, "laneline_inv_len_loss": 0.40903, "laneline_inv_field_loss": 6.80317, "roadside_len_loss": 0.36854, "roadside_field_loss": 1.71669, "roadside_view_loss": 5.36991, "roadside_inv_len_loss": 0.2556, "roadside_inv_field_loss": 3.21899, "depth_loss": 0.15892, "loss": 53.45388, "grad_norm": 252.6665, "time": 0.42848}
{"mode": "train", "epoch": 1, "iter": 113, "lr": 0.0, "memory": 6893, "data_time": 0.00948, "laneline_hm_loss": 2.3931, "roadside_hm_loss": 2.66182, "laneline_st_off_loss": 6.61716, "roadside_st_off_loss": 6.07039, "mask_loss": 0.5621, "lane_type_mask_loss": 0.40366, "lane_color_mask_loss": 0.21899, "side_type_mask_loss": 0.29821, "laneline_len_loss": 0.26986, "laneline_field_loss": 2.38478, "laneline_view_loss": 7.02071, "laneline_inv_len_loss": 0.35142, "laneline_inv_field_loss": 5.2567, "roadside_len_loss": 0.31895, "roadside_field_loss": 1.52975, "roadside_view_loss": 3.82405, "roadside_inv_len_loss": 0.22498, "roadside_inv_field_loss": 2.33992, "depth_loss": 0.12241, "loss": 42.86897, "grad_norm": 190.05478, "time": 0.41027}
{"mode": "train", "epoch": 1, "iter": 114, "lr": 0.0, "memory": 6893, "data_time": 0.00886, "laneline_hm_loss": 2.37011, "roadside_hm_loss": 2.57677, "laneline_st_off_loss": 8.00851, "roadside_st_off_loss": 7.32181, "mask_loss": 0.55463, "lane_type_mask_loss": 0.39135, "lane_color_mask_loss": 0.20358, "side_type_mask_loss": 0.2867, "laneline_len_loss": 0.25087, "laneline_field_loss": 2.62942, "laneline_view_loss": 9.38258, "laneline_inv_len_loss": 0.34773, "laneline_inv_field_loss": 7.06522, "roadside_len_loss": 0.2977, "roadside_field_loss": 1.66113, "roadside_view_loss": 5.19618, "roadside_inv_len_loss": 0.22804, "roadside_inv_field_loss": 3.12841, "depth_loss": 0.22942, "loss": 52.13018, "grad_norm": 238.41115, "time": 0.42326}
{"mode": "train", "epoch": 1, "iter": 115, "lr": 0.0, "memory": 6893, "data_time": 0.00725, "laneline_hm_loss": 2.5878, "roadside_hm_loss": 2.948, "laneline_st_off_loss": 6.92836, "roadside_st_off_loss": 6.68142, "mask_loss": 0.55181, "lane_type_mask_loss": 0.35826, "lane_color_mask_loss": 0.19619, "side_type_mask_loss": 0.272, "laneline_len_loss": 0.24299, "laneline_field_loss": 2.6357, "laneline_view_loss": 8.8297, "laneline_inv_len_loss": 0.3704, "laneline_inv_field_loss": 6.09176, "roadside_len_loss": 0.30009, "roadside_field_loss": 1.80192, "roadside_view_loss": 5.17428, "roadside_inv_len_loss": 0.21719, "roadside_inv_field_loss": 2.87291, "depth_loss": 0.14048, "loss": 49.20127, "grad_norm": 249.48085, "time": 0.40846}
{"mode": "train", "epoch": 1, "iter": 116, "lr": 0.0, "memory": 6893, "data_time": 0.00714, "laneline_hm_loss": 2.6685, "roadside_hm_loss": 3.04973, "laneline_st_off_loss": 6.46091, "roadside_st_off_loss": 6.04052, "mask_loss": 0.5759, "lane_type_mask_loss": 0.35978, "lane_color_mask_loss": 0.19681, "side_type_mask_loss": 0.26528, "laneline_len_loss": 0.26851, "laneline_field_loss": 2.33061, "laneline_view_loss": 7.88142, "laneline_inv_len_loss": 0.37752, "laneline_inv_field_loss": 5.83837, "roadside_len_loss": 0.3077, "roadside_field_loss": 1.61612, "roadside_view_loss": 4.96156, "roadside_inv_len_loss": 0.24316, "roadside_inv_field_loss": 3.01158, "depth_loss": 0.14467, "loss": 46.59864, "grad_norm": 212.04814, "time": 0.40321}
{"mode": "train", "epoch": 1, "iter": 117, "lr": 0.0, "memory": 6893, "data_time": 0.00681, "laneline_hm_loss": 2.78528, "roadside_hm_loss": 3.3054, "laneline_st_off_loss": 6.19974, "roadside_st_off_loss": 5.91187, "mask_loss": 0.5874, "lane_type_mask_loss": 0.31976, "lane_color_mask_loss": 0.19737, "side_type_mask_loss": 0.26034, "laneline_len_loss": 0.29103, "laneline_field_loss": 2.26197, "laneline_view_loss": 7.37759, "laneline_inv_len_loss": 0.38141, "laneline_inv_field_loss": 5.48299, "roadside_len_loss": 0.32079, "roadside_field_loss": 1.55656, "roadside_view_loss": 4.60565, "roadside_inv_len_loss": 0.21133, "roadside_inv_field_loss": 2.8583, "depth_loss": 0.11958, "loss": 45.03436, "grad_norm": 250.41541, "time": 0.40264}
{"mode": "train", "epoch": 1, "iter": 118, "lr": 0.0, "memory": 6893, "data_time": 0.00529, "laneline_hm_loss": 2.55633, "roadside_hm_loss": 2.85038, "laneline_st_off_loss": 6.77634, "roadside_st_off_loss": 6.22058, "mask_loss": 0.56448, "lane_type_mask_loss": 0.33665, "lane_color_mask_loss": 0.201, "side_type_mask_loss": 0.26159, "laneline_len_loss": 0.24237, "laneline_field_loss": 2.35974, "laneline_view_loss": 7.6325, "laneline_inv_len_loss": 0.32143, "laneline_inv_field_loss": 5.52683, "roadside_len_loss": 0.27313, "roadside_field_loss": 1.62202, "roadside_view_loss": 4.56771, "roadside_inv_len_loss": 0.19908, "roadside_inv_field_loss": 2.71075, "depth_loss": 0.1323, "loss": 45.35519, "grad_norm": 220.54716, "time": 0.39602}
{"mode": "train", "epoch": 1, "iter": 119, "lr": 0.0, "memory": 6893, "data_time": 0.00553, "laneline_hm_loss": 2.33876, "roadside_hm_loss": 2.37449, "laneline_st_off_loss": 6.81378, "roadside_st_off_loss": 6.09638, "mask_loss": 0.50228, "lane_type_mask_loss": 0.27491, "lane_color_mask_loss": 0.17238, "side_type_mask_loss": 0.22302, "laneline_len_loss": 0.17898, "laneline_field_loss": 2.66869, "laneline_view_loss": 9.13035, "laneline_inv_len_loss": 0.2493, "laneline_inv_field_loss": 6.68998, "roadside_len_loss": 0.19888, "roadside_field_loss": 1.70123, "roadside_view_loss": 4.82739, "roadside_inv_len_loss": 0.16222, "roadside_inv_field_loss": 2.80703, "depth_loss": 0.23077, "loss": 47.64083, "grad_norm": 193.23325, "time": 0.40586}
{"mode": "train", "epoch": 1, "iter": 120, "lr": 0.0, "memory": 6893, "data_time": 0.00514, "laneline_hm_loss": 2.55611, "roadside_hm_loss": 2.72917, "laneline_st_off_loss": 7.06496, "roadside_st_off_loss": 6.52787, "mask_loss": 0.55642, "lane_type_mask_loss": 0.31385, "lane_color_mask_loss": 0.19095, "side_type_mask_loss": 0.24123, "laneline_len_loss": 0.21121, "laneline_field_loss": 2.42115, "laneline_view_loss": 8.32102, "laneline_inv_len_loss": 0.28793, "laneline_inv_field_loss": 6.2829, "roadside_len_loss": 0.23377, "roadside_field_loss": 1.70645, "roadside_view_loss": 5.06719, "roadside_inv_len_loss": 0.18062, "roadside_inv_field_loss": 3.12312, "depth_loss": 0.1479, "loss": 48.16382, "grad_norm": 222.56564, "time": 0.40383}
{"mode": "train", "epoch": 1, "iter": 121, "lr": 0.0, "memory": 6893, "data_time": 0.00863, "laneline_hm_loss": 2.42976, "roadside_hm_loss": 2.44772, "laneline_st_off_loss": 7.85454, "roadside_st_off_loss": 6.77282, "mask_loss": 0.54603, "lane_type_mask_loss": 0.25252, "lane_color_mask_loss": 0.16372, "side_type_mask_loss": 0.2062, "laneline_len_loss": 0.18071, "laneline_field_loss": 2.50569, "laneline_view_loss": 8.76786, "laneline_inv_len_loss": 0.25741, "laneline_inv_field_loss": 6.5237, "roadside_len_loss": 0.19657, "roadside_field_loss": 1.66996, "roadside_view_loss": 5.09712, "roadside_inv_len_loss": 0.17275, "roadside_inv_field_loss": 2.94005, "depth_loss": 0.21259, "loss": 49.19772, "grad_norm": 243.08946, "time": 0.43114}
{"mode": "train", "epoch": 1, "iter": 122, "lr": 0.0, "memory": 6893, "data_time": 0.02934, "laneline_hm_loss": 2.65088, "roadside_hm_loss": 2.99198, "laneline_st_off_loss": 4.82523, "roadside_st_off_loss": 4.5804, "mask_loss": 0.60685, "lane_type_mask_loss": 0.29969, "lane_color_mask_loss": 0.19063, "side_type_mask_loss": 0.22981, "laneline_len_loss": 0.21897, "laneline_field_loss": 2.08957, "laneline_view_loss": 6.05529, "laneline_inv_len_loss": 0.29424, "laneline_inv_field_loss": 4.47959, "roadside_len_loss": 0.23684, "roadside_field_loss": 1.48029, "roadside_view_loss": 3.79264, "roadside_inv_len_loss": 0.18926, "roadside_inv_field_loss": 2.37508, "depth_loss": 0.13992, "loss": 37.72715, "grad_norm": 158.3557, "time": 0.45879}
{"mode": "train", "epoch": 1, "iter": 123, "lr": 0.0, "memory": 6893, "data_time": 0.01104, "laneline_hm_loss": 2.35563, "roadside_hm_loss": 2.49277, "laneline_st_off_loss": 7.0021, "roadside_st_off_loss": 6.28294, "mask_loss": 0.54305, "lane_type_mask_loss": 0.25191, "lane_color_mask_loss": 0.1634, "side_type_mask_loss": 0.20442, "laneline_len_loss": 0.16535, "laneline_field_loss": 2.67948, "laneline_view_loss": 9.19198, "laneline_inv_len_loss": 0.24453, "laneline_inv_field_loss": 6.71, "roadside_len_loss": 0.1873, "roadside_field_loss": 1.71726, "roadside_view_loss": 5.02136, "roadside_inv_len_loss": 0.15689, "roadside_inv_field_loss": 2.93912, "depth_loss": 0.17661, "loss": 48.48609, "grad_norm": 195.57985, "time": 0.41278}
{"mode": "train", "epoch": 1, "iter": 124, "lr": 0.0, "memory": 6893, "data_time": 0.00565, "laneline_hm_loss": 2.32984, "roadside_hm_loss": 2.40002, "laneline_st_off_loss": 7.27789, "roadside_st_off_loss": 6.63326, "mask_loss": 0.51833, "lane_type_mask_loss": 0.23809, "lane_color_mask_loss": 0.15397, "side_type_mask_loss": 0.19636, "laneline_len_loss": 0.18863, "laneline_field_loss": 2.54286, "laneline_view_loss": 8.85994, "laneline_inv_len_loss": 0.24803, "laneline_inv_field_loss": 6.63079, "roadside_len_loss": 0.20038, "roadside_field_loss": 1.5969, "roadside_view_loss": 4.93155, "roadside_inv_len_loss": 0.16838, "roadside_inv_field_loss": 2.99269, "depth_loss": 0.15746, "loss": 48.26536, "grad_norm": 221.89409, "time": 0.39877}
{"mode": "train", "epoch": 1, "iter": 125, "lr": 0.0, "memory": 6893, "data_time": 0.00549, "laneline_hm_loss": 2.23835, "roadside_hm_loss": 2.31324, "laneline_st_off_loss": 7.08427, "roadside_st_off_loss": 5.9022, "mask_loss": 0.49831, "lane_type_mask_loss": 0.20855, "lane_color_mask_loss": 0.1436, "side_type_mask_loss": 0.18469, "laneline_len_loss": 0.16014, "laneline_field_loss": 2.7468, "laneline_view_loss": 10.09227, "laneline_inv_len_loss": 0.22904, "laneline_inv_field_loss": 7.3116, "roadside_len_loss": 0.17321, "roadside_field_loss": 1.69848, "roadside_view_loss": 5.50926, "roadside_inv_len_loss": 0.15249, "roadside_inv_field_loss": 3.13783, "depth_loss": 0.16708, "loss": 49.95141, "grad_norm": 198.7756, "time": 0.3923}
{"mode": "train", "epoch": 1, "iter": 126, "lr": 0.0, "memory": 6893, "data_time": 0.00531, "laneline_hm_loss": 2.37642, "roadside_hm_loss": 2.51654, "laneline_st_off_loss": 6.82714, "roadside_st_off_loss": 7.52809, "mask_loss": 0.55926, "lane_type_mask_loss": 0.2114, "lane_color_mask_loss": 0.14816, "side_type_mask_loss": 0.18546, "laneline_len_loss": 0.16346, "laneline_field_loss": 2.65227, "laneline_view_loss": 9.45702, "laneline_inv_len_loss": 0.22883, "laneline_inv_field_loss": 6.94918, "roadside_len_loss": 0.18128, "roadside_field_loss": 1.76342, "roadside_view_loss": 5.36273, "roadside_inv_len_loss": 0.14852, "roadside_inv_field_loss": 3.11656, "depth_loss": 0.20443, "loss": 50.58016, "grad_norm": 215.67793, "time": 0.39333}
{"mode": "train", "epoch": 1, "iter": 127, "lr": 0.0, "memory": 6893, "data_time": 0.00528, "laneline_hm_loss": 2.43742, "roadside_hm_loss": 2.49868, "laneline_st_off_loss": 5.87994, "roadside_st_off_loss": 4.82051, "mask_loss": 0.52616, "lane_type_mask_loss": 0.23097, "lane_color_mask_loss": 0.16181, "side_type_mask_loss": 0.18883, "laneline_len_loss": 0.18537, "laneline_field_loss": 2.42017, "laneline_view_loss": 7.93974, "laneline_inv_len_loss": 0.23993, "laneline_inv_field_loss": 5.79749, "roadside_len_loss": 0.19297, "roadside_field_loss": 1.59687, "roadside_view_loss": 4.59434, "roadside_inv_len_loss": 0.16008, "roadside_inv_field_loss": 2.72737, "depth_loss": 0.17347, "loss": 42.77214, "grad_norm": 181.56374, "time": 0.39283}
{"mode": "train", "epoch": 1, "iter": 128, "lr": 0.0, "memory": 6893, "data_time": 0.00552, "laneline_hm_loss": 2.26565, "roadside_hm_loss": 2.44107, "laneline_st_off_loss": 5.20089, "roadside_st_off_loss": 4.70387, "mask_loss": 0.51155, "lane_type_mask_loss": 0.20769, "lane_color_mask_loss": 0.15058, "side_type_mask_loss": 0.1801, "laneline_len_loss": 0.16297, "laneline_field_loss": 2.35837, "laneline_view_loss": 7.76744, "laneline_inv_len_loss": 0.23885, "laneline_inv_field_loss": 5.85928, "roadside_len_loss": 0.17749, "roadside_field_loss": 1.5293, "roadside_view_loss": 4.49796, "roadside_inv_len_loss": 0.16051, "roadside_inv_field_loss": 2.79608, "depth_loss": 0.12533, "loss": 41.33498, "grad_norm": 154.3111, "time": 0.39429}
{"mode": "train", "epoch": 1, "iter": 129, "lr": 0.0, "memory": 6893, "data_time": 0.00505, "laneline_hm_loss": 2.31194, "roadside_hm_loss": 2.46665, "laneline_st_off_loss": 4.90199, "roadside_st_off_loss": 4.83678, "mask_loss": 0.5202, "lane_type_mask_loss": 0.18936, "lane_color_mask_loss": 0.14035, "side_type_mask_loss": 0.16612, "laneline_len_loss": 0.15837, "laneline_field_loss": 2.30093, "laneline_view_loss": 7.7046, "laneline_inv_len_loss": 0.21991, "laneline_inv_field_loss": 5.69897, "roadside_len_loss": 0.17, "roadside_field_loss": 1.55262, "roadside_view_loss": 4.55264, "roadside_inv_len_loss": 0.14768, "roadside_inv_field_loss": 2.67383, "depth_loss": 0.151, "loss": 40.86396, "grad_norm": 177.7496, "time": 0.39092}
{"mode": "train", "epoch": 1, "iter": 130, "lr": 0.0, "memory": 6893, "data_time": 0.00562, "laneline_hm_loss": 2.04848, "roadside_hm_loss": 2.1543, "laneline_st_off_loss": 6.4483, "roadside_st_off_loss": 5.78039, "mask_loss": 0.50232, "lane_type_mask_loss": 0.19089, "lane_color_mask_loss": 0.13645, "side_type_mask_loss": 0.16138, "laneline_len_loss": 0.13429, "laneline_field_loss": 2.61153, "laneline_view_loss": 9.31154, "laneline_inv_len_loss": 0.18759, "laneline_inv_field_loss": 6.97342, "roadside_len_loss": 0.14427, "roadside_field_loss": 1.69914, "roadside_view_loss": 5.24361, "roadside_inv_len_loss": 0.1272, "roadside_inv_field_loss": 3.10573, "depth_loss": 0.21313, "loss": 47.17395, "grad_norm": 169.44118, "time": 0.39794}
{"mode": "train", "epoch": 1, "iter": 131, "lr": 0.0, "memory": 6893, "data_time": 0.00513, "laneline_hm_loss": 3.05268, "roadside_hm_loss": 3.20654, "laneline_st_off_loss": 4.9943, "roadside_st_off_loss": 4.25489, "mask_loss": 0.65824, "lane_type_mask_loss": 0.19619, "lane_color_mask_loss": 0.1384, "side_type_mask_loss": 0.16853, "laneline_len_loss": 0.17759, "laneline_field_loss": 1.9861, "laneline_view_loss": 6.58134, "laneline_inv_len_loss": 0.28806, "laneline_inv_field_loss": 4.7981, "roadside_len_loss": 0.18966, "roadside_field_loss": 1.55648, "roadside_view_loss": 4.76511, "roadside_inv_len_loss": 0.17487, "roadside_inv_field_loss": 2.92017, "depth_loss": 0.09651, "loss": 40.20375, "grad_norm": 174.06657, "time": 0.40166}
{"mode": "train", "epoch": 1, "iter": 132, "lr": 0.0, "memory": 6893, "data_time": 0.00606, "laneline_hm_loss": 2.11072, "roadside_hm_loss": 2.26194, "laneline_st_off_loss": 5.82443, "roadside_st_off_loss": 5.74296, "mask_loss": 0.49556, "lane_type_mask_loss": 0.17084, "lane_color_mask_loss": 0.12744, "side_type_mask_loss": 0.15564, "laneline_len_loss": 0.16706, "laneline_field_loss": 2.44104, "laneline_view_loss": 8.59375, "laneline_inv_len_loss": 0.21383, "laneline_inv_field_loss": 6.59722, "roadside_len_loss": 0.17515, "roadside_field_loss": 1.55915, "roadside_view_loss": 4.97935, "roadside_inv_len_loss": 0.15616, "roadside_inv_field_loss": 3.07444, "depth_loss": 0.20791, "loss": 45.0546, "grad_norm": 168.52702, "time": 0.40252}
{"mode": "train", "epoch": 1, "iter": 133, "lr": 0.0, "memory": 6893, "data_time": 0.00523, "laneline_hm_loss": 2.00769, "roadside_hm_loss": 2.17531, "laneline_st_off_loss": 4.61026, "roadside_st_off_loss": 3.86402, "mask_loss": 0.49888, "lane_type_mask_loss": 0.18479, "lane_color_mask_loss": 0.13488, "side_type_mask_loss": 0.16197, "laneline_len_loss": 0.142, "laneline_field_loss": 2.43779, "laneline_view_loss": 8.44757, "laneline_inv_len_loss": 0.20212, "laneline_inv_field_loss": 6.37627, "roadside_len_loss": 0.14763, "roadside_field_loss": 1.54155, "roadside_view_loss": 4.73146, "roadside_inv_len_loss": 0.14205, "roadside_inv_field_loss": 2.87672, "depth_loss": 0.23928, "loss": 40.92225, "grad_norm": 120.32197, "time": 0.3925}
{"mode": "train", "epoch": 1, "iter": 134, "lr": 0.0, "memory": 6893, "data_time": 0.00552, "laneline_hm_loss": 2.26299, "roadside_hm_loss": 2.44723, "laneline_st_off_loss": 5.10309, "roadside_st_off_loss": 4.81163, "mask_loss": 0.51834, "lane_type_mask_loss": 0.16172, "lane_color_mask_loss": 0.12725, "side_type_mask_loss": 0.14794, "laneline_len_loss": 0.1676, "laneline_field_loss": 2.25645, "laneline_view_loss": 7.52878, "laneline_inv_len_loss": 0.2279, "laneline_inv_field_loss": 5.75402, "roadside_len_loss": 0.17874, "roadside_field_loss": 1.47548, "roadside_view_loss": 4.40882, "roadside_inv_len_loss": 0.16464, "roadside_inv_field_loss": 2.77442, "depth_loss": 0.17633, "loss": 40.69334, "grad_norm": 174.9841, "time": 0.39957}
{"mode": "train", "epoch": 1, "iter": 135, "lr": 0.0, "memory": 6893, "data_time": 0.00549, "laneline_hm_loss": 2.38571, "roadside_hm_loss": 2.57427, "laneline_st_off_loss": 5.66225, "roadside_st_off_loss": 4.26526, "mask_loss": 0.54598, "lane_type_mask_loss": 0.15784, "lane_color_mask_loss": 0.12531, "side_type_mask_loss": 0.14043, "laneline_len_loss": 0.17826, "laneline_field_loss": 2.35415, "laneline_view_loss": 7.63821, "laneline_inv_len_loss": 0.20589, "laneline_inv_field_loss": 5.50935, "roadside_len_loss": 0.17996, "roadside_field_loss": 1.59525, "roadside_view_loss": 4.56336, "roadside_inv_len_loss": 0.13908, "roadside_inv_field_loss": 2.70137, "depth_loss": 0.15922, "loss": 41.08117, "grad_norm": 180.87035, "time": 0.39918}
{"mode": "train", "epoch": 1, "iter": 136, "lr": 0.0, "memory": 6893, "data_time": 0.00571, "laneline_hm_loss": 1.83583, "roadside_hm_loss": 2.02087, "laneline_st_off_loss": 5.21852, "roadside_st_off_loss": 4.12281, "mask_loss": 0.45497, "lane_type_mask_loss": 0.14556, "lane_color_mask_loss": 0.11372, "side_type_mask_loss": 0.13379, "laneline_len_loss": 0.13487, "laneline_field_loss": 2.72547, "laneline_view_loss": 9.81229, "laneline_inv_len_loss": 0.18266, "laneline_inv_field_loss": 7.31704, "roadside_len_loss": 0.13826, "roadside_field_loss": 1.70196, "roadside_view_loss": 5.32, "roadside_inv_len_loss": 0.13362, "roadside_inv_field_loss": 3.13508, "depth_loss": 0.17855, "loss": 44.82588, "grad_norm": 137.6337, "time": 0.39728}
{"mode": "train", "epoch": 1, "iter": 137, "lr": 0.0, "memory": 6893, "data_time": 0.00577, "laneline_hm_loss": 2.021, "roadside_hm_loss": 2.13285, "laneline_st_off_loss": 5.19766, "roadside_st_off_loss": 5.56932, "mask_loss": 0.50082, "lane_type_mask_loss": 0.14806, "lane_color_mask_loss": 0.11933, "side_type_mask_loss": 0.1367, "laneline_len_loss": 0.14277, "laneline_field_loss": 2.4872, "laneline_view_loss": 8.75701, "laneline_inv_len_loss": 0.18487, "laneline_inv_field_loss": 6.45268, "roadside_len_loss": 0.14693, "roadside_field_loss": 1.59617, "roadside_view_loss": 4.7992, "roadside_inv_len_loss": 0.13764, "roadside_inv_field_loss": 2.7764, "depth_loss": 0.20864, "loss": 43.51525, "grad_norm": 153.34119, "time": 0.40006}
{"mode": "train", "epoch": 1, "iter": 138, "lr": 0.0, "memory": 6893, "data_time": 0.00582, "laneline_hm_loss": 1.98192, "roadside_hm_loss": 2.19885, "laneline_st_off_loss": 5.29588, "roadside_st_off_loss": 4.94646, "mask_loss": 0.47924, "lane_type_mask_loss": 0.13712, "lane_color_mask_loss": 0.11314, "side_type_mask_loss": 0.12911, "laneline_len_loss": 0.14614, "laneline_field_loss": 2.57501, "laneline_view_loss": 9.0341, "laneline_inv_len_loss": 0.18285, "laneline_inv_field_loss": 6.69708, "roadside_len_loss": 0.14944, "roadside_field_loss": 1.69088, "roadside_view_loss": 5.14455, "roadside_inv_len_loss": 0.13557, "roadside_inv_field_loss": 2.99418, "depth_loss": 0.19819, "loss": 44.22972, "grad_norm": 185.30934, "time": 0.39878}
{"mode": "train", "epoch": 1, "iter": 139, "lr": 0.0, "memory": 6893, "data_time": 0.00655, "laneline_hm_loss": 1.8642, "roadside_hm_loss": 2.03555, "laneline_st_off_loss": 4.5272, "roadside_st_off_loss": 3.98835, "mask_loss": 0.47394, "lane_type_mask_loss": 0.13427, "lane_color_mask_loss": 0.10377, "side_type_mask_loss": 0.1247, "laneline_len_loss": 0.13815, "laneline_field_loss": 2.575, "laneline_view_loss": 9.06089, "laneline_inv_len_loss": 0.17345, "laneline_inv_field_loss": 6.76074, "roadside_len_loss": 0.14086, "roadside_field_loss": 1.64039, "roadside_view_loss": 5.09249, "roadside_inv_len_loss": 0.13247, "roadside_inv_field_loss": 2.99233, "depth_loss": 0.23623, "loss": 42.19498, "grad_norm": 113.20769, "time": 0.40057}
{"mode": "train", "epoch": 1, "iter": 140, "lr": 0.0, "memory": 6893, "data_time": 0.00564, "laneline_hm_loss": 1.81885, "roadside_hm_loss": 2.03074, "laneline_st_off_loss": 5.19351, "roadside_st_off_loss": 4.85367, "mask_loss": 0.46333, "lane_type_mask_loss": 0.12932, "lane_color_mask_loss": 0.10637, "side_type_mask_loss": 0.11958, "laneline_len_loss": 0.13077, "laneline_field_loss": 2.61906, "laneline_view_loss": 9.47567, "laneline_inv_len_loss": 0.17438, "laneline_inv_field_loss": 7.22595, "roadside_len_loss": 0.13249, "roadside_field_loss": 1.55559, "roadside_view_loss": 4.97223, "roadside_inv_len_loss": 0.13304, "roadside_inv_field_loss": 3.02132, "depth_loss": 0.26976, "loss": 44.42562, "grad_norm": 145.59348, "time": 0.39695}
{"mode": "train", "epoch": 1, "iter": 141, "lr": 0.0, "memory": 6893, "data_time": 0.00557, "laneline_hm_loss": 2.03672, "roadside_hm_loss": 2.18708, "laneline_st_off_loss": 6.5589, "roadside_st_off_loss": 5.887, "mask_loss": 0.48391, "lane_type_mask_loss": 0.11922, "lane_color_mask_loss": 0.10901, "side_type_mask_loss": 0.11686, "laneline_len_loss": 0.12818, "laneline_field_loss": 2.79527, "laneline_view_loss": 9.65286, "laneline_inv_len_loss": 0.16805, "laneline_inv_field_loss": 6.95269, "roadside_len_loss": 0.13033, "roadside_field_loss": 1.78928, "roadside_view_loss": 5.24745, "roadside_inv_len_loss": 0.12023, "roadside_inv_field_loss": 2.96962, "depth_loss": 0.15687, "loss": 47.60953, "grad_norm": 197.64928, "time": 0.39099}
{"mode": "train", "epoch": 1, "iter": 142, "lr": 0.0, "memory": 6893, "data_time": 0.01569, "laneline_hm_loss": 2.13702, "roadside_hm_loss": 2.25063, "laneline_st_off_loss": 4.40833, "roadside_st_off_loss": 3.88783, "mask_loss": 0.51427, "lane_type_mask_loss": 0.12771, "lane_color_mask_loss": 0.10939, "side_type_mask_loss": 0.12134, "laneline_len_loss": 0.14693, "laneline_field_loss": 2.32886, "laneline_view_loss": 8.00945, "laneline_inv_len_loss": 0.19253, "laneline_inv_field_loss": 6.00842, "roadside_len_loss": 0.14779, "roadside_field_loss": 1.54916, "roadside_view_loss": 4.74188, "roadside_inv_len_loss": 0.14045, "roadside_inv_field_loss": 2.91957, "depth_loss": 0.14342, "loss": 39.88498, "grad_norm": 155.94576, "time": 0.40425}
{"mode": "train", "epoch": 1, "iter": 143, "lr": 0.0, "memory": 6893, "data_time": 0.00564, "laneline_hm_loss": 1.84071, "roadside_hm_loss": 2.1333, "laneline_st_off_loss": 5.26197, "roadside_st_off_loss": 4.51945, "mask_loss": 0.47554, "lane_type_mask_loss": 0.11302, "lane_color_mask_loss": 0.09752, "side_type_mask_loss": 0.10897, "laneline_len_loss": 0.11712, "laneline_field_loss": 2.63174, "laneline_view_loss": 9.49238, "laneline_inv_len_loss": 0.14891, "laneline_inv_field_loss": 6.91937, "roadside_len_loss": 0.11783, "roadside_field_loss": 1.66311, "roadside_view_loss": 5.12504, "roadside_inv_len_loss": 0.11437, "roadside_inv_field_loss": 2.95276, "depth_loss": 0.23447, "loss": 44.0676, "grad_norm": 376.38928, "time": 0.39965}
{"mode": "train", "epoch": 1, "iter": 144, "lr": 0.0, "memory": 6893, "data_time": 0.00581, "laneline_hm_loss": 1.88119, "roadside_hm_loss": 1.98487, "laneline_st_off_loss": 4.5244, "roadside_st_off_loss": 4.07645, "mask_loss": 0.45351, "lane_type_mask_loss": 0.12472, "lane_color_mask_loss": 0.11041, "side_type_mask_loss": 0.11661, "laneline_len_loss": 0.14367, "laneline_field_loss": 2.69461, "laneline_view_loss": 9.22505, "laneline_inv_len_loss": 0.17948, "laneline_inv_field_loss": 6.75243, "roadside_len_loss": 0.14396, "roadside_field_loss": 1.63087, "roadside_view_loss": 4.86991, "roadside_inv_len_loss": 0.14482, "roadside_inv_field_loss": 2.84085, "depth_loss": 0.15951, "loss": 42.05732, "grad_norm": 159.01039, "time": 0.39862}
{"mode": "train", "epoch": 1, "iter": 145, "lr": 0.0, "memory": 6893, "data_time": 0.00584, "laneline_hm_loss": 1.94872, "roadside_hm_loss": 2.16357, "laneline_st_off_loss": 4.2958, "roadside_st_off_loss": 3.95742, "mask_loss": 0.4738, "lane_type_mask_loss": 0.10967, "lane_color_mask_loss": 0.09655, "side_type_mask_loss": 0.10558, "laneline_len_loss": 0.12767, "laneline_field_loss": 2.67417, "laneline_view_loss": 9.75535, "laneline_inv_len_loss": 0.16365, "laneline_inv_field_loss": 7.20183, "roadside_len_loss": 0.12971, "roadside_field_loss": 1.72585, "roadside_view_loss": 5.5136, "roadside_inv_len_loss": 0.12222, "roadside_inv_field_loss": 3.24063, "depth_loss": 0.16037, "loss": 43.96619, "grad_norm": 143.43832, "time": 0.40125}
{"mode": "train", "epoch": 1, "iter": 146, "lr": 0.0, "memory": 6893, "data_time": 0.0061, "laneline_hm_loss": 1.94211, "roadside_hm_loss": 2.08526, "laneline_st_off_loss": 4.13083, "roadside_st_off_loss": 4.62466, "mask_loss": 0.46688, "lane_type_mask_loss": 0.10906, "lane_color_mask_loss": 0.09726, "side_type_mask_loss": 0.10856, "laneline_len_loss": 0.14037, "laneline_field_loss": 2.79069, "laneline_view_loss": 9.34088, "laneline_inv_len_loss": 0.17216, "laneline_inv_field_loss": 6.64454, "roadside_len_loss": 0.13949, "roadside_field_loss": 1.74964, "roadside_view_loss": 5.07817, "roadside_inv_len_loss": 0.13458, "roadside_inv_field_loss": 2.90342, "depth_loss": 0.16276, "loss": 42.82132, "grad_norm": 131.04893, "time": 0.39852}
{"mode": "train", "epoch": 1, "iter": 147, "lr": 0.0, "memory": 6893, "data_time": 0.00542, "laneline_hm_loss": 1.90668, "roadside_hm_loss": 2.10033, "laneline_st_off_loss": 4.2236, "roadside_st_off_loss": 3.97306, "mask_loss": 0.46587, "lane_type_mask_loss": 0.11663, "lane_color_mask_loss": 0.10011, "side_type_mask_loss": 0.10912, "laneline_len_loss": 0.13997, "laneline_field_loss": 2.56235, "laneline_view_loss": 8.87302, "laneline_inv_len_loss": 0.17507, "laneline_inv_field_loss": 6.59908, "roadside_len_loss": 0.14044, "roadside_field_loss": 1.61899, "roadside_view_loss": 4.89176, "roadside_inv_len_loss": 0.13615, "roadside_inv_field_loss": 2.92693, "depth_loss": 0.21029, "loss": 41.26943, "grad_norm": 131.84845, "time": 0.40311}
{"mode": "train", "epoch": 1, "iter": 148, "lr": 0.0, "memory": 6893, "data_time": 0.00568, "laneline_hm_loss": 2.05855, "roadside_hm_loss": 2.23351, "laneline_st_off_loss": 4.02852, "roadside_st_off_loss": 3.46449, "mask_loss": 0.49787, "lane_type_mask_loss": 0.10227, "lane_color_mask_loss": 0.09659, "side_type_mask_loss": 0.10189, "laneline_len_loss": 0.14035, "laneline_field_loss": 2.41757, "laneline_view_loss": 8.5235, "laneline_inv_len_loss": 0.17174, "laneline_inv_field_loss": 6.14386, "roadside_len_loss": 0.14201, "roadside_field_loss": 1.55402, "roadside_view_loss": 4.97305, "roadside_inv_len_loss": 0.13623, "roadside_inv_field_loss": 2.84805, "depth_loss": 0.14723, "loss": 39.7813, "grad_norm": 144.14603, "time": 0.39293}
{"mode": "train", "epoch": 1, "iter": 149, "lr": 0.0, "memory": 6893, "data_time": 0.01303, "laneline_hm_loss": 1.63157, "roadside_hm_loss": 1.7817, "laneline_st_off_loss": 4.45301, "roadside_st_off_loss": 4.32973, "mask_loss": 0.41939, "lane_type_mask_loss": 0.10784, "lane_color_mask_loss": 0.09962, "side_type_mask_loss": 0.10729, "laneline_len_loss": 0.09843, "laneline_field_loss": 2.69389, "laneline_view_loss": 9.43968, "laneline_inv_len_loss": 0.12546, "laneline_inv_field_loss": 7.19388, "roadside_len_loss": 0.09973, "roadside_field_loss": 1.69202, "roadside_view_loss": 4.83684, "roadside_inv_len_loss": 0.09979, "roadside_inv_field_loss": 2.85214, "depth_loss": 0.2968, "loss": 42.35882, "grad_norm": 132.94609, "time": 0.40603}
{"mode": "train", "epoch": 1, "iter": 150, "lr": 0.0, "memory": 6893, "data_time": 0.0055, "laneline_hm_loss": 1.82691, "roadside_hm_loss": 1.92959, "laneline_st_off_loss": 3.91439, "roadside_st_off_loss": 3.3929, "mask_loss": 0.45938, "lane_type_mask_loss": 0.10253, "lane_color_mask_loss": 0.08902, "side_type_mask_loss": 0.1016, "laneline_len_loss": 0.12232, "laneline_field_loss": 2.76284, "laneline_view_loss": 9.60647, "laneline_inv_len_loss": 0.16281, "laneline_inv_field_loss": 6.96556, "roadside_len_loss": 0.12452, "roadside_field_loss": 1.75735, "roadside_view_loss": 5.21044, "roadside_inv_len_loss": 0.12411, "roadside_inv_field_loss": 3.00513, "depth_loss": 0.16727, "loss": 41.82514, "grad_norm": 112.95833, "time": 0.39744}
{"mode": "train", "epoch": 1, "iter": 151, "lr": 0.0, "memory": 6893, "data_time": 0.00582, "laneline_hm_loss": 2.00306, "roadside_hm_loss": 2.108, "laneline_st_off_loss": 3.90401, "roadside_st_off_loss": 3.70882, "mask_loss": 0.50458, "lane_type_mask_loss": 0.09888, "lane_color_mask_loss": 0.08938, "side_type_mask_loss": 0.09364, "laneline_len_loss": 0.11379, "laneline_field_loss": 2.38778, "laneline_view_loss": 7.89029, "laneline_inv_len_loss": 0.14637, "laneline_inv_field_loss": 5.66482, "roadside_len_loss": 0.11163, "roadside_field_loss": 1.57336, "roadside_view_loss": 4.3508, "roadside_inv_len_loss": 0.10913, "roadside_inv_field_loss": 2.50234, "depth_loss": 0.15862, "loss": 37.51933, "grad_norm": 134.14085, "time": 0.40009}
{"mode": "train", "epoch": 1, "iter": 152, "lr": 0.0, "memory": 6893, "data_time": 0.00599, "laneline_hm_loss": 1.70162, "roadside_hm_loss": 1.82476, "laneline_st_off_loss": 3.90038, "roadside_st_off_loss": 3.83604, "mask_loss": 0.45282, "lane_type_mask_loss": 0.10666, "lane_color_mask_loss": 0.09307, "side_type_mask_loss": 0.1026, "laneline_len_loss": 0.14112, "laneline_field_loss": 2.53555, "laneline_view_loss": 9.16939, "laneline_inv_len_loss": 0.15884, "laneline_inv_field_loss": 6.82053, "roadside_len_loss": 0.14203, "roadside_field_loss": 1.62609, "roadside_view_loss": 5.20921, "roadside_inv_len_loss": 0.13595, "roadside_inv_field_loss": 3.03295, "depth_loss": 0.18702, "loss": 41.17662, "grad_norm": 140.06894, "time": 0.39937}
{"mode": "train", "epoch": 1, "iter": 153, "lr": 0.0, "memory": 6893, "data_time": 0.00581, "laneline_hm_loss": 2.09691, "roadside_hm_loss": 2.25498, "laneline_st_off_loss": 3.20593, "roadside_st_off_loss": 3.0722, "mask_loss": 0.53621, "lane_type_mask_loss": 0.10512, "lane_color_mask_loss": 0.10069, "side_type_mask_loss": 0.10008, "laneline_len_loss": 0.13654, "laneline_field_loss": 2.17726, "laneline_view_loss": 6.95429, "laneline_inv_len_loss": 0.15413, "laneline_inv_field_loss": 5.00936, "roadside_len_loss": 0.13576, "roadside_field_loss": 1.48256, "roadside_view_loss": 3.98087, "roadside_inv_len_loss": 0.12571, "roadside_inv_field_loss": 2.30524, "depth_loss": 0.13165, "loss": 34.06549, "grad_norm": 119.85236, "time": 0.40243}
{"mode": "train", "epoch": 1, "iter": 154, "lr": 0.0, "memory": 6893, "data_time": 0.0059, "laneline_hm_loss": 1.72701, "roadside_hm_loss": 1.91392, "laneline_st_off_loss": 3.52538, "roadside_st_off_loss": 3.57544, "mask_loss": 0.41538, "lane_type_mask_loss": 0.09985, "lane_color_mask_loss": 0.09086, "side_type_mask_loss": 0.09869, "laneline_len_loss": 0.14085, "laneline_field_loss": 2.54424, "laneline_view_loss": 9.15806, "laneline_inv_len_loss": 0.16169, "laneline_inv_field_loss": 6.98956, "roadside_len_loss": 0.14104, "roadside_field_loss": 1.5593, "roadside_view_loss": 4.96729, "roadside_inv_len_loss": 0.14377, "roadside_inv_field_loss": 3.02721, "depth_loss": 0.2302, "loss": 40.50974, "grad_norm": 99.40817, "time": 0.40096}
{"mode": "train", "epoch": 1, "iter": 155, "lr": 0.0, "memory": 6893, "data_time": 0.00564, "laneline_hm_loss": 1.8182, "roadside_hm_loss": 1.94175, "laneline_st_off_loss": 3.53185, "roadside_st_off_loss": 3.37242, "mask_loss": 0.43857, "lane_type_mask_loss": 0.09409, "lane_color_mask_loss": 0.09055, "side_type_mask_loss": 0.09587, "laneline_len_loss": 0.12643, "laneline_field_loss": 2.43597, "laneline_view_loss": 8.09144, "laneline_inv_len_loss": 0.15103, "laneline_inv_field_loss": 6.04631, "roadside_len_loss": 0.12663, "roadside_field_loss": 1.49797, "roadside_view_loss": 4.23868, "roadside_inv_len_loss": 0.13062, "roadside_inv_field_loss": 2.56132, "depth_loss": 0.19422, "loss": 36.98391, "grad_norm": 105.90141, "time": 0.39182}
{"mode": "train", "epoch": 1, "iter": 156, "lr": 0.0, "memory": 6893, "data_time": 0.0149, "laneline_hm_loss": 1.89131, "roadside_hm_loss": 2.01611, "laneline_st_off_loss": 3.33225, "roadside_st_off_loss": 3.34324, "mask_loss": 0.47416, "lane_type_mask_loss": 0.09679, "lane_color_mask_loss": 0.09737, "side_type_mask_loss": 0.09882, "laneline_len_loss": 0.12406, "laneline_field_loss": 2.41964, "laneline_view_loss": 7.99672, "laneline_inv_len_loss": 0.15278, "laneline_inv_field_loss": 5.84329, "roadside_len_loss": 0.12506, "roadside_field_loss": 1.58446, "roadside_view_loss": 4.49942, "roadside_inv_len_loss": 0.12592, "roadside_inv_field_loss": 2.62197, "depth_loss": 0.21959, "loss": 37.06297, "grad_norm": 104.83357, "time": 0.39958}
{"mode": "train", "epoch": 1, "iter": 157, "lr": 0.0, "memory": 6893, "data_time": 0.01292, "laneline_hm_loss": 1.7862, "roadside_hm_loss": 1.94371, "laneline_st_off_loss": 3.40357, "roadside_st_off_loss": 2.92397, "mask_loss": 0.43842, "lane_type_mask_loss": 0.09026, "lane_color_mask_loss": 0.08114, "side_type_mask_loss": 0.09206, "laneline_len_loss": 0.12178, "laneline_field_loss": 2.63861, "laneline_view_loss": 9.26574, "laneline_inv_len_loss": 0.14861, "laneline_inv_field_loss": 6.82774, "roadside_len_loss": 0.12557, "roadside_field_loss": 1.66431, "roadside_view_loss": 5.13319, "roadside_inv_len_loss": 0.1252, "roadside_inv_field_loss": 2.98271, "depth_loss": 0.18485, "loss": 39.97765, "grad_norm": 134.58885, "time": 0.40791}
{"mode": "train", "epoch": 1, "iter": 158, "lr": 0.0, "memory": 6893, "data_time": 0.00662, "laneline_hm_loss": 1.69064, "roadside_hm_loss": 1.83542, "laneline_st_off_loss": 4.2245, "roadside_st_off_loss": 3.90436, "mask_loss": 0.40267, "lane_type_mask_loss": 0.08712, "lane_color_mask_loss": 0.08086, "side_type_mask_loss": 0.08711, "laneline_len_loss": 0.13606, "laneline_field_loss": 2.5109, "laneline_view_loss": 9.15899, "laneline_inv_len_loss": 0.14702, "laneline_inv_field_loss": 6.9547, "roadside_len_loss": 0.13587, "roadside_field_loss": 1.57543, "roadside_view_loss": 5.07338, "roadside_inv_len_loss": 0.1359, "roadside_inv_field_loss": 3.0527, "depth_loss": 0.19144, "loss": 41.38507, "grad_norm": 141.16759, "time": 0.4014}
{"mode": "train", "epoch": 1, "iter": 159, "lr": 0.0, "memory": 6893, "data_time": 0.00553, "laneline_hm_loss": 1.59993, "roadside_hm_loss": 1.74421, "laneline_st_off_loss": 4.15336, "roadside_st_off_loss": 4.03272, "mask_loss": 0.41119, "lane_type_mask_loss": 0.08048, "lane_color_mask_loss": 0.07618, "side_type_mask_loss": 0.08339, "laneline_len_loss": 0.10044, "laneline_field_loss": 2.95796, "laneline_view_loss": 11.34104, "laneline_inv_len_loss": 0.11657, "laneline_inv_field_loss": 8.3466, "roadside_len_loss": 0.09884, "roadside_field_loss": 1.80025, "roadside_view_loss": 5.7946, "roadside_inv_len_loss": 0.1031, "roadside_inv_field_loss": 3.21877, "depth_loss": 0.28267, "loss": 46.34231, "grad_norm": 111.23895, "time": 0.39957}
{"mode": "train", "epoch": 1, "iter": 160, "lr": 0.0, "memory": 6893, "data_time": 0.00559, "laneline_hm_loss": 1.59321, "roadside_hm_loss": 1.79935, "laneline_st_off_loss": 3.98098, "roadside_st_off_loss": 3.0384, "mask_loss": 0.43467, "lane_type_mask_loss": 0.08898, "lane_color_mask_loss": 0.0802, "side_type_mask_loss": 0.09068, "laneline_len_loss": 0.12569, "laneline_field_loss": 2.52059, "laneline_view_loss": 9.06385, "laneline_inv_len_loss": 0.13798, "laneline_inv_field_loss": 6.85548, "roadside_len_loss": 0.12586, "roadside_field_loss": 1.52735, "roadside_view_loss": 4.84571, "roadside_inv_len_loss": 0.12953, "roadside_inv_field_loss": 2.90921, "depth_loss": 0.24663, "loss": 39.59436, "grad_norm": 179.10977, "time": 0.40126}
