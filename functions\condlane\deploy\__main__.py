import os
import json

import ipdb
import torch
# from petrel_client.client import Client

from .process import dump_results
from .utils import get_model, get_data, parse_args
from .tokestrel import tokestrel
from .todataset import todataset


@torch.no_grad()
def main():
    args = parse_args()
    model, config = get_model(args)
    import numpy as np
    #print("model______________________________________________________________________________===================+++++++++++++++: ", model.__class__)
    # client = Client(args.client_conf)
    client = None
    data, data_info = get_data(
        config,
        client,
        args.crop,
        args.random,
        args.camera,
    )
    #print("data: ", data)
    np.save("deploy_main_data.npy", data)
    #print("data_info: ", data_info)
    np.save("deploy_main_data_info.npy", data_info)
    #print("config: ", config)
    np.save("deploy_main_config.npy", config)
    print(">>>>>>>> infer end to end")
    for m in model.modules():
        m.condconv = args.condconv
        m.tokestrel = "e2e"

    if args.cmd == "tokestrel":
        outputs = tokestrel(model, data, args, config)
    elif args.cmd == "todataset":
        outputs = todataset(model, data, data_info, args)
    # todo
#    if args.checkpoint:
#        batch = config.get("tokestrel", {}).get("batch_size",
#                                                {}).get("image", 1)
#        batch_results = dump_results(data_info[-batch:], outputs, config)
#        with open(os.path.join(args.outdir, "runner_result.txt"), "w") as f:
#            for image_results in batch_results:
#                for _ in image_results:
#                    f.write(json.dumps(_) + "\n")

if __name__ == "__main__":
    main()
