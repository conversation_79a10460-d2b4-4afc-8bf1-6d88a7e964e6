import numpy as np
import torch
from torch import nn

from pillar.models import MODELS


@MODELS.register_module()
class FieldLaneHeadBeta(nn.Module):

    def __init__(
            self,
            in_channel,
            num_classes,
            with_break_point=False,
            mask_property_group=[2, 13, 6, 6, 2],
            hm_idx=-1,
            mask_idx=0,
            regression=True,
            train_cfg=None,
            test_cfg=None,
            proposal_heatmap_thr=0.45,
            range_method="field",
            expand=1,
            with_start_point_off=False,
            with_start_point_feat_group=False,
            test_mode=False,
            norm_cfg=dict(type="BN2d", requires_grad=True),
    ):
        super().__init__()
        self.num_classes = num_classes
        self.hm_idx = hm_idx  #1
        self.mask_idx = mask_idx  #0
        self.regression = regression
        self.with_break_point = with_break_point
        self.with_start_point_off = with_start_point_off
        self.mask_property_group = mask_property_group
        self.test_mode = test_mode
        self.start_point_head = nn.Sequential(
            nn.Conv2d(in_channel,
                      in_channel * expand,
                      kernel_size=3,
                      padding=1),
            nn.ReLU(),
            nn.Conv2d(in_channel * expand,
                      num_classes,
                      kernel_size=3,
                      groups=num_classes if with_start_point_feat_group else 1,
                      padding=1),
        )
        if with_start_point_off:
            self.start_point_off_head = nn.Sequential(
                nn.Conv2d(in_channel,
                          in_channel * expand,
                          kernel_size=3,
                          padding=1),
                nn.ReLU(),
                nn.Conv2d(
                    in_channel * expand,
                    num_classes * 2,
                    kernel_size=3,
                    groups=num_classes if with_start_point_feat_group else 1,
                    padding=1),
            )
        if not isinstance(mask_property_group, dict):
            self.property_mask_head = nn.Sequential(
                nn.Conv2d(in_channel,
                          in_channel * expand,
                          kernel_size=3,
                          padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channel * expand,
                          sum(mask_property_group),
                          kernel_size=3,
                          padding=1),
            )
        else:
            for name in mask_property_group:
                out_dim, layer_index, head_expand = mask_property_group[name]
                head = nn.Sequential(
                    nn.Conv2d(in_channel,
                              in_channel * head_expand,
                              kernel_size=3,
                              padding=1),
                    nn.ReLU(),
                    nn.Conv2d(in_channel * head_expand,
                              out_dim,
                              kernel_size=3,
                              padding=1),
                )
                self.add_module(name, head)
        if with_break_point:
            self.break_point_head = nn.Sequential(
                # nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1),
                # nn.ReLU(),
                # nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1),
                # nn.BatchNorm2d(in_channels),
                nn.Conv2d(in_channel, in_channel, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channel, 1, kernel_size=3, padding=1),
            )
        self.proposal_heatmap_thr = proposal_heatmap_thr
        self.range_method = range_method
        assert range_method in ["field"]

    def maxpooling(self, heatmap, kernel=3):
        pad = (kernel - 1) // 2
        maxpool = nn.functional.max_pool2d(
            heatmap,
            (kernel, kernel),
            stride=1,
            padding=pad,
        )
        return maxpool

    def nms(self, heat, maxpool=None):
        if maxpool is None:
            maxpool = self.maxpooling(heat)
        heat = (heat >= maxpool).float() * heat
        return heat

    def ctdet_decode(self, heat, maxpool=None, thr=0.1):

        def _format(heat, inds):
            num_ins = [0] * heat.shape[0]
            ret = []
            for i, (n, c, y, x) in enumerate(zip(*inds)):
                id_class = c + 1
                coord = [x, y]
                score = heat[n, c, y, x]
                ret.append({
                    "coord": coord,
                    "id_class": id_class,
                    "score": score
                })
                num_ins[n] += 1

            return ret, num_ins

        heat_nms = self.nms(heat, maxpool)
        heat_nms = heat_nms.detach().cpu().numpy()
        inds = np.where(heat_nms > thr)
        seeds, num_ins = _format(heat_nms, inds)
        scores = heat_nms[inds[0], inds[1], inds[2], inds[3]]
        inds = torch.from_numpy(np.stack(inds, -1)).to(heat.device)
        scores = torch.from_numpy(scores).to(heat.device)
        return seeds, num_ins, inds, scores

    def proposal_head(self, inputs):
        x_list = list(inputs)
        f_hm = x_list[self.hm_idx]
        proposal_heatmap = self.start_point_head(f_hm)
        proposal_offset = None
        if self.with_start_point_off:
            proposal_offset = self.start_point_off_head(f_hm)
        if isinstance(self.mask_property_group, list):
            f_mask = x_list[self.mask_idx]
            aux_mask = self.property_mask_head(f_mask)
        else:
            aux_mask = {}
            for name in self.mask_property_group:
                _, layer_index, _ = self.mask_property_group[name]
                aux_mask[name] = (self.__getattr__(name)(x_list[layer_index]))
        if self.with_break_point:
            bk_mask = self.break_point_head(f_mask)
            aux_mask = torch.cat([aux_mask, bk_mask], dim=1)

        ret = {"proposal_heatmap": proposal_heatmap}
        if isinstance(self.mask_property_group, dict):
            if not self.training:
                attributes = torch.cat([
                    aux_mask.pop("laneline_type"),
                    aux_mask.pop("laneline_color"),
                    aux_mask.pop("roadside_type"),
                ], 1)
                ret["attributes"] = attributes
            ret.update(aux_mask)
        else:
            ret["aux_mask"] = aux_mask
        if proposal_offset is not None:
            ret["proposal_offset"] = proposal_offset
        return ret

    def forward_train(self, inputs):

        proposal_o = self.proposal_head(inputs)

        return proposal_o

    def forward_test(self, inputs):
        proposal_o = self.proposal_head(inputs)

        if getattr(self, "tokestrel", False):
            return proposal_o

        seeds, num_ins, pos_tensor, scores = self.ctdet_decode(
            torch.sigmoid(proposal_o["proposal_heatmap"]),
            None,
            thr=self.proposal_heatmap_thr,
        )

        return dict(proposal_indices=pos_tensor,
                    cls_score=scores,
                    **proposal_o)

    def inference_mask(self, pos):
        pass

    def forward(
        self,
        x_list,
        hm_thr=0.3,
    ):
        return self.forward_test(x_list, )

    def load_pretrained_dict(self, pretrained=None):
        renamed_dict = {}
        pretrained_dict = torch.load(pretrained,
                                     map_location="cpu")["state_dict"]
        for key, value in pretrained_dict.items():
            new_key = ".".join(key.split(".")[1:])
            renamed_dict[new_key] = value
        return renamed_dict

    def check_keys(self, own_keys, other_keys, own_name):
        own_keys = set(own_keys)
        other_keys = set(other_keys)
        shared_keys = own_keys & other_keys
        unexpected_keys = other_keys - own_keys
        missing_keys = own_keys - other_keys

        info = "Loading {}:{} shared keys, {} unexpected keys, {} missing keys.".format(
            own_name, len(shared_keys), len(unexpected_keys),
            len(missing_keys))

        if len(missing_keys) > 0:
            info += "\nmissing keys are as follows:\n    {}".format(
                "\n    ".join(missing_keys))
        print_log(info, logger="root")

    def init_weights(self, pretrained=None):
        if isinstance(pretrained, str):
            print_log(f"CondLaneHead: load model from: {pretrained}",
                      logger="root")
            pretrained_dict = self.load_pretrained_dict(pretrained)
            if "locations.y" in pretrained_dict.keys():
                _ = pretrained_dict.pop("locations.y")
                _ = pretrained_dict.pop("locations.x")
            self.load_state_dict(pretrained_dict, strict=False)
            self.check_keys(self.state_dict().keys(), pretrained_dict.keys(),
                            "CondLaneHead")
