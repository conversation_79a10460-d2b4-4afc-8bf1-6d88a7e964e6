"""
InceptionNeXt implementation, modified to produce 4 outputs of shapes:
[8, 64, 72, 216], [8, 64, 36, 108], [8, 128, 18, 54], [8, 128, 9, 27]
with input [8, 3, 288, 864].
"""

from functools import partial

import torch
import torch.nn as nn
import torch.utils.checkpoint as checkpoint
import torch.nn.functional as F

from timm.models.layers import trunc_normal_, DropPath
from timm.models.layers.helpers import to_2tuple

from mmengine.logging import MMLogger
from mmcv.runner.checkpoint import CheckpointLoader

from pillar.models import MODELS


class InceptionDWConv2d(nn.Module):
    """ Inception depthwise convolution. """
    def __init__(self, in_channels, square_kernel_size=3, band_kernel_size=11, branch_ratio=0.125):
        super().__init__()
        gc = int(in_channels * branch_ratio)  # channel per branch
        self.dwconv_hw = nn.Conv2d(gc, gc, square_kernel_size,
                                   padding=square_kernel_size // 2, groups=gc)
        self.dwconv_w = nn.Conv2d(gc, gc, kernel_size=(1, band_kernel_size),
                                  padding=(0, band_kernel_size // 2), groups=gc)
        self.dwconv_h = nn.Conv2d(gc, gc, kernel_size=(band_kernel_size, 1),
                                  padding=(band_kernel_size // 2, 0), groups=gc)
        self.split_indexes = (in_channels - 3 * gc, gc, gc, gc)

    def forward(self, x):
        x_id, x_hw, x_w, x_h = torch.split(x, self.split_indexes, dim=1)
        return torch.cat(
            (x_id,
             self.dwconv_hw(x_hw),
             self.dwconv_w(x_w),
             self.dwconv_h(x_h)),
            dim=1,
        )


class ConvMlp(nn.Module):
    """ MLP using 1x1 convs that keeps spatial dims. """
    def __init__(
            self, in_features, hidden_features=None, out_features=None,
            act_layer=nn.ReLU, norm_layer=None, bias=True, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        bias = to_2tuple(bias)

        self.fc1 = nn.Conv2d(in_features, hidden_features, kernel_size=1, bias=bias[0])
        self.norm = norm_layer(hidden_features) if norm_layer else nn.Identity()
        self.act = act_layer()
        self.drop = nn.Dropout(drop)
        self.fc2 = nn.Conv2d(hidden_features, out_features, kernel_size=1, bias=bias[1])

    def forward(self, x):
        x = self.fc1(x)
        x = self.norm(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        return x


class MlpHead(nn.Module):
    """ MLP classification head. """
    def __init__(self, dim, num_classes=1000, mlp_ratio=3,
                 act_layer=nn.GELU, norm_layer=partial(nn.LayerNorm, eps=1e-6),
                 drop=0., bias=True):
        super().__init__()
        hidden_features = int(mlp_ratio * dim)
        self.fc1 = nn.Linear(dim, hidden_features, bias=bias)
        self.act = act_layer()
        self.norm = norm_layer(hidden_features)
        self.fc2 = nn.Linear(hidden_features, num_classes, bias=bias)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        # global average pool
        x = x.mean((2, 3))
        x = self.fc1(x)
        x = self.act(x)
        x = self.norm(x)
        x = self.drop(x)
        x = self.fc2(x)
        return x


class MetaNeXtBlock(nn.Module):
    """ A single block. """
    def __init__(
            self,
            dim,
            token_mixer=nn.Identity,
            norm_layer=nn.BatchNorm2d,
            mlp_layer=ConvMlp,
            mlp_ratio=4,
            act_layer=nn.GELU,
            ls_init_value=1e-6,
            drop_path=0.,
    ):
        super().__init__()
        self.token_mixer = token_mixer(dim)
        self.norm = norm_layer(dim)
        self.mlp = mlp_layer(dim, int(mlp_ratio * dim), act_layer=act_layer)
        self.gamma = nn.Parameter(ls_init_value * torch.ones(dim)) if ls_init_value else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        shortcut = x
        x = self.token_mixer(x)
        x = self.norm(x)
        x = self.mlp(x)
        if self.gamma is not None:
            x = x.mul(self.gamma.reshape(1, -1, 1, 1))
        x = self.drop_path(x) + shortcut
        return x


class MetaNeXtStage(nn.Module):
    """A stage with optional downsampling + multiple MetaNeXtBlocks."""
    def __init__(
            self,
            in_chs,
            out_chs,
            ds_stride=2,
            depth=2,
            drop_path_rates=None,
            ls_init_value=1.0,
            token_mixer=nn.Identity,
            act_layer=nn.GELU,
            norm_layer=None,
            mlp_ratio=4,
    ):
        super().__init__()
        self.grad_checkpointing = False

        # Downsample if ds_stride=2, else identity
        if ds_stride > 1:
            self.downsample = nn.Sequential(
                norm_layer(in_chs),
                nn.Conv2d(in_chs, out_chs, kernel_size=ds_stride, stride=ds_stride),
            )
        else:
            self.downsample = nn.Sequential(
                norm_layer(in_chs),
                nn.Conv2d(in_chs, out_chs, kernel_size=1, stride=1),
            )

        drop_path_rates = drop_path_rates or [0.] * depth
        stage_blocks = []
        for i in range(depth):
            stage_blocks.append(MetaNeXtBlock(
                dim=out_chs,
                drop_path=drop_path_rates[i],
                ls_init_value=ls_init_value,
                token_mixer=token_mixer,
                act_layer=act_layer,
                norm_layer=norm_layer,
                mlp_ratio=mlp_ratio,
            ))
        self.blocks = nn.Sequential(*stage_blocks)

    def forward(self, x):
        # downsample if stride=2 or just conv if stride=1
        x = self.downsample(x)

        if self.grad_checkpointing and not torch.jit.is_scripting():
            x = checkpoint.checkpoint_sequential(self.blocks, len(self.blocks), x)
        else:
            x = self.blocks(x)
        return x


@MODELS.register_module()
class MetaNeXt(nn.Module):
    """
    A PyTorch impl of InceptionNeXt, returning 4 feature maps of shapes:
      [B, 64,   72, 216]  (Stage 0)
      [B, 64,   36, 108]  (Stage 1)
      [B, 128,  18, 54 ]  (Stage 2)
      [B, 128,   9, 27 ]  (Stage 3)

    with an input of size [B, 3, 288, 864].
    """

    def __init__(
            self,
            in_chans=3,
            num_classes=1000,
            depths=(2, 2, 2, 2),           # Example small
            dims=(96, 192, 384, 768),
            token_mixers=InceptionDWConv2d,
            norm_layer=nn.BatchNorm2d,
            act_layer=nn.GELU,
            mlp_ratios=(4, 4, 4, 4),
            head_fn=MlpHead,
            drop_rate=0.,
            drop_path_rate=0.,
            ls_init_value=1e-6,
            init_cfg=None,
            **kwargs,
    ):
        super().__init__()
        num_stages = len(depths)
        if not isinstance(token_mixers, (list, tuple)):
            token_mixers = [token_mixers] * num_stages
        if not isinstance(mlp_ratios, (list, tuple)):
            mlp_ratios = [mlp_ratios] * num_stages

        self.init_cfg = init_cfg
        self.num_classes = num_classes
        self.drop_rate = drop_rate

        # --------------------
        # Stem: stride=4
        # [3, 288,864] -> [96, 72,216]
        # --------------------
        self.stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            norm_layer(dims[0]),
            act_layer()
        )

        # ---------------
        # 4 Stages
        #  - Stage0: ds_stride=1 => total stride=4*1=4 => final shape [96,72,216]
        #  - Stage1: ds_stride=2 => total stride=8 =>    shape [192,36,108]
        #  - Stage2: ds_stride=2 => total stride=16 =>   shape [384,18,54]
        #  - Stage3: ds_stride=2 => total stride=32 =>   shape [768,9,27]
        # ---------------
        dp_rates_all = torch.linspace(0, drop_path_rate, sum(depths))
        dp_rates_per_stage = [x.tolist() for x in dp_rates_all.split(depths)]

        self.stages = nn.ModuleList()
        prev_ch = dims[0]
        for i in range(num_stages):
            out_ch = dims[i]
            # Stage0 => ds_stride=1
            # Stages1-3 => ds_stride=2
            ds_stride = 1 if i == 0 else 2

            stage = MetaNeXtStage(
                in_chs=prev_ch,
                out_chs=out_ch,
                ds_stride=ds_stride,
                depth=depths[i],
                drop_path_rates=dp_rates_per_stage[i],
                ls_init_value=ls_init_value,
                token_mixer=token_mixers[i],
                act_layer=act_layer,
                norm_layer=norm_layer,
                mlp_ratio=mlp_ratios[i],
            )
            self.stages.append(stage)
            prev_ch = out_ch

        # --------------------------------------------
        # Transitions to final channels [64,64,128,128]
        # --------------------------------------------
        self.transition_layers = nn.ModuleList([
            nn.Conv2d(dims[0],  64, kernel_size=1),   # stage0 => [64,72,216]
            nn.Conv2d(dims[1],  64, kernel_size=1),   # stage1 => [64,36,108]
            nn.Conv2d(dims[2], 128, kernel_size=1),   # stage2 => [128,18,54]
            nn.Conv2d(dims[3], 128, kernel_size=1),   # stage3 => [128,9,27]
        ])

        # Optional classification head (unused in many detection tasks)
        self.head = head_fn(128, num_classes, drop=drop_rate)

    def forward_features(self, x):
        # Stem => shape [B, 96, 72, 216]
        x = self.stem(x)

        # Stage 0 => [B, 96, 72, 216],  transition => [B,64,72,216]
        # Stage 1 => [B,192,36,108],    transition => [B,64,36,108]
        # Stage 2 => [B,384,18,54],     transition => [B,128,18,54]
        # Stage 3 => [B,768,9,27],      transition => [B,128,9,27]
        outs = []
        for i, stage in enumerate(self.stages):
            x = stage(x)
            out = self.transition_layers[i](x)
            outs.append(out)

        # outs => 4 feature maps
        return outs

    def forward(self, x):
        feats = self.forward_features(x)
        # Debug shapes
#        for i, feat in enumerate(feats):
#            print(f"Stage {i} output: {feat.shape}")
        return tuple(feats)

    def init_weights(self, pretrained=None):
        logger = MMLogger.get_current_instance()
        if self.init_cfg is None:
            logger.warn(f'No pre-trained weights for {self.__class__.__name__}, training from scratch')
            # Example of default init
            for m in self.modules():
                if isinstance(m, (nn.Conv2d, nn.Linear)):
                    trunc_normal_(m.weight, std=.02)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
        else:
            # Load partial checkpoint
            assert 'checkpoint' in self.init_cfg, (
                "Only support specifying a `checkpoint` key in init_cfg for "
                f"{self.__class__.__name__}"
            )
            ckpt = CheckpointLoader.load_checkpoint(
                self.init_cfg['checkpoint'],
                logger=logger,
                map_location='cpu'
            )
            # Some checkpoints might store under 'state_dict' or 'model'
            if 'state_dict' in ckpt:
                state_dict = ckpt['state_dict']
            elif 'model' in ckpt:
                state_dict = ckpt['model']
            else:
                state_dict = ckpt

            # remove head keys if mismatch
            removed_keys = []
            for key in list(state_dict.keys()):
                if key.startswith('head.'):
                    removed_keys.append(key)
                    del state_dict[key]
            if removed_keys:
                logger.info(f"Removed these head parameters: {removed_keys}")

            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warn(f"[MetaNeXt] Missing keys: {missing_keys}")
            if unexpected_keys:
                logger.warn(f"[MetaNeXt] Unexpected keys: {unexpected_keys}")

