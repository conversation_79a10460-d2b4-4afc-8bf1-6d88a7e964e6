import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from pillar.models import MODELS
from mmengine.logging import <PERSON><PERSON><PERSON><PERSON>
from mmcv.runner.checkpoint import CheckpointLoader


class InvertedResidual(nn.Module):
    def __init__(self, inp, oup, stride, expand_ratio):
        super(InvertedResidual, self).__init__()
        self.stride = stride
        assert stride in [1, 2]

        self.use_res_connect = self.stride == 1 and inp == oup

        self.conv = nn.Sequential(
            # pointwise convolution
            nn.Conv2d(in_channels=inp, out_channels=inp * expand_ratio,
                      kernel_size=1, stride=1, padding=0, bias=False),
            nn.BatchNorm2d(inp * expand_ratio),
            nn.ReLU6(inplace=True),
            # depthwise convolution via groups
            nn.Conv2d(in_channels=inp * expand_ratio, out_channels=inp * expand_ratio,
                      kernel_size=3, stride=stride, padding=1, groups=inp * expand_ratio, bias=False),
            nn.BatchNorm2d(inp * expand_ratio),
            nn.ReLU6(inplace=True),
            # pointwise linear convolution
            nn.Conv2d(in_channels=inp * expand_ratio, out_channels=oup,
                      kernel_size=1, stride=1, padding=0, bias=False),
            nn.BatchNorm2d(oup),
        )

    def forward(self, x):
        if self.use_res_connect:
            return x + self.conv(x)
        else:
            return self.conv(x)

@MODELS.register_module()
class MobileNetV2_dynamicFPN(nn.Module):
    def __init__(self, width_mult=1., init_cfg=None):
        super(MobileNetV2_dynamicFPN, self).__init__()

        self.input_channel = int(32 * width_mult)
        self.width_mult = width_mult
        self.init_cfg = init_cfg

        # First layer
        self.first_layer = nn.Sequential(
            nn.Conv2d(3, self.input_channel, kernel_size=3, stride=2, padding=1, bias=False),
            nn.BatchNorm2d(self.input_channel),
            nn.ReLU6(inplace=True)
        )

        # Inverted residual blocks (each n layers)
        self.inverted_residual_setting = [
            {'expansion_factor': 1, 'width_factor': 16, 'n': 1, 'stride': 1},
            {'expansion_factor': 6, 'width_factor': 24, 'n': 2, 'stride': 2},
            {'expansion_factor': 6, 'width_factor': 32, 'n': 3, 'stride': 2},
            {'expansion_factor': 6, 'width_factor': 64, 'n': 4, 'stride': 2},
            {'expansion_factor': 6, 'width_factor': 96, 'n': 3, 'stride': 1},
            {'expansion_factor': 6, 'width_factor': 160, 'n': 3, 'stride': 2},
            {'expansion_factor': 6, 'width_factor': 320, 'n': 1, 'stride': 1},
        ]
        self.inverted_residual_blocks = nn.ModuleList(
            [self._make_inverted_residual_block(**setting)
             for setting in self.inverted_residual_setting])

        # reduce feature maps to one pixel
        # allows to upsample semantic information of every part of the image
        self.average_pool = nn.AdaptiveAvgPool2d(1)

        # Top layer
        # input channels = last width factor
        self.top_layer = nn.Conv2d(
            int(self.inverted_residual_setting[-1]['width_factor'] * self.width_mult),
            256, kernel_size=1, stride=1, padding=0)

        # Lateral layers
        # exclude last setting as this lateral connection is the the top layer
        # build layer only if resulution has decreases (stride > 1)
        self.lateral_setting = [setting for setting in self.inverted_residual_setting[:-1]
                                if setting['stride'] > 1]
        self.lateral_layers = nn.ModuleList([
            nn.Conv2d(int(setting['width_factor'] * self.width_mult),
                      256, kernel_size=1, stride=1, padding=0)
            for setting in self.lateral_setting])

        # Smooth layers
        # n = lateral layers + 1 for top layer
        self.smooth_layers = nn.ModuleList([nn.Conv2d(256, 256, kernel_size=3, stride=1, padding=1)] *
                                           (len(self.lateral_layers) + 1))
        
        # **Transition layers** to modify output channels
        self.transition_layers = nn.ModuleList([
            nn.Conv2d(256, 64, kernel_size=1, stride=1, padding=0),  # For 72x216
            nn.Conv2d(256, 64, kernel_size=1, stride=1, padding=0),  # For 36x108
            nn.Conv2d(256, 128, kernel_size=1, stride=1, padding=0), # For 18x54
            nn.Conv2d(256, 128, kernel_size=1, stride=1, padding=0), # For 9x27
        ])

        #self._initialize_weights()

    def _make_inverted_residual_block(self, expansion_factor, width_factor, n, stride):
        inverted_residual_block = []
        output_channel = int(width_factor * self.width_mult)
        for i in range(n):
            # except the first layer, all layers have stride 1
            if i != 0:
                stride = 1
            inverted_residual_block.append(
                InvertedResidual(self.input_channel, output_channel, stride, expansion_factor))
            self.input_channel = output_channel

        return nn.Sequential(*inverted_residual_block)

    def _upsample_add(self, x, y):
        '''Upsample and add two feature maps.
        Args:
          x: (Variable) top feature map to be upsampled.
          y: (Variable) lateral feature map.
        Returns:
          (Variable) added feature map.
        Note in PyTorch, when input size is odd, the upsampled feature map
        with `F.upsample(..., scale_factor=2, mode='nearest')`
        maybe not equal to the lateral feature map size.
        e.g.
        original input size: [N,_,15,15] ->
        conv2d feature map size: [N,_,8,8] ->
        upsampled feature map size: [N,_,16,16]
        So we choose bilinear upsample which supports arbitrary output sizes.
        '''
        _, _, H, W = y.size()
        return F.upsample(x, size=(H, W), mode='bilinear', align_corners=False) + y

    def init_weights(self, pretrained=None):
        if self.init_cfg is None:
            for m in self.modules():
                if isinstance(m, nn.Conv2d):
                    n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                    m.weight.data.normal_(0, math.sqrt(2. / n))
                    if m.bias is not None:
                        m.bias.data.zero_()
                elif isinstance(m, nn.BatchNorm2d):
                    m.weight.data.fill_(1)
                    m.bias.data.zero_()
                elif isinstance(m, nn.Linear):
                    n = m.weight.size(1)
                    m.weight.data.normal_(0, 0.01)
                    m.bias.data.zero_()

        else:
            # load checkpoint
            assert 'checkpoint' in self.init_cfg, (
                "Only support specifying a `checkpoint` key in init_cfg for "
                f"{self.__class__.__name__}"
            )
            ckpt = CheckpointLoader.load_checkpoint(
                self.init_cfg['checkpoint'],
                logger=logger,
                map_location='cpu'
            )
            # some checkpoints might store under 'state_dict' or 'model'
            if 'state_dict' in ckpt:
                state_dict = ckpt['state_dict']
            elif 'model' in ckpt:
                state_dict = ckpt['model']
            else:
                state_dict = ckpt

            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warn(f"[MobileNet] Missing keys: {missing_keys}")
            if unexpected_keys:
                logger.warn(f"[MobileNet] Unexpected keys: {unexpected_keys}")

    def forward(self, x):
        # Bottom-up pathway
        x = self.first_layer(x)
        lateral_tensors = []
        n_lateral_connections = 0

        for i, block in enumerate(self.inverted_residual_blocks):
            output = block(x)
            if self.inverted_residual_setting[i]['stride'] > 1 \
                    and n_lateral_connections < len(self.lateral_layers):
                lateral_tensors.append(self.lateral_layers[n_lateral_connections](output))
                n_lateral_connections += 1
            x = output

        # Top-down pathway and FPN connections
        m_layers = [self.top_layer(x)]
        lateral_tensors.reverse()

        for lateral_tensor in lateral_tensors:
            m_layers.append(self._upsample_add(m_layers[-1], lateral_tensor))

        # Smooth layers
        p_layers = [smooth_layer(m_layer) for smooth_layer, m_layer in zip(self.smooth_layers, m_layers)]

        # Resize outputs to match required dimensions
        output_shapes = [
            (72, 216), (36, 108), (18, 54), (9, 27)
        ]
        p_layers_resized = [
            F.interpolate(p, size=shape, mode='bilinear', align_corners=False)
            for p, shape in zip(p_layers, output_shapes)
        ]
        
        # Apply transition layers to adjust channel dimensions
        final_outputs = [trans_layer(p) for trans_layer, p in zip(self.transition_layers, p_layers_resized)]

#        for each_output in final_outputs:
#            print(each_output.shape)

        return final_outputs
