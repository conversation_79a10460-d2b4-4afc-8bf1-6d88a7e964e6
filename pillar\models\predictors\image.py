from mmcls.models.classifiers import ImageClassifier as _ImageClassifier
from mmcls.models.heads import MultiLabelClsHead
import numpy as np

from ..builder import MODELS


@MODELS.register_module()
class ImageClassifier(_ImageClassifier):
    def forward_dummy(self, img, **kwargs):
        """Inference for deployment"""
        x = self.extract_feat(img)

        try:
            if isinstance(self.head, MultiLabelClsHead):
                assert 'softmax' not in kwargs, (
                    'Please use `sigmoid` instead of `softmax` '
                    'in multi-label tasks.')
            res = self.head.forward_dummy(x, **kwargs)
        except TypeError as e:
            if 'not tuple' in str(e) and self.return_tuple:
                return TypeError(
                    'Seems the head cannot handle tuple input. We have '
                    'changed all backbones\' output to a tuple. Please '
                    'update your custom head\'s forward function. '
                    'Temporarily, you can set "return_tuple=False" in '
                    'your backbone config to disable this feature.')
            raise e

        return res

    @staticmethod
    def post_process(scores):
        """Post-process for inference result"""
        plain_struc = not isinstance(scores, dict)
        if plain_struc:
            scores = dict(logit=scores)
        results = dict()
        for attr, score in scores.items():
            if isinstance(score, list):
                score = np.stack(score)
            # plain result dict
            pred_score = np.max(score, axis=1)
            results[f'{attr}_pred_score'] = pred_score
            pred_label = np.argmax(score, axis=1)
            results[f'{attr}_pred_label'] = pred_label
        return results

    def forward_test(self, *args, **kwargs):
        """Test for post-process"""
        post_process = kwargs.pop('post_process', None)

        res = super().forward_test(*args, **kwargs)

        if post_process:
            res = self.post_process(res)
        return res
