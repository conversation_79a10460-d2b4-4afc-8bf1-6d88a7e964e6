# sh slurm_train.sh ad_rd 128
#!/usr/bin/env bash


set -x

now=$(date +"%Y%m%d_%H%M%S")

## 123
CONFIG="./configs/config_r331_one_stage_11.6MB_finetuning.py"

WORK_DIR="./condlane_train/"

ROOT="/home/<USER>/condlane_new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage"

export PYTHONPATH=$ROOT:$ROOT/functions:$PYTHONPATH

python -u $ROOT/tools/train.py ${CONFIG}             \
	 --work-dir=${WORK_DIR} --launcher="none" 2>&1 | tee "${WORK_DIR}/train_${now}.log"

