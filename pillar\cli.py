import os
import re
import queue
import random
import subprocess
import threading
import time
import click


def echo_warning(msg):
    """echo warning"""
    click.echo(click.style(msg, fg='yellow'))  # type: ignore


def highlighted_error(msg):
    """highlight error"""
    return click.style(msg, fg='red', bold=True)  # type: ignore


def echo_success(msg):
    """echo success"""
    click.echo(click.style(msg, fg='green'))  # type: ignore


def echo_error(msg):
    """echo error"""
    click.echo(click.style(msg, fg='red'))  # type: ignore


PILLAR_ROOT = os.path.dirname(__file__)
PLUGIN_ROOT = os.path.join(PILLAR_ROOT, '.plugin')
if not os.path.exists(PLUGIN_ROOT):
    raise RuntimeError(highlighted_error('Please install pillar first.'))
FUNCTION_ROOT = os.path.join(PLUGIN_ROOT, 'functions')
if not os.path.exists(FUNCTION_ROOT):
    raise RuntimeError(highlighted_error(
        'Please install pillar\'s function first.'))
# for custom_imports
os.environ['PYTHONPATH'] = f'{FUNCTION_ROOT}:{PILLAR_ROOT}:' + \
    os.environ.get('PYTHONPATH', '')

# to avoid checking git repository
os.environ['GIT_DISCOVERY_ACROSS_FILESYSTEM'] = "1"

COMMAND_SETTINGS = dict(add_help_option=False, context_settings=dict(
    ignore_unknown_options=True, show_default=False))


@click.group(name='dist_group')
def cli_dist():
    """Distributed command-line interface"""


FUNCTION_TYPE = click.Choice(list(filter(lambda func_name: not func_name.startswith(
    '__'), os.listdir(FUNCTION_ROOT))), case_sensitive=False)
LAUNCHER_TYPE = click.Choice(
    ['none', 'pytorch', 'slurm'], case_sensitive=False)


PREFIXES = ['http://', 'https://', 'pavi://', r'(\S+\:)?s3://', 'modelzoo://',
            'torchvision://', 'open-mmlab://', 'openmmlab://', 'mmcls://']
def is_cloud_file(file_path):
    for prefix in PREFIXES:
        if re.match(prefix, file_path) is not None:
            return True
    return False


def __parse_file_path(file_path, root_dir=None):
    if file_path is None:
        basename = None
        echo_warning('Filename is not given and the default one will be used')
    else:
        if os.path.exists(file_path) or is_cloud_file(file_path):
            return file_path
        basename = os.path.basename(file_path)
        if basename != file_path:
            echo_warning(f'File {file_path} does not exist and '
                         f'{basename} will be retrieved under {root_dir}')
    if root_dir is None:
        raise ValueError(highlighted_error(
            f'Root directory is required when {file_path} does not exist'))
    assert os.path.isdir(root_dir), 'invalid root folder'
    targets = list()
    for root, dirs, files in os.walk(root_dir):
        for filename in files:
            if basename is not None and filename != basename:
                continue
            filepath = os.path.join(root, filename)
            if not os.path.isfile(filepath):
                continue
            targets.append(filepath)
    num_tgt = len(targets)
    if num_tgt == 0:
        raise ValueError(highlighted_error(
            f'No file named {basename} is found under {root_dir}'))
    elif num_tgt == 1:
        return targets[0]
    else:
        # if there are multiple matching results, select which end with filename
        echo_warning(f'Multiple files {targets} are retrieved under {root_dir}')
        for target in targets:
            if target.endswith(filename):
                return target
        return targets[0]


def __parse_func_name(func_name, cmd_path, help_flag):
    ctx = click.get_current_context()
    if func_name is None:
        if help_flag:
            click.echo(ctx.get_help(), color=ctx.color)
            ctx.exit()
        if len(FUNCTION_TYPE.choices) > 1:
            click.echo(ctx.get_help(), color=ctx.color)
            ctx.exit()
        echo_warning('Argument [function] is not specific and '
                     f'will be set as [{FUNCTION_TYPE.choices[0]}]')
        func_name = FUNCTION_TYPE.choices[0]
    elif help_flag:
        with subprocess.Popen(['python', cmd_path, '--help']) as proc:
            proc.wait()
        ctx.exit()
    return func_name


PHASE_TYPE = click.Choice(list(cli_dist.commands.keys()))


def __generate_dist_cmd(
        phase: PHASE_TYPE, launcher: LAUNCHER_TYPE, port: int, other_args: tuple,
        common_cmd: list, cfg_path: str,
        partition: str, gpus: int, nodes: int, gpus_per_node: int,
        cpus_per_task: int, srun_args: str):
    if gpus_per_node is None:
        gpus_per_node = gpus
    _nodes = float(gpus) / gpus_per_node
    if not _nodes.is_integer():
        raise ValueError(highlighted_error(
            f'Invalid arguments: GPUS {gpus} and GPUS_PER_NODE {gpus_per_node}'))
    if nodes is None:
        nodes = int(_nodes)
    elif nodes != _nodes:
        raise ValueError(highlighted_error(
            f'Invalid argument: NODES {nodes}'))

    if launcher == 'none':
        if nodes > 1:
            raise ValueError(highlighted_error(
                'Multi-node mode is not allowed when launcher=none'))
        if phase == 'train':
            if any(['--gpus' in arg or '--gpu-ids' in arg for arg in other_args]):
                echo_warning(f'Argument [gpus] {gpus} is not employed '
                             'for gpu setting contained in other_args')
            else:
                common_cmd.extend(['--gpu-ids', *map(str, range(gpus))])
        else:  # test|infer
            echo_warning(
                f'Argument [gpus] {gpus} is not required for command {phase}')
        cmd = ['python'] + common_cmd
    elif launcher == 'pytorch':
        cmd = ['python', '-m', 'torch.distributed.launch', f'--nnodes={nodes}',
               f'--nproc_per_node={gpus_per_node}', f'--master_port={port}'] + common_cmd
    elif launcher == 'slurm':
        if partition is None:
            raise ValueError(highlighted_error(
                f'Invalid argument: PARTITION {partition}'))
        srun_args = srun_args.split()
        if not any(['-J' in arg or '--job-name' in arg for arg in srun_args]):
            job_name = os.path.splitext(os.path.basename(cfg_path))[0]
            srun_args.append(f'--job-name={job_name}')
        cmd = ['srun', f'--partition={partition}', f'--ntasks={gpus}', f'--nodes={nodes}',
               f'--gres=gpu:{gpus_per_node}', f'--ntasks-per-node={gpus_per_node}',
               f'--cpus-per-task={cpus_per_task}', '--kill-on-bad-exit=1',
               *srun_args, 'python', '-u'] + common_cmd
    return cmd


def dist_decorator(func):
    """"Build Decorator for Distributed Commands."""
    @click.option('-h', '--help', is_flag=True, help='show this message and exit')
    @click.option(
        '-t', '--tag', type=str, envvar='TAG', help='tag used to mark this experiment',
        default=time.strftime('%Y%m%d_%H%M%S', time.localtime()))
    @click.option(
        '-o', '--work-dir', '--out-dir', type=click.Path(writable=True), envvar='WORK_DIR',
        default=os.path.join(os.getcwd(), 'work_dirs'), help='directory to save outputs')
    @click.option(
        '-C', '--config', '--cfg', type=str, envvar='CONFIG', help='config file')
    @click.option(
        '-M', '--checkpoint', '--ckpt', type=str, envvar='CHECKPOINT', help='checkpoint file')
    @click.option(
        '-a', '--algorithm-backend', type=str, envvar='ALGORITHM_BACKEND', default='OpenMMLab',
        help='algorithm library backend')
    @click.option(
        '-l', '--launcher', type=LAUNCHER_TYPE, envvar='LAUNCHER',
        default='slurm', help='job launcher')
    @click.option(
        '-p', '--port', type=int, envvar='PORT', default=random.randint(20000, 30000),
        help='port used for synchronization')
    @click.option(
        '-P', '--partition', type=str, envvar='PARTITION', help='partition to use')
    @click.option(
        '-G', '--gpus', type=int, envvar='GPUS', default=8,
        help='number of gpus to use (world size)')
    @click.option(
        '-n', '--nodes', '--nnodes', type=int, envvar='NODES', help='number of nodes to use')
    @click.option(
        '-g', '--gpus-per-node', type=int, envvar='GPUS_PER_NODE',
        help='number of gpus per node (local world size)')
    @click.option(
        '-c', '--cpus-per-task', type=int, envvar='CPUS_PER_TASK', default=5,
        help='number of cpus per task')
    @click.option(
        '-s', '--srun-args', type=str, envvar='SRUN_ARGS', default='',
        help='other srun arguments that might be used')
    @click.option(
        '-d', '--debug', is_flag=True, help='return command line without calling')
    #: function name
    @click.argument('function', type=FUNCTION_TYPE, required=False)
    @click.argument('other_args', nargs=-1, type=click.UNPROCESSED, required=False)
    def wrapper(function: FUNCTION_TYPE, help: bool, debug: bool,
                tag: str, work_dir: str, config: str, checkpoint: str,
                algorithm_backend: str, launcher: LAUNCHER_TYPE, port: int,
                other_args: tuple = (), **kwargs):
        phase = func.__name__
        cmd_path = os.path.relpath(os.path.join(
            PLUGIN_ROOT, 'tools', f'{phase}.py'), os.getcwd())
        function = __parse_func_name(function, cmd_path, help)
        func_root = os.path.join(PLUGIN_ROOT, 'functions', function)
        click.echo(f'Function Root Path: {func_root}')

        cfg_path = __parse_file_path(
            config, root_dir=os.path.join(func_root, 'configs'))
        common_cmd = [cmd_path, cfg_path]
        if phase in ['test', 'infer']:
            common_cmd.append(__parse_file_path(
                checkpoint, root_dir=os.path.join(func_root, 'checkpoint')))
        elif checkpoint is not None:
            ckpt_path = __parse_file_path(
                checkpoint, root_dir=os.path.join(func_root, 'checkpoint'))
            if any(['--resume-from' in arg for arg in other_args]):
                echo_warning(
                    f'Checkpoint file {ckpt_path} is not employed '
                    'for resume-from argument contained in other_args')
            else:
                common_cmd.append(f'--resume-from={ckpt_path}')
        common_cmd.extend(
            [f'--work-dir={os.path.join(work_dir, tag)}', f'--launcher={launcher}',
             f'--algorithm-backend={algorithm_backend}', f'--function={function}',
             *other_args])

        cmd = __generate_dist_cmd(
            phase, launcher, port, other_args, common_cmd, cfg_path, **kwargs)
        echo_success('Command Line: ' + ' '.join(cmd))
        if debug:
            return cmd
        exitcode = subprocess.check_call(cmd, env=dict(
            os.environ, MASTER_PORT=str(port)))
        if exitcode == 0:
            # type: ignore
            echo_success(
                f'Function [{function}] (phase={phase}) finished successfully.')
            return cmd
        echo_error(
            f'Function [{function}] (phase={phase}) not finished successfully.')

    wrapper.__doc__ = func.__doc__
    return wrapper


@cli_dist.command(name='train', **COMMAND_SETTINGS)
@dist_decorator
def train():
    """Perform Distributed Training."""


@cli_dist.command(name='test', **COMMAND_SETTINGS)
@dist_decorator
def test():
    """Perform Distributed Testing."""


@cli_dist.command(name='infer', **COMMAND_SETTINGS)
@dist_decorator
def infer():
    """Perform Distributed Inference."""


@click.group(name='unf_group')
def cli_unf():
    """Unified command-line interface"""


def __parse_command_mapper(cmd_root: str, ignore_cmds: list):
    ignore_cmds = set(ignore_cmds)
    cmd_mapper = dict()
    for rootpath, _, filelist in os.walk(cmd_root):
        for filename in filelist:
            if not filename.endswith('.py'):
                continue
            command = os.path.splitext(filename)[0]
            if command in ignore_cmds:
                continue
            cmd_mapper[command] = os.path.relpath(
                os.path.join(rootpath, filename), os.getcwd())
    return cmd_mapper


COMMAND_MAPPER = __parse_command_mapper(cmd_root=os.path.join(
    PLUGIN_ROOT, 'tools'), ignore_cmds=list(cli_dist.commands.keys()))
COMMAND_TYPE = click.Choice(list(COMMAND_MAPPER.keys()), case_sensitive=False)


def __redirect_message(file: str, pipes: list):
    if not pipes:
        return None
    _open_f = isinstance(file, str)
    if _open_f:
        file = open(file, 'w', encoding='utf-8')

    def readline(_pipe, _queue):
        try:
            with _pipe:
                for msg in iter(_pipe.readline, b''):
                    _queue.put(msg.decode('utf-8').rstrip())
        finally:
            _queue.put(None)

    msg_queue = queue.Queue()
    for pipe in pipes:
        threading.Thread(target=readline, args=(pipe, msg_queue)).start()
    for _ in range(len(pipes)):
        for msg in iter(msg_queue.get, None):
            click.echo(msg)
            file.write(f'{msg}\n')
    if not _open_f:
        return None
    file.close()

def __generate_unf_cmd(
        command: COMMAND_TYPE, launcher: LAUNCHER_TYPE, other_args: tuple,
        common_cmd: list, partition: str, gpus: int, nodes: int, gpus_per_node: int,
        cpus_per_task: int, srun_args: str):
    if gpus_per_node is None:
        gpus_per_node = gpus
    _nodes = float(gpus) / gpus_per_node
    if not _nodes.is_integer():
        raise ValueError(highlighted_error(
            f'Invalid arguments: GPUS {gpus} and GPUS_PER_NODE {gpus_per_node}'))
    if nodes is None:
        nodes = int(_nodes)
    elif nodes != _nodes:
        raise ValueError(highlighted_error(
            f'Invalid argument: NODES {nodes}'))
    
    if launcher == 'none':
        cmd = ['python'] + common_cmd
    elif launcher == 'slurm':
        if partition is None:
            raise ValueError(highlighted_error(
                f'Invalid argument: PARTITION {partition}'))
        srun_args = srun_args.split()

        cmd = ['srun', f'--partition={partition}', f'--ntasks={gpus}', f'--nodes={nodes}',
               f'--gres=gpu:{gpus_per_node}', f'--ntasks-per-node={gpus_per_node}',
               f'--cpus-per-task={cpus_per_task}', '--kill-on-bad-exit=1',
               *srun_args, 'python', '-u'] + common_cmd
    return cmd

def unf_decorator(func):
    """"Build Decorator for Unified Commands."""
    @click.option('-h', '--help', is_flag=True, help='show this message and exit')
    @click.option(
        '-t', '--tag', type=str, envvar='TAG', help='tag used to mark this experiment',
        default=time.strftime('%Y%m%d_%H%M%S', time.localtime()))
    @click.option(
        '-o', '--work-dir', '--out-dir', type=click.Path(writable=True), envvar='WORK_DIR',
        default=os.path.join(os.getcwd(), 'work_dirs'), help='directory to save outputs')
    @click.option(
        '-d', '--debug', is_flag=True, help='return command line without calling')
    @click.option(
        '-l', '--launcher', type=LAUNCHER_TYPE, envvar='LAUNCHER',
        default='none', help='job launcher')
    @click.option(
        '-P', '--partition', type=str, envvar='PARTITION', help='partition to use')
    @click.option(
        '-G', '--gpus', type=int, envvar='GPUS', default=8,
        help='number of gpus to use (world size)')
    @click.option(
        '-n', '--nodes', '--nnodes', type=int, envvar='NODES', help='number of nodes to use')
    @click.option(
        '-g', '--gpus-per-node', type=int, envvar='GPUS_PER_NODE',
        help='number of gpus per node (local world size)')
    @click.option(
        '-c', '--cpus-per-task', type=int, envvar='CPUS_PER_TASK', default=5,
        help='number of cpus per task')
    @click.option(
        '-s', '--srun-args', type=str, envvar='SRUN_ARGS', default='',
        help='other srun arguments that might be used')
    #: command name
    @click.argument('command', type=COMMAND_TYPE, required=False)
    @click.argument('other_args', nargs=-1, type=click.UNPROCESSED, required=False)
    def wrapper(command: COMMAND_TYPE, help: bool, debug: bool,
                tag: str, work_dir: str, launcher: LAUNCHER_TYPE, 
                other_args: tuple = (), **kwargs):
        ctx = click.get_current_context()
        if command is None:
            click.echo(ctx.get_help(), color=ctx.color)
            ctx.exit()
        elif help:
            with subprocess.Popen(['python', COMMAND_MAPPER[command], '--help']) as proc:
                proc.wait()
            ctx.exit()
        common_cmd = [COMMAND_MAPPER[command], *other_args]
        cmd = __generate_unf_cmd(
            command, launcher, other_args, common_cmd, **kwargs)
        echo_success('Command Line: ' + ' '.join(cmd))
        logfile = os.path.join(work_dir, f'{command}_{tag}.log')
        click.echo('Logfile: ' + os.path.relpath(logfile, os.getcwd()))
        if debug:
            return cmd
        os.makedirs(work_dir, exist_ok=True)
        with subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                env=os.environ, bufsize=1) as proc:
            __redirect_message(file=logfile, pipes=[proc.stdout, proc.stderr])
            exitcode = proc.wait()
        if exitcode == 0:
            # type: ignore
            echo_success(f'Command [{command}] finished successfully.')
            return cmd
        echo_error(f'Command [{command}] not finished successfully.')

    wrapper.__doc__ = func.__doc__
    return wrapper


@cli_unf.command(name='run', **COMMAND_SETTINGS)
@unf_decorator
def run():
    """Perform Unified Command."""


def __locate_func(ctx, param, value):
    if not value or ctx.resilient_parsing:
        return
    click.echo(
        f'Pillar Path: {os.path.realpath(os.path.abspath(PILLAR_ROOT))}', color=ctx.color)
    ctx.exit()


@click.command(cls=click.CommandCollection, sources=[cli_dist, cli_unf])
@click.help_option('-h', '--help')
@click.version_option(None, '-v', '--version')
@click.option('-l', '--locate', is_flag=True, expose_value=False, is_eager=True,
              help='Locate pillar source code.', callback=__locate_func)
def cli():
    """Pillar Command Line Interface.
    """


if __name__ == '__main__':
    cli()
