import os

from mmcv import FileClient


ANNOTATION_EXTS = set(['.json', '.pkl', '.npy', '.npz', '.yml'])
DATA_EXTS = set(['.jpg', '.jpeg', '.png', '.pcd', '.bin'])


def digit_version(version_str):
    digit_version = []
    for x in version_str.split('.'):
        if x.isdigit():
            digit_version.append(int(x))
        elif x.find('rc') != -1:
            patch_version = x.split('rc')
            digit_version.append(int(patch_version[0]) - 1)
            digit_version.append(int(patch_version[1]))
    return digit_version


def infer_client_from_uri(uri, petrel_conf_path=None):
    file_prefix = FileClient.parse_uri_prefix(uri)
    if file_prefix == 's3' and petrel_conf_path is not None:
        return FileClient(prefix=file_prefix, conf_path=petrel_conf_path)
    return FileClient(prefix=file_prefix)


def generate_datalist(data, img_root, petrel_conf_path=None):
    """
        generate data list
    """
    if data is None:
        return None

    data_list, anno_num = list(), 0
    for _data in data:
        # image file
        if os.path.splitext(_data)[1] in DATA_EXTS:
            data_list.append(_data)
            continue

        # annotation file
        if os.path.splitext(_data)[1] in ANNOTATION_EXTS:
            data_list.append(_data)
            anno_num += 1
            continue

        file_client = infer_client_from_uri(_data, petrel_conf_path)
        if file_client.isdir(_data):
            _datas = [
                os.path.join(_data, filename)
                for filename in file_client.list_dir_or_file(
                    _data, list_dir=False, recursive=True)
            ]
            res_list, res_num = generate_datalist(_datas, img_root, petrel_conf_path)
            data_list.extend(res_list)
            anno_num += res_num
            continue

        # list file
        try:
            with open(_data, 'r', encoding='utf-8') as file:
                filelines = file.read().splitlines()
            _datas = [
                os.path.join(img_root,
                             fileline.split()[0]) for fileline in filelines
            ]
            res_list, res_num = generate_datalist(_datas, img_root, petrel_conf_path)
            data_list.extend(res_list)
            anno_num += res_num
        except UnicodeDecodeError:  # annotation file
            data_list.append(_data)
            anno_num += 1
    assert anno_num == 0 or anno_num == len(
        data_list), f'invalid file contained by source data {data}'
    return data_list, anno_num
