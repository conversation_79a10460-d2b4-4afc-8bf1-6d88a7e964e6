from mmseg.models.losses import CrossEntropyLoss as _CrossEntropyLoss

from ..builder import MODELS


@MODELS.register_module()
class CrossEntropyLoss(_CrossEntropyLoss):
    """CrossEntropyLoss.

    Inherits from `mmseg.models.losses.CrossEntropyLoss`,
    and put `ignore_index` into `__init__` params.
    """

    def __init__(self,
                 ignore_index=255,
                 **kwargs):
        super().__init__(**kwargs)
        self.ignore_index = ignore_index

    def forward(self, cls_score, label, **kwargs):
        """Forward function."""
        if 'ignore_index' not in kwargs:
            kwargs['ignore_index'] = self.ignore_index
        
        return super().forward(cls_score, label, **kwargs)
