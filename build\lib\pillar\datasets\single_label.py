from collections import defaultdict
import math

import numpy as np
from mmcls.datasets.base_dataset import BaseDataset
from mmcls.core.evaluation import (
    calculate_confusion_matrix,
    precision_recall_f1,
    support,
)
from mmcls.models.losses import accuracy

from .builder import DATASETS


@DATASETS.register_module()
class SingleLabelDataset(BaseDataset):
    """Single label dataset with resample.
    The subclass of SingleLabelDataset need to overwrite
    `load_original_data_infos` and `evaluate_single_attribute` method.

    Args:
        data_prefix (str): the prefix of data path
        pipeline (list): a list of dict, where each element represents
            a operation defined in `mmcls.datasets.pipelines`
        ann_file (str | None): the annotation file. When ann_file is str,
            the subclass is expected to read from the ann_file. When ann_file
            is None, the subclass is expected to read according to data_prefix
        test_mode (bool): in train mode or test mode
    """

    ALLOWED_METRICS = [
        "accuracy",
        "precision",
        "recall",
        "f1_score",
        "support",
    ]

    def __init__(self, attr_names=[], percentage=1.0, target_freqs={}, **kwargs):
        self.attr_names = attr_names
        self.percentage = percentage
        self.target_freqs = target_freqs

        super().__init__(**kwargs)  # initialize CLASSES

        assert self.percentage > 0.0, "percentage must be a positive number"
        for target_freq in self.target_freqs.values():
            assert target_freq > 0.0, "target frequencies must be positive numbers"

    def load_annotations(self):
        data_infos = self.load_original_data_infos()

        # resampling for class balance
        category_nums = defaultdict(int)
        for label_name, cat_infos in data_infos.items():
            category_nums[label_name] = len(cat_infos)
        total_num = np.array(list(category_nums.values())).sum() * self.percentage

        category_indices = dict()
        for label_name, cat_num in category_nums.items():
            assert cat_num > 0, f"category {label_name} does not contain any images"
            target_freq = self.target_freqs.get(label_name, None)
            if target_freq is None:
                target_num = math.ceil(cat_num * self.percentage)
            else:
                target_num = math.ceil(target_freq * total_num)

            if cat_num > target_num:  # downsample
                cat_indices = np.random.choice(cat_num, target_num, replace=False)
            elif cat_num < target_num:  # upsample
                repeat_num, sample_num = target_num // cat_num, target_num % cat_num
                cat_indices = np.concatenate(
                    [np.arange(cat_num)] * repeat_num
                    + [np.random.choice(cat_num, sample_num, replace=False)]
                )
            else:
                cat_indices = np.arange(cat_num)
            category_indices[label_name] = cat_indices

        resampled_infos = list()
        for cat_infos, cat_indices in zip(
            data_infos.values(), category_indices.values()
        ):
            resampled_infos.extend([cat_infos[cat_idx] for cat_idx in cat_indices])

        # sort resampled_infos with origin_index
        resampled_infos.sort(key=lambda x: x.get("origin_index"))

        # list[dict[img_prefix, img_info, gt_label]]
        return resampled_infos

    def load_original_data_infos(self):
        raise NotImplementedError

    def evaluate(self, results, metric="accuracy", metric_options=None, logger=None, **eval_kwargs):
        """Evaluate the dataset.

        Args:
            results (dict): Testing results of the dataset, flatten dict.
            metric (str | list[str]): Metrics to be evaluated.
            Default value is `accuracy`.
            metric_options (dict, optional): Options for calculating metrics.
                Allowed keys are 'topk', 'thrs' and 'average_mode'.
                Defaults to None.
            logger (logging.Logger | str, optional): Logger used for printing
                related information during evaluation. Defaults to None.
        Returns:
            dict: evaluation results
        """
        if isinstance(metric, str):
            metrics = [metric]
        else:
            metrics = metric

        invalid_metrics = set(metrics) - set(self.ALLOWED_METRICS)
        if len(invalid_metrics) != 0:
            raise ValueError(f"metric {invalid_metrics} is not supported.")

        # flatten dict
        gt_labels_dict = self.get_gt_labels()

        eval_results = {}
        for attr_name in self.attr_names:
            # results is dict of attr_name to merged-prediction
            attr_results = np.vstack(results[attr_name])
            gt_labels = gt_labels_dict[attr_name]

            assert len(gt_labels) == len(attr_results), (
                "dataset testing results should " "be of the same length as gt_labels."
            )

            attr_eval_results = self.evaluate_single_attribute(
                attr_results, gt_labels, metrics, metric_options
            )

            eval_results.update(
                {
                    attr_name + "_" + key: value
                    for key, value in attr_eval_results.items()
                }
            )

        return eval_results

    # gt_labels: flatten dict
    def get_gt_labels(self):
        """Get all ground-truth labels (categories)."""
        gt_labels = dict()

        for attr_name in self.attr_names:
            gt_labels[attr_name] = np.array(
                [data["gt_label"][attr_name] for data in self.data_infos]
            )
        return gt_labels

    def get_img_infos(self):
        return [data_info["img_info"] for data_info in self.data_infos]

    def evaluate_single_attribute(self, results, gt_labels, metrics, metric_options):
        """Evaluate single attribute."""

        if metric_options is None:
            metric_options = {"topk": (1,)}

        topk = metric_options.get("topk", (1,))
        thrs = metric_options.get("thrs")
        average_mode = metric_options.get("average_mode", "macro")

        eval_results = {}
        if "accuracy" in metrics:
            if thrs is not None:
                acc = accuracy(results, gt_labels, topk=topk, thrs=thrs)
            else:
                acc = accuracy(results, gt_labels, topk=topk)
            if isinstance(topk, tuple):
                eval_results_ = {f"accuracy_top-{k}": a for k, a in zip(topk, acc)}
            else:
                eval_results_ = {"accuracy": acc}
            if isinstance(thrs, tuple):
                for key, values in eval_results_.items():
                    eval_results.update(
                        {
                            f"{key}_thr_{thr:.2f}": value.item()
                            for thr, value in zip(thrs, values)
                        }
                    )
            else:
                eval_results.update({k: v.item() for k, v in eval_results_.items()})

        if "confusion_matrix" in metrics:
            confusion_matrix = calculate_confusion_matrix(results, gt_labels)
            eval_results["confusion_matrix"] = confusion_matrix.numpy()
        else:
            confusion_matrix = None

        if "support" in metrics:
            support_value = support(results, gt_labels, average_mode=average_mode)
            eval_results["support"] = support_value

        precision_recall_f1_keys = ["precision", "recall", "f1_score"]
        if len(set(metrics) & set(precision_recall_f1_keys)) != 0:
            if thrs is not None:
                precision_recall_f1_values = precision_recall_f1(
                    results, gt_labels, average_mode=average_mode, thrs=thrs
                )
            else:
                precision_recall_f1_values = precision_recall_f1(
                    results, gt_labels, average_mode=average_mode
                )
            for key, values in zip(
                precision_recall_f1_keys, precision_recall_f1_values
            ):
                if key in metrics:
                    if isinstance(thrs, tuple):
                        eval_results.update(
                            {
                                f"{key}_thr_{thr:.2f}": value
                                for thr, value in zip(thrs, values)
                            }
                        )
                    else:
                        eval_results[key] = values

        return eval_results
