import os
import re
import sys
import argparse
import subprocess


CUDA_MAPPINGS = {
    '90': {
        '1.5.0':  {
            'torch': '1.5.0+cuda90.cudnn7.6.3.lms',
            'torchvision': '0.6.0+cu90',
        },
        '1.6.0': {
            'torch': '1.6.0+cuda90.cudnn7.6.3',
            'torchvision': '0.6.0+cu90', 
        },
        '1.8.1': {
            'torch': '1.8.1+cuda90.cudnn7.6.5',
            'torchvision': '0.6.0+cu90',
        },
    },
    '101':{
        '1.5.0':{
            'torch': '1.5.0+cu101',
            'torchvision': '0.9.1'
        },
        '1.6.0':{
            'torch': '1.6.0+cu101',
            'torchvision': '0.9.1'
        },
        '1.8.1':{
            'torch': '1.8.1+cu101',
            'torchvision': '0.9.1'
        }
    },
    '102': {
        '1.8.1':{
            'torch': '1.8.1+cu102',
            'torchvision': '0.9.1',
        },
    },
    '111': {
        '1.8.0':{
            'torch': '1.8.0+cu111',
            'torchvision': '0.9.1',
        },
        '1.8.1':{
            'torch': '1.8.1+cu111',
            'torchvision': '0.9.1',
        },
    },
    '113': {
        '1.10.1':{
            'torch': '1.10.1+cu113',
            'torchvision': '0.11.2+cu113',
        },
    },
}

class Installer(object):
    MMCV_URL = 'http://download.openmmlab.com/mmcv/dist/' \
               '{cuda_version}/{torch_version}/index.html'
    
    DEFAULT_ARGS = ['--trusted-host', 'pypi.opencloud.com', '--index-url',
                    'http://pypi.opencloud.com/repository/pypi-proxy/simple/']
    
    RUNTIME_REQUIREMENTS = 'requirements/runtime.txt'
    OPEN_MMLAB_REQUIREMENTS = 'requirements/open_mmlab.txt'
    SPRING_REQUIREMENTS = 'requirements/spring.txt'
    
    def __init__(self,
                 need_spring=False,
                 partition='Test',
                 requeried_cuda_version=None,
                 requeried_torch_version=None):
        self.need_spring = need_spring
        self.partition = partition
        self.cuda_version = self.get_cuda_version(requeried_cuda_version)
        self.torch_version = self.get_torch_version(requeried_torch_version)

    def get_cuda_version(self, requeried_cuda_version):
        """ 
            @return: [str] cuda version
        """
        cuda_version = requeried_cuda_version
        current_cuda_version = self.get_current_cuda_version()
        share_cuda_versions = self.get_share_cuda_version()

        if cuda_version:
            cuda_version = cuda_version.replace('.', '')
            if cuda_version != current_cuda_version:
                # to find cuda version in share folder
                assert cuda_version in share_cuda_versions, (
                    f"Unsupported CUDA version {cuda_version}, "
                    f"current cuda version: {cuda_version}, "
                    f"share cuda versions: {share_cuda_versions}.")

                cuda_path = f"/mnt/lustre/share/cuda-{cuda_version[:-1] + '.' + cuda_version[-1]}"
                print(f'Use share CUDA environment in {cuda_path}.')
                cmd = f"export PATH={cuda_path}/bin:$PATH"
                os.popen(cmd)
        else:
            assert current_cuda_version is not None, \
                "Current CUDA version not found, please specify a CUDA version."
            
            cuda_version = current_cuda_version

        assert cuda_version in CUDA_MAPPINGS.keys(), (
            f"Current CUDA's version {cuda_version} is not supported, "
            f"Pillar supports CUDA versions: {list(CUDA_MAPPINGS.keys())}")

        return cuda_version
    
    def get_current_cuda_version(self):
        stdout = os.popen('nvcc --version').readlines()
        for info in stdout:
            regex = r'release (\S+),'
            match = re.search(regex, info)
            if match:
                return str(match.group(1)).replace('.', '')
        return None

    def get_share_cuda_version(self):
        """ 
            @return: the set of share path cuda
        """
        cmd = "ls /mnt/lustre/share/ | grep 'cuda'"
        stdout = os.popen(cmd).readlines()
        versions = set()
        for info in stdout:
            regex = r'^cuda-(\d+.\d+)'
            match = re.search(regex, info)
            if match:
                versions.add(str(match.group(1)).replace('.', ''))

        return versions

    def get_torch_version(self, requeried_torch_version):
        torch_version = requeried_torch_version

        if torch_version:
            assert torch_version in CUDA_MAPPINGS[self.cuda_version], (
                f"Current CUDA's environment is {self.cuda_version}, "
                f"got torch version: {torch_version}, Pillar only support "
                f"these torch versions: {list(CUDA_MAPPINGS[self.cuda_version].keys())}"
                " under current CUDA version.")
        else:
            # find the highest version of torch under the cuda version
            torch_version = sorted(list(CUDA_MAPPINGS[self.cuda_version].keys()), reverse=True)[0] 

        return torch_version

    def install(self):
        self.install_runtime()
        self.install_open_mmlab()
        if self.need_spring:
            self.install_spring()

    def install_runtime(self):
        with open(self.RUNTIME_REQUIREMENTS, 'r') as f:
            packages = f.readlines()
        install_args = ['--trusted-host', 'spring.com', 
                        '--extra-index-url', 'http://spring.com/pypi']
        for package in packages:
            package = package.strip()
            parts = self.parse_line(package)
            if parts[0] == 'torch':
                package = self.format_torch_package(package)
            elif parts[0] == 'torchvision':
                package = self.format_torchvision_package(package)
            self.install_package(package, *self.DEFAULT_ARGS, *install_args)

    def parse_line(self, line):
        pat = '(' + '|'.join(['>=', '==', '>']) + ')'
        parts = re.split(pat, line, maxsplit=1)
        parts = [p.strip() for p in parts]
        return parts
    
    def format_torch_package(self, package):
        return package + f"=={CUDA_MAPPINGS[self.cuda_version][self.torch_version]['torch']}"
    
    def format_torchvision_package(self, package):
        return package + f"=={CUDA_MAPPINGS[self.cuda_version][self.torch_version]['torchvision']}"
    
    def install_package(self, package, *args):
        package = package.strip()
        if package and not package.startswith('#'):
            subprocess.check_call([sys.executable, "-m", "pip", "install", package, *args])
    
    def install_open_mmlab(self):
        with open(self.OPEN_MMLAB_REQUIREMENTS, 'r') as f:
            packages = f.readlines()
        mmcv_install_args = ['--trusted-host', 'download.openmmlab.com',
                             '-f', self.get_mmcv_full_find_link()]
        install_args = ['--trusted-host', 'pypi.openmmlab.com',
                        '--extra-index-url', 'http://pypi.openmmlab.com:1024/simple']
        for package in packages:
            package = package.strip()
            parts = self.parse_line(package)
            if parts[0] == 'mmcv-full':
                self.install_package(package, *self.DEFAULT_ARGS, *mmcv_install_args)
           
            self.install_package(package, *self.DEFAULT_ARGS, *install_args)

    def get_mmcv_full_find_link(self):
        find_link = self.MMCV_URL.format(cuda_version='cu'+self.cuda_version, 
                                         torch_version='torch'+".".join(self.torch_version.split(".")[:-1]))
        return find_link

    def install_spring(self):
        with open(self.SPRING_REQUIREMENTS, 'r') as f:
            packages = f.readlines()
        install_args = ['--trusted-host', 'spring.com', 
                        '--extra-index-url', 'http://spring.com/pypi']
        for package in packages:
            package = package.strip()
            parts = self.parse_line(package)
            if parts[0] == 'spring':
                package = self.format_spring_package(package)
            self.install_package(package, *self.DEFAULT_ARGS, *install_args)    
   
    def format_spring_package(self, package):
        torch_version = self.torch_version.replace('.', '')
        return package + f'+cu{self.cuda_version}.torch{torch_version}.mvapich2.pmi2.nartgpu'


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Pillar build env')
    parser.add_argument('--need-spring', type=str2bool, default=False,
                        help='use spring backend or not')
    parser.add_argument('--partition', help='partition for pod setup')
    parser.add_argument('--torch-version', '-t', default=False,
                        help='torch version')
    parser.add_argument('--cuda-version', '-c', default=False,
                        help='cuda version')
    
    args = parser.parse_args()

    installer = Installer(need_spring=args.need_spring,
                          partition=args.partition,
                          requeried_cuda_version=args.cuda_version,
                          requeried_torch_version=args.torch_version)
    installer.install()
