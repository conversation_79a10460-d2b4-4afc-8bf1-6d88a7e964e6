import os
import json
import time
import subprocess

import numpy as np

from .utils import model_infer


def convert(model, input_dict, output_names, filename, opset_version=9):
    from torch.onnx import export
    import onnx
    from onnxsim.onnx_simplifier import simplify
    # from onnxsim import simplify
    import torch
    print("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
    #print("model: ", model)
    #print("model.model.__dir__(): ", model.model.__dir__())
    #torch.save(model.model, "tokestrel_output/model.pt")
    #print("input_dict: ", input_dict)
    print("list(input_dict.keys()): ", list(input_dict.keys()))
    print("output_names: ", output_names)
    export(
        model,
        input_dict,
        filename,
        input_names=list(input_dict.keys()),
        output_names=output_names,
        opset_version=opset_version,
        #verbose=True,
        enable_onnx_checker=True,
        use_external_data_format=False,
    )
    model = onnx.load(filename)
    onnx.save(model, filename)
    onnx.save(model, str(filename).rsplit('.', 1)[0] + '_ori.onnx')
    print('simplify begin!', flush=True)
    # model_opt, check_ok = simplify(model)
    print('simplify end!', flush=True)
    # onnx.save(model_opt, filename)

def deploy_phase(
    model,
    model_file,
    phasename,
    batch_sizes,
    opset_version=9,
    outdir="tokestrel",
):
    print(">>>>>>>> infer", phasename)
    output_dict = model_file["output"]
    input_dict = model_file["input"]
    for m in model.modules():
        m.tokestrel = phasename
    #print("batch_sizes: ", batch_sizes)
    for k, v in input_dict.items():
        input_dict[k] = v[:batch_sizes.get(k, 1)]

    model_file["backend"] = "kestrel_onnx"
    model_file["net"] = f"{phasename}/model.onnx"
    phasename = os.path.join(outdir, phasename)
    os.makedirs(phasename, exist_ok=True)
    #print("---------------------------------------")
    #print("input_dict: ", input_dict)
    #print("\n")
    #print("output_dict: ", [output_dict[i].shape for i in output_dict])
    #print("------------------------------------------")
    convert(
        model,
        input_dict,
        list(output_dict.keys()),
        f"{phasename}/model.onnx",
        opset_version,
    )
    print('convert done!',flush=True)


def tar_model(model_files, args, config):

    def dump_category_param():
        split = [
            len(config.categories.attr[k]) for k in config.categories.group
        ]
        length = np.cumsum(split).tolist()
        category = {
            "LANELINE": {
                "id":
                0,
                "attribute": [
                    {
                        "type_name": "lane_type",
                        "attr_name": config.categories.attr["lane_type"],
                        "length": [0, length[0] - 1],
                    },
                    {
                        "type_name": "lane_color",
                        "attr_name": config.categories.attr["lane_color"],
                        "length": [length[0], length[1] - 1],
                    },
                ],
            },
            "ROADSIDE": {
                "id":
                1,
                "attribute": [
                    {
                        "type_name": "roadside",
                        "attr_name": config.categories.attr["roadside"],
                        "length": [length[1], length[2] - 1],
                    },
                ],
            },
        }
        return category

    def dump_meta():
        version = config.get("tokestrel", {}).get("version", "0.0.0")
        if isinstance(version, str):
            version = version.strip().split(".")
        version = [int(_) for _ in version]
        train_date = time.strftime("%Y%m%d", time.localtime())
        meta = {
            "model_name": config.get("tokestrel",
                                     {}).get("model_name", "condlane"),
            "model_type": "condlane",
            "version": {
                "major": version[0],
                "minor": version[1],
                "patch": version[2],
                "train_date": train_date,
            },
            "description": "",
        }
        return meta

    def dump_nart_config():
        min_shapes = {}
        max_shapes = {}
        batch_sizes = config.get("tokestrel", {}).get("batch_size", {})
        for i, phase in enumerate(model_files):
            input_dict = model_files[phase]["input"]
            for k, v in input_dict.items():
                batch_size = batch_sizes.get(k, 1)
                shape = list(v.shape[1:])
                min_shapes[k] = [1] + shape
                max_shapes[k] = [batch_size] + shape
        json = {
            "min_shapes": min_shapes,
            "max_shapes": max_shapes,
            "build_shapes": max_shapes,
        }
        return json

    def dump_parameters():
        for model_file in model_files.values():
            model_file["input"] = {k: k for k in model_file["input"]}
            model_file["output"] = {k: k for k in model_file["output"]}
        if "net" in model_files:
            model_files["net"]["max_batch_size"] = 1
        parameters = dict(
            model_files=model_files,
            image_width=config.img_scale[0],
            image_height=config.img_scale[1],
            input_w=config.img_scale[0],
            input_h=config.img_scale[1],
            long_scale=config.img_scale[0],
            short_scale=config.img_scale[1],
            pixel_means=config.img_norm_cfg.mean,
            pixel_stds=config.img_norm_cfg.std,
            num_classes=2,
            image_channel=3,
            model_preprocess_support=True,
            proposal_heatmap_thr=config.proposal_heatmap_thr,
            key_points_size_thr=config.post_processor.key_points_size_thr,
            roadside_key_points_size_thr=config.post_processor.side_key_points_size_thr,
            breakpoints_thr=config.post_processor.breakpoints_thr,
            with_breakpoints=config.post_processor.with_breakpoints,
            lane_proposal_thresh=config.proposal_heatmap_thr,
            roadside_proposal_thresh=config.proposal_heatmap_thr,
            momentum=config.post_processor.get("momentum", 0.4),
            view_distance=config.post_processor.get("view_distance", 1),
            field_scale=-1,  # config.post_processor.get("field_scale", -1),
            length_scale=0,  # config.post_processor.get("field_scale", 0),
            num_row_ref_point=config.post_processor.num_row_ref_point,
            slice_top=config.get("h_crop", [0, config.img_scale[1]])[0],
            slice_bottom=config.get("h_crop", [0, config.img_scale[1]])[1],
            slice_left=config.get("w_crop", [0, config.img_scale[0]])[0],
            slice_right=config.get("w_crop", [0, config.img_scale[0]])[1],
            overlap_thr=config.post_processor.get("overlap_thr", 3),
            th_dp_radius_laneline=config.post_processor.get("th_dp_radius_laneline", 2),
            th_dp_radius_roadside=config.post_processor.get("th_dp_radius_roadside", 9),
            line_lenght_thr=config.post_processor.th_len,
        )
        return parameters

    names = ["nart_config", "category_param", "meta", "parameters"]
    for name in names:
        datadict = locals()[f"dump_{name}"]()
        filename = os.path.join(args.outdir, f"{name}.json")
        json.dump(datadict, open(filename, "w"), indent=2)

    files = [f"{name}.json" for name in names[1:]]
    for phase in model_files:
        files.append(phase + "/model.onnx")
    subprocess.run(
        ["tar", "cvf", "model.tar"] + files,
        check=True,
        cwd=args.outdir,
    )
    print("upload model.tar like this:", flush=True)
    print(f">>> python -m adela.cmd -p <pid> -rat {args.outdir}/model.tar", flush=True)


def tokestrel(model, data, args, config):
    args.outdir = os.path.join(args.outdir, "tokestrel")
    os.makedirs(args.outdir, exist_ok=True)
    outputs = model_infer(model, data, args.dup_i, th=0.1)
    print("outputs.keys(): ", outputs.keys())
    print('deploy phase begin!', flush=True)
    #import numpy as np
    #np.save("tokestrel_output/outputs.npy", outputs)
    #import torch
    #print("model.__class__: ", model.__class__)
    #print("model: ", model)
    #print("model.__dir__(): ", model.__dir__())
    #torch.save("tokestrel_output/model", model.model)
    #np.save("tokestrel_output/config", config)
    for i, phase in enumerate(outputs):
        #print('#'*10 + 'i = {}, phase = {}'.format(i, phase), flush=True)
        deploy_phase(
            model,
            outputs[phase],
            phase,
            config.get("tokestrel", {}).get("batch_size", {}),
            12,#11,
            outdir=args.outdir,
        )
    print('deploy phase done (with opset 12)!', flush=True)
    ret = {
        k: v.detach().cpu().numpy()
        for m in outputs.values() for k, v in m["output"].items()
    }
    tar_model(outputs, args, config)
    return ret
