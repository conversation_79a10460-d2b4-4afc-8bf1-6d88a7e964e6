import json

# File paths
ground_truth_path = "/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/eval_files/ground_truth_batch3_cropped_b3e2.txt"
reformatted_cpp_results_path = "/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/eval_files/reformatted_cpp_results.txt"
filtered_ground_truth_path = "/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/eval_files/filtered_ground_truth.txt"

# Load image paths from reformatted_cpp_results.txt
cpp_image_paths = set()

with open(reformatted_cpp_results_path, 'r') as cpp_file:
    for line in cpp_file:
        try:
            entry = json.loads(line.strip())
            image_path = entry.get('image_path')
            if image_path:
                cpp_image_paths.add(image_path)
        except json.JSONDecodeError:
            continue  # In case of any decoding errors, skip the line

# Filter ground_truth864_496.txt
with open(ground_truth_path, 'r') as ground_truth_file, open(filtered_ground_truth_path, 'w') as output_file:
    for line in ground_truth_file:
        try:
            entry = json.loads(line.strip())
            image_path = entry.get('image_path')
            if image_path and image_path in cpp_image_paths:
                output_file.write(json.dumps(entry) + '\n')
        except json.JSONDecodeError:
            continue  # In case of any decoding errors, skip the line

#print(f"Filtered ground truth file saved to: {filtered_ground_truth_path}")
