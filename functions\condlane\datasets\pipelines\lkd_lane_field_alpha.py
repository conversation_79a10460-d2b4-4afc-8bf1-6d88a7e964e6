import math

import cv2
import ipdb
import numpy as np
from scipy import interpolate

from pillar.datasets import PIPELINES
from .lkd_lane_cls_formation import CollectLkdClsLane, draw_label, gaussian2D
from .lkd_lane_field_beta import points2field



def draw_umich_gaussian(heatmap,
                        center,
                        radius,
                        k=1,
                        offmap=None,
                        ypos=None,
                        xpos=None,
                        hm_h=72,
                        hm_w=128,
                        hard_code=True):
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)
    x, y = int(center[0]), int(center[1])
    height, width = heatmap.shape[0:2]
    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)
    masked_heatmap = heatmap[y - top:y + bottom, x - left:x + right]
    if offmap is not None:
        offmap[1, y - top:y + bottom, x - left:x +
               right] = center[1] / hm_h - ypos[y - top:y + bottom]
        offmap[0, y - top:y + bottom, x - left:x +
               right] = center[0] / hm_w - xpos[x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom,
                               radius - left:radius + right]
    if hard_code:
        masked_gaussian[masked_gaussian > 0.05] = 1
    if np.size(masked_gaussian) > 0 and np.size(masked_heatmap) > 0:
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def extend_line(line, rows=1):
    start = line[1]
    end = line[0]
    dx = end[0] - start[0]
    extend = []
    for i in range(1, rows + 1):
        extend_point = np.array([[line[0][0] + dx * i, line[0][1] + i]])
        extend.append(extend_point)
    extended = np.concatenate(extend + [line])
    return extended


@PIPELINES.register_module()
class CollectFieldLaneAlpha(CollectLkdClsLane):

    def __init__(
        self,
        view_distance=5,
        inv_view_distance=0,
        property_down_rate=1,
        num_ref_point=1,
        num_drop_remote_point=0,
        num_start_rows=0,
        bg_weight=0,
        draw_r=3,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.view_distance = view_distance
        self.inv_view_distance = inv_view_distance
        self.property_down_rate = property_down_rate
        self.num_ref_point = num_ref_point
        self.num_drop_remote_point = num_drop_remote_point
        self.num_start_rows = num_start_rows
        self.bg_weight = bg_weight
        self.draw_r = draw_r
        # some default keys, fix it in code to simple the config
        self.meta_keys = [
            'filename', 'img_prefix', 'sub_img_name', 'img_shape', 'ori_shape',
            'roi', 'bk_roi', 'break_points', 'img_norm_cfg', 'hm_shape',
            'mask_shape', 'down_scale', 'hm_down_scale', 'laneline',
            'roadside', 'scene', 'camera_index'
        ]
        self.keys = [
            'img', 'ignore_laneline', 'ignore_roadside', 'laneline_gt_hm',
            'laneline_start_point_off', 'roadside_gt_hm',
            'roadside_start_point_off', 'laneline_field', 'laneline_attr',
            'roadside_field', 'roadside_attr'
        ]
        self.zero_field = None

    def get_zero_field(self, mask_h, mask_w):
        if self.zero_field is None:
            self.zero_field = dict(
                offset = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),
                field = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),

                offset_weight = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),
                field_weight = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),

                inv_vector_field = np.zeros((self.num_ref_point * (self.inv_view_distance), \
                    mask_h, mask_w), np.float32),
                inv_field_weight = np.zeros((self.num_ref_point * (self.inv_view_distance), \
                    mask_h, mask_w), np.float32),
                inv_length = np.zeros((1, mask_h, mask_w), np.float32),

                length =  np.zeros((1, mask_h, mask_w), np.float32),
                length_weight = np.zeros((1, mask_h, mask_w), np.float32),
                inv_length_weight = np.zeros((1, mask_h, mask_w), np.float32),
                gt_mask = np.zeros((1, mask_h, mask_w), np.float32),
            )
            if self.view_distance > 1:
                self.zero_field["view_weight"] = np.zeros((self.num_ref_point * (self.view_distance - 1), \
                    mask_h, mask_w), np.float32),
                self.zero_field["view"] = np.zeros((self.num_ref_point * (self.view_distance - 1), \
                    mask_h, mask_w), np.float32),
        return self.zero_field

    def reshape(self, v):
        v = v.reshape(v.shape[0], -1, self.num_ref_point)
        v = v.transpose(2, 0, 1)
        v = v / self.num_ref_point
        return v

    def resize(self, v, w, h):
        return cv2.resize(
            v,
            (w, h),
            interpolation=cv2.INTER_NEAREST,
        )

    def points2field(
        self,
        points,
        mask_h,
        mask_w,
        hm_h,
        hm_w,
        gt_labels,
        gt_colors,
    ):
        field = points2field(
            points,
            mask_h,
            mask_w * self.num_ref_point,
            gt_labels,
            gt_colors,
            th=self.draw_r,
        )
        weight = field.pop("weight")
        weight[field["line_mask"] < 0.5] = 0
        off_weight = self.reshape(weight) * self.num_ref_point
        len_weight = self.resize(
            weight,
            mask_w,
            mask_h,
        )[None]
        field["offset_weight"] = off_weight
        field["field_weight"] = off_weight
        field["inv_field_weight"] = off_weight
        field["length_weight"] = len_weight
        field["inv_length_weight"] = len_weight
        field["gt_mask"] = self.resize(
            field.pop("line_mask").astype(np.float32),
            mask_w,
            mask_h,
        )[None]
        field["offset"] = self.reshape(field.pop("off_field"))
        field["field"] = self.reshape(field.pop("off_forward"))
        field["inv_vector_field"] = self.reshape(field.pop("off_backward"))
        field["length"] = self.resize(
            field.pop("len_forward"),
            mask_w,
            mask_h,
        )[None]
        field["inv_length"] = self.resize(
            field.pop("len_backward"),
            mask_w,
            mask_h,
        )[None]
        field["type"] = self.resize(
            field.pop("label"),
            hm_w,
            hm_h,
        )
        field["color"] = self.resize(
            field.pop("color"),
            hm_w,
            hm_h,
        )
        return field

    def process_mask(
        self,
        mask,
        gt_hm,
        start_point_off,
        mask_w,
        mask_h,
        hm_w,
        hm_h,
        ratio_hm_mask,
        anno_label=255,
        anno_color=255,
    ):
        assert mask is not None
        gt_points, gt_labels, gt_colors, idx2insidx = self.mask2point(
            mask,
            anno_label,
            anno_color,
        )
        valid_gt = []
        valid_gt_points = []
        valid_idx2insidx = []
        for i in range(len(gt_points)):
            gt_point = gt_points[i]
            if 0 < self.filter_pts < len(gt_point):
                gt_point = gt_point[self.filter_pts:]
            # pts = (gt_point + 0.5) / self.down_scale
            pts = (gt_point) / self.down_scale
            pts = pts[::-1]
            if len(pts) > 1:
                valid_gt.append(pts)
                valid_gt_points.append(gt_point)
                valid_idx2insidx.append(idx2insidx[i])
        gt_points = valid_gt_points

        # draw gt_hm_lane
        gt_hm_lane_ends = []  # [hm end point,points]
        for l in valid_gt:
            if self.endpoint == "down":
                idx = 0
            elif self.endpoint == "top":
                idx = -1
            elif self.endpoint == "far":
                idx = int(len(l) * 0.75)
            elif self.endpoint == "near":
                idx = int(len(l) * 0.25)
            else:
                idx = len(l) // 2
            point = tuple(l[idx] * ratio_hm_mask)
            gt_hm_lane_ends.append([point, l])  # [end point,lane points]
        radius = [self.radius] * len(gt_hm_lane_ends)
        if len(gt_hm_lane_ends) >= 2:
            endpoints = [p[0] for p in gt_hm_lane_ends]
            for j in range(len(endpoints)):
                dis = self.min_dis_one_point(endpoints, j)
                if dis < 1.5 * radius[j]:
                    radius[j] = int(max(dis / 1.5, 1) + 0.49999)

        # regression
        length_weight = np.arange(mask_h, 0, -1, dtype=np.float32) / mask_h
        length_weight = length_weight.reshape(1, mask_h, 1).repeat(mask_w,
                                                                   axis=2)
        inv_length_weight = np.arange(1, mask_h + 1, 1,
                                      dtype=np.float32) / mask_h
        inv_length_weight = inv_length_weight.reshape(1, mask_h,
                                                      1).repeat(mask_w, axis=2)
        type_field = np.ones((mask_h // self.property_down_rate, \
            mask_w // self.property_down_rate), np.long) * 255
        color_field = np.ones((mask_h  // self.property_down_rate, \
            mask_w  // self.property_down_rate), np.long) * 255
        # y_ref_pos = np.arange(0.5 , mask_h, 1) / mask_h
        x_hm_pos = np.arange(0.5, hm_w, 1) / hm_w
        y_hm_pos = np.arange(0.5, hm_h, 1).reshape(hm_h, 1) / hm_h
        start_hm_downsample_rate = mask_w // hm_w
        res = []
        # import ipdb
        # ipdb.set_trace()
        gt_points_scaled = []
        for insidx, (end_point, line) in enumerate(gt_hm_lane_ends):
            if self.num_start_rows <= 0:
                gt_hm[0] = draw_umich_gaussian(gt_hm[0], end_point, radius[insidx], \
                1, start_point_off, y_hm_pos, x_hm_pos, hm_h, hm_w)
            y_min = int(np.min(line[:, 1]))
            y_max = int(np.max(line[:, 1]))
            xs = line[:, 0]
            ys = line[:, 1]
            start_row, end_row = int(ys[0]) + 1, int(ys[-1]) - 2
            if start_row - end_row < 2:
                end_row -= 1
            row_idxs = np.arange(start_row, end_row, -1)
            grid_y = row_idxs + 0.5
            kind_id = len(line)
            kind_id = kind_id if kind_id < 4 else 4
            inter_kind = {2: 'slinear', 3: 'quadratic', 4: 'cubic'}[kind_id]
            grid_x = interpolate.interp1d(ys, xs, kind = inter_kind, fill_value="extrapolate", \
                bounds_error = False)(grid_y)
            if kind_id >= 3:
                # use linear mode intead of cubic for bound point
                grid_x[0] = grid_x[1] * 2 - grid_x[2]
            points_scaled = np.stack([grid_x, grid_y], -1)
            points_scaled = points_scaled * [self.num_ref_point, 1]
            gt_points_scaled.append(points_scaled)
            start_point_row = start_row
            if len(grid_x) > 2:
                while start_point_row >= mask_h or \
                    grid_x[start_row - start_point_row] < 0 or \
                    grid_x[start_row - start_point_row] > mask_w:
                    if start_row - start_point_row + 1 >= len(grid_x):
                        break
                    start_point_row -= 1
            start_x, start_y =grid_x[start_row - start_point_row] / mask_w , \
                (start_point_row + 0.5) / mask_h
            # start_point_row = int(ys[0])
            # start_x, start_y = xs[0] / mask_w, ys[0] / mask_h
            for y in range(start_row, end_row, -1):
                if y >= mask_h:
                    continue
                ref_x = grid_x[start_row - y]
                # norm_ref_x = ref_x / mask_w
                r = self.draw_r
                if start_row > y > end_row + 1:
                    angle = math.atan2(
                        1,
                        np.abs(grid_x[start_row - y + 1] -
                               grid_x[start_row - y]))
                    half_line_width = r * y / mask_h
                    r = int(np.round(half_line_width / math.sin(angle))) + 1
                # field_r = r * self.num_ref_point
                s_x = int(np.clip(ref_x - r + 1, 0, mask_w - 3))
                e_x = int(np.clip(ref_x + r, s_x + 3, mask_w))

                # field_s_x = int(np.clip(ref_x * self.num_ref_point - field_r + 1,0, num_row_point -3))
                # field_e_x = int(np.clip(ref_x * self.num_ref_point + field_r, field_s_x + 3, num_row_point))

                as_x, ae_x = s_x // self.property_down_rate, e_x // self.property_down_rate
                ae_x = max(as_x + 1, ae_x)
                # gt_mask[0, y, s_x:e_x] = 1
                if self.num_start_rows > 0 and start_point_row - y < self.num_start_rows * start_hm_downsample_rate:
                    hm_l_x, hm_r_x = min(max(as_x -1,0), mask_w // start_hm_downsample_rate - 1), \
                        min(ae_x + 1, mask_w // start_hm_downsample_rate)
                    start_point_off[0, y // start_hm_downsample_rate, hm_l_x :hm_r_x] = \
                        start_x - x_hm_pos[hm_l_x :hm_r_x]
                    start_point_off[1, y // start_hm_downsample_rate, hm_l_x :hm_r_x] = \
                        start_y - y_hm_pos[y // start_hm_downsample_rate][0]
                    gt_hm[0, y // start_hm_downsample_rate, hm_l_x:hm_r_x] = 1
            info = dict(
                y_max=y_max,
                y_min=y_min,
                # type=gt_labels[insidx],
                # color=gt_colors[insidx],
            )
            res.append(info)

        field = self.points2field(
            gt_points_scaled,
            mask_h,
            mask_w,
            hm_h,
            hm_w,
            gt_labels,
            gt_colors,
        )
        type_field = field.pop("type")
        color_field = field.pop("color")
        attrs = np.stack([type_field, color_field])
        return res, field, attrs, gt_points, valid_idx2insidx

    def target(self, results):
        output_h = int(results["img_shape"][0])
        output_w = int(results["img_shape"][1])
        mask_h = int(output_h // self.down_scale)  # 8x
        mask_w = int(output_w // self.down_scale)
        hm_h = int(output_h // self.hm_down_scale)  # 16x
        hm_w = int(output_w // self.hm_down_scale)
        ratio_hm_mask = self.down_scale / self.hm_down_scale

        results["hm_shape"] = [hm_h, hm_w]
        results["mask_shape"] = [mask_h, mask_w]
        results["down_scale"] = self.down_scale
        results["hm_down_scale"] = self.hm_down_scale

        def resize(im):
            if type(im) is np.ndarray:
                return cv2.resize(
                    im,
                    (mask_w // self.property_down_rate,
                     mask_h // self.property_down_rate),
                    interpolation=cv2.INTER_NEAREST,
                )
            else:
                return np.ones(
                    (mask_h // self.property_down_rate,
                     mask_w // self.property_down_rate), np.uint8) * im

        field_dims = 3 + 2 * self.view_distance
        if self.inv_view_distance > 0:
            field_dims = field_dims + 2 * self.inv_view_distance + 1

        mask = results["laneline_mask"]
        label = results["laneline_label"]
        color = results["laneline_color"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, valid_idx2insidx = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label, color)

            self.kps2mask(mask, results.get("bks", []), gt_masks,
                          valid_idx2insidx, mask_h)

            lane = dict(key_points=gt_points, gt_masks=gt_masks)
            results["laneline_field"] = field
            results["laneline_attr"] = attribute_target
            results["laneline"] = lane
            results["ignore_laneline"] = False
        else:
            results["laneline_field"] = self.get_zero_field(mask_h, mask_w)
            results["laneline_attr"] = np.stack([
                resize(label),
                resize(color),
            ]).astype(np.long)
            results["laneline"] = dict(key_points=[], gt_masks=[])
            results["ignore_laneline"] = True
        results["laneline_start_point_off"] = start_point_off
        results["laneline_gt_hm"] = gt_hm

        mask = results["roadside_mask"]
        label = results["roadside_label"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, _ = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label)

            roadside = dict(key_points=gt_points, gt_masks=gt_masks)
            results["roadside_field"] = field
            results["roadside_attr"] = attribute_target[:1]
            results["roadside"] = roadside
            results["ignore_roadside"] = False
        else:
            results["roadside_field"] = self.get_zero_field(mask_h, mask_w)
            results["roadside_attr"] = resize(label)[None].astype(np.long)
            results["roadside"] = dict(key_points=[], gt_masks=[])
            results["ignore_roadside"] = True
        results["roadside_start_point_off"] = start_point_off
        results["roadside_gt_hm"] = gt_hm
        return True
