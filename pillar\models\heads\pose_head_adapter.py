import torch.nn as nn

from ..builder import MODELS


@MODELS.register_module()
class PoseHeadAdapter(nn.Module):
    def __init__(self, adaptee_type, **kwargs):
        super().__init__()
        kwargs['type'] = adaptee_type
        self.adaptee = MODELS.build(kwargs)
    
    def init_weights(self):
        self.adaptee.init_weights()

    def forward_train(self, x, target, target_weight):
        x = self.adaptee.forward(x)

        losses = dict()
        loss = self.adaptee.get_loss(x, target, target_weight)
        losses.update(loss)
        accuracy = self.adaptee.get_accuracy(x, target, target_weight)
        losses.update(accuracy)

        return losses
        
    def simple_test(self, 
                    feat,
                    feat_flipped=None,
                    img_metas=None,
                    img_size=None,
                    return_heatmap=False,
                    **kwargs):
        result = dict()
        output_heatmap = self.adaptee.inference_model(feat, flip_pairs=None)

        if feat_flipped is not None:
            output_flipped_heatmap = self.adaptee.inference_model(
                    feat_flipped, img_metas[0]['flip_pairs'])
            output_heatmap = (output_heatmap +
                              output_flipped_heatmap) * 0.5

        keypoint_result = self.adaptee.decode(
            img_metas, output_heatmap, img_size=img_size)
        
        result.update(keypoint_result)

        if not return_heatmap:
            output_heatmap = None

        result['output_heatmap'] = output_heatmap

        return result
    
    def forward(self, x):
        return self.adaptee.forward(x)
    
