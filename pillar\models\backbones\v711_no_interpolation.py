import math

import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule
from mmcv.runner import BaseModule
from mmcv.utils.parrots_wrapper import _BatchNorm
from mmcls.models.backbones.base_backbone import BaseBackbone

from ..builder import MODELS


class BasicBlock(BaseModule):
    def __init__(self,
                 in_channels,
                 out_channels,
                 stride=1,
                 dilation=1,
                 conv_cfg=None,
                 norm_cfg=dict(type='BN'),
                 act_cfg=dict(type='ReLU'),
                 init_cfg=None):
        super().__init__(init_cfg)

        self.conv = ConvModule(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=3,
            stride=stride,
            padding=dilation,
            dilation=dilation,
            bias=False,
            conv_cfg=conv_cfg,
            norm_cfg=norm_cfg,
            act_cfg=act_cfg,
        )

    def forward(self, x):
        x = self.conv(x)
        return x


class ConcatBlock(BaseModule):
    def __init__(self,
                 in_channels,
                 mul,
                 stride=1,
                 dilations=None,
                 conv_cfg=None,
                 norm_cfg=None,
                 act_cfg=None,
                 init_cfg=None):

        super().__init__(init_cfg)

        self.expansion = [16, 8, 4, 4] # 32
        self.expansion = [expansion * mul for expansion in self.expansion]
        if dilations is None:
            dilations = [1, 1, 1, 1]
        else:
            assert len(dilations) == 4
        self.dilations = dilations

        self.cfgs = dict(
            conv_cfg=conv_cfg,
            norm_cfg=norm_cfg,
            act_cfg=act_cfg,
        )

        blocks = list()
        expansions = [in_channels] + self.expansion
        for in_channels, out_channels, dilation in zip(expansions[:-1], expansions[1:], self.dilations):
            block = BasicBlock(
                in_channels=in_channels,
                out_channels=out_channels,
                stride=stride,
                dilation=dilation,
                **self.cfgs
            )
            blocks.append(block)
        self.blocks = nn.ModuleList(blocks)

    def forward(self, x):
        outs = list()
        for block in self.blocks:
            x = block(x)
            outs.append(x)

        x = torch.cat(outs, 1)
        return x


@MODELS.register_module()
class V711(BaseBackbone):
    def __init__(self,
                 dilations_group=None,
                 num_classes=-1,
                 conv_cfg=None,
                 norm_cfg=dict(type='BN'),
                 act_cfg=dict(type='ReLU'),
                 norm_eval=False,
                 init_cfg=None,
                 channels=32,
                 depths=[2, 3, 4],
                 in_expansions=[1, 1, 2],
                 expansions=[1, 2, 4],
                 double_concat_block_channel=False,
                ):

        super().__init__(init_cfg)

        if dilations_group is None:
            dilations_group = [None] * sum(depths)  # for each concat block
        else:
            assert len(dilations_group) == sum(depths)

        self.dilations_group = dilations_group
        self.num_classes = num_classes

        self.cfgs = dict(
            conv_cfg=conv_cfg,
            norm_cfg=norm_cfg,
            act_cfg=act_cfg,
        )

        self.norm_eval = norm_eval

        self.channels = channels
        self.depths, self.expansions, self.in_expansions = depths, expansions, in_expansions
        self.double_concat_block_channel = double_concat_block_channel

        # feature
        layers = [BasicBlock(
            in_channels=3,
            out_channels=self.channels,
            stride=2,
            **self.cfgs
        )]

        offset = 0
        for depth, in_expansion, expansion in zip(self.depths, self.in_expansions, self.expansions):
            in_channels = self.channels * in_expansion
            out_channels = self.channels * expansion
            layer = [BasicBlock(
                in_channels=in_channels,
                out_channels=out_channels,
                stride=2,
                **self.cfgs
            )]
            for idx, dilations in enumerate(self.dilations_group[offset:offset + depth]):
                if self.double_concat_block_channel and depth > 1 and idx >= int(depth / 2):
                        layer.append(ConcatBlock(
                        in_channels=out_channels * 2,
                        mul=expansion,
                        stride=1,
                        dilations=dilations,
                        **self.cfgs
                    ))
                else:
                    layer.append(ConcatBlock(
                        in_channels=out_channels,
                        mul=expansion,
                        stride=1,
                        dilations=dilations,
                        **self.cfgs
                    ))
            layers.append(nn.Sequential(*layer))
            offset += depth

        self.layers = nn.ModuleList(layers)

        if self.num_classes > 0:
            self.classifier = nn.Conv2d(in_channels, num_classes, kernel_size=1, stride=1, padding=0, bias=True)


    def forward(self, x):
        outs = list()
        for layer in self.layers:
            x = layer(x)
            outs.append(x)

        if self.num_classes > 0:
            y = self.classifier(x)
            y = y.view(x.size(0), self.num_classes)
            outs.append(y)

        return outs[-1]

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
                if m.bias is not None:
                    m.bias.data.zero_()
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()
            elif isinstance(m, nn.Linear):
                m.weight.data.normal_(0, 0.01)
                m.bias.data.zero_()

    def train(self, mode=True):
        super().train(mode)
        if mode and self.norm_eval:
            for m in self.modules():
                # trick: eval have effect on BatchNorm only
                if isinstance(m, _BatchNorm):
                    m.eval()
