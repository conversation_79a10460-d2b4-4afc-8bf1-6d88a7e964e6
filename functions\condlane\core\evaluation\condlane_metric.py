import os
import copy
import argparse
from functools import partial

import ipdb
import numpy as np
from tqdm import tqdm
from multiprocessing import Pool
from sklearn.metrics import confusion_matrix
from scipy.optimize import linear_sum_assignment

from .condlane_draw import draw_maps
from .condlane_dist import calc_iou, calc_bias, calc_dtw, lane_distance, MAX_DIST
from .condlane_format import parse_results, group_results, collect, format_dict, format_list

def lanes_match(dist, others):
    pred_idx, gt_idx = linear_sum_assignment(dist)
    pairs = np.stack([pred_idx, gt_idx], -1)
    match = {k: v[pred_idx, gt_idx].tolist() for k, v in others.items()}
    return pairs, match


def match_gt_pred(ioumap, others, iou_th=0, strict=True):
    gt_idx = np.argmax(ioumap, 1)
    pred_idx = np.arange(len(gt_idx))
    ioumax = ioumap[pred_idx, gt_idx]
    mask = ioumax > iou_th
    ioumax = ioumax[mask]
    gt_idx[~mask] = -1

    if strict:
        assigned = set()
        for i, v in enumerate(gt_idx):
            if v >= 0 and v in assigned:
                gt_idx[i] = -1
            assigned.add(v)
    filt = gt_idx >= 0
    pred_idx = pred_idx[filt]
    gt_idx = gt_idx[filt]
    pairs = np.stack([pred_idx, gt_idx], -1)
    match = {k: v[pred_idx, gt_idx].tolist() for k, v in others.items()}
    return pairs, match


def argmax(data, keys=None):
    if isinstance(data, dict):
        data = [data[k] for k in keys]
    return np.argmax(data)


def eval_bk_point(pred, gt):
    ret = dict(
        n_pred=len(pred),
        n_gt=len(gt),
        dist=[],
    )
    if ret["n_gt"] <= 0 or ret["n_pred"] <= 0:
        return ret
    pred = np.array(pred)
    gt = np.array(gt)
    dist = np.linalg.norm(pred[:, None] - gt[None, :], 2, -1)
    pred_idx, gt_idx = linear_sum_assignment(dist)
    ret["dist"].extend(dist[pred_idx, gt_idx].tolist())
    return ret


def eval_single(input, categories):
    gt, pred = input
    assert len(gt) > 0
    roi = gt[0]["roi"]
    gt_pts = [_["key_points"] - [roi["left"], roi["top"]] for _ in gt]
    confidence = [_["confidence"] for _ in pred]
    idx = np.argsort(confidence)[::-1]
    pred_pts = [pred[i]["key_points"] - [roi["left"], roi["top"]] for i in idx]

    gt_maps = draw_maps(gt_pts, roi, 1, 1)
    pred_maps = draw_maps(pred_pts, roi, 1, 1)
    bias_dist = calc_bias(pred_maps, gt_maps)

    gt_maps = draw_maps(gt_pts, roi, 4, 4)
    pred_maps = draw_maps(pred_pts, roi, 4, 4)
    mask_iou_4_4 = calc_iou(pred_maps, gt_maps)

    gt_maps = draw_maps(gt_pts, roi, 16, 1)
    pred_maps = draw_maps(pred_pts, roi, 16, 1)
    mask_iou_16_1 = calc_iou(pred_maps, gt_maps)
    points_matches = lane_distance(pred_maps, gt_maps)

    # dtw_dist = calc_dtw(pred_pts, gt_pts, None)

    others = dict(
        iou_16_1=mask_iou_16_1,
        bias=bias_dist[..., 0],
        bias_n=bias_dist[..., 1],
        bias_m=bias_dist[..., 2],
        bias_f=bias_dist[..., 3],
        # dtw=dtw_dist[..., 0],
        # dtw_n=dtw_dist[..., 1],
        # dtw_m=dtw_dist[..., 2],
        # dtw_f=dtw_dist[..., 3],
        iou_4_4=mask_iou_4_4,
    )

    lanes_matches = dict(n_gt=len(gt), n_pred=len(pred))
    # _, match = lanes_match(dtw_dist[..., 0], others)
    # lanes_matches["dtw"] = match
    # _, match = lanes_match(bias_dist[..., 0], others)
    # lanes_matches["bias"] = match
    _, match = match_gt_pred(mask_iou_4_4, others)
    lanes_matches["iou_4_4"] = match
    pairs, match = lanes_match(1 - mask_iou_16_1, others)
    lanes_matches["iou_16_1"] = match

    attr_preds = {}
    attr_gts = {}
    bk_dist = []
    for n, (i, j) in enumerate(pairs):
        if match["iou_16_1"][n] < 0.1:
            continue
        pred_attr = pred[idx[i]]["attribute"]
        for k in pred_attr:
            gt_label = gt[j]["attribute"][k]
            if gt_label == 255:
                continue
            attr_gts.setdefault(k, [])
            attr_gts[k].append(gt_label)

            pred_label = argmax(pred_attr[k], categories[k])
            attr_preds.setdefault(k, [])
            attr_preds[k].append(pred_label)

        bk_dist.append(
            eval_bk_point(
                pred[idx[i]]["break_points"],
                gt[j]["break_points"],
            ))

    return dict(
        meta=dict(
            image_path=gt[0]["image_path"],
            img_prefix=gt[0]["img_prefix"],
            scene=gt[0]["scene"],
            camera_index=[gt[0]["camera_index"]],
            pred_idx=idx[pairs[:, 0]],
            gt_idx=pairs[:, 1],
        ),
        lanes_matches=lanes_matches,
        points_matches=points_matches,
        bk_matches=bk_dist,
        attributes=dict(pred=attr_preds, gt=attr_gts),
    )


def eval_parallel(
    gts,
    preds,
    categories,
    workers=0,
    types=["LANELINE", "ROADSIDE"],
):
    fn = partial(eval_single, categories=categories)

    results_dict = {}
    for label in types:
        if label not in gts.keys():
            continue
        inputs = [[gts[label][key],
                   preds.get(label, {}).get(key, [])] for key in gts[label]]
        if workers > 1:
            pool = Pool(workers)
            results = list(tqdm(pool.imap(fn, inputs), total=len(inputs)))
            pool.close()
            pool.join()
        else:
            results = [fn(_) for _ in tqdm(inputs)]
        results_dict[label] = results

    return results_dict


def attr_metric(gts, preds, label_num, labels=None):
    if len(preds) <= 0 or len(gts) <= 0:
        return {}
    if labels is None:
        labels = np.arange(label_num)
    cm = confusion_matrix(gts, preds, labels=labels)
    TP = np.diag(cm.astype(np.float))
    TP_FP = cm.sum(0)
    TP_FN = cm.sum(1)
    prec = np.where(TP_FP > 0, TP / TP_FP, -1)
    rec = np.where(TP_FN > 0, TP / TP_FN, -1)
    return dict(
        confusion=cm.tolist(),
        prec=prec.tolist(),
        rec=rec.tolist(),
    )


def calc_f1(mask, n_pred, n_gt, **kwargs):
    tp = mask.sum()
    prec = tp / n_pred
    rec = tp / n_gt
    ret = {}
    ret.update(
        f1=2 * prec * rec / (prec + rec),
        prec=prec,
        rec=rec,
    )
    for k, v in kwargs.items():
        v = v[mask]
        v = v[v < MAX_DIST]  # ignore inf
        ret[f"{k}_mean"] = np.mean(v)
        if k == "bias":
            ret[f"{k}_std"] = np.std(v)
    ret.update(
        tp=tp,
        fp=n_pred - tp,
        fn=n_gt - tp,
    )
    return ret


def collect_eval(results, categories):
    results = collect(results)

    lanes_matches = {}
    n_pred = results["lanes_matches"].pop("n_pred")
    n_gt = results["lanes_matches"].pop("n_gt")
    if n_pred > 0 and n_gt > 0:
        th_dict = dict(
            iou_16_1=[0.1],
            iou_4_4=[0.0],
            # bias=[4],
            # dtw=[50],
        )
        for k, v in results["lanes_matches"].items():
            for _ in v:
                v[_] = np.array(v[_])
            for th in th_dict[k]:
                if "iou" in k:
                    mask = v[k] > th
                else:
                    mask = v[k] < th
                ret = calc_f1(mask, n_pred, n_gt, **v)
                if k == "iou_4_4":
                    tp = (mask & (v["bias"] < 6)).sum()
                    prec = tp / n_pred
                    rec = tp / n_gt
                    ret.update(
                        f1=2 * prec * rec / (prec + rec),
                        prec=prec,
                        rec=rec,
                    )
                lanes_matches[f"{k}_{th:.3g}"] = ret

    bk_matches = {}
    if len(results["bk_matches"]) > 0:
        results["bk_matches"] = collect(results["bk_matches"])
        n_gt = results["bk_matches"]["n_gt"]
        n_pred = results["bk_matches"]["n_pred"]
        if n_gt > 0 and n_pred > 0:
            dist = np.array(results["bk_matches"]["dist"])
            for th in [40, 70, 100]:
                bk_matches[f"dist_{th:.3g}"] = calc_f1(
                    dist < th,
                    n_pred,
                    n_gt,
                    dist=dist,
                )

    points_matches = {}
    for k, v in results["points_matches"].items():
        if len(v["dist"]) <= 0:
            continue
        points_matches[k] = dict(
            dist_mean=np.mean(v["dist"]),
            dist_std=np.std(v["dist"], ddof=1),
            miss_points=v["miss"],
        )

    attributes = {}
    attr_preds = results["attributes"]["pred"]
    attr_gts = results["attributes"]["gt"]
    for k in attr_gts:
        attributes[k] = attr_metric(
            attr_gts[k],
            attr_preds[k],
            len(categories[k]),
        )
    metrics = dict(
        lanes_matches=lanes_matches,
        points_matches=points_matches,
        bk_matches=bk_matches,
        attributes=attributes,
    )
    return metrics


def eval_group(results_dict, categories):
    metrics = dict(
        lanes_matches={},
        points_matches={},
        bk_matches={},
        attributes={},
    )
    for label, results in results_dict.items():
        metric = collect_eval(results, categories)
        metrics["lanes_matches"][label] = metric["lanes_matches"]
        metrics["points_matches"][label] = metric["points_matches"]
        metrics["bk_matches"][label] = metric["bk_matches"]
        for k, v in metric["attributes"].items():
            metrics["attributes"][k] = v

    result_str = []
    for name in ["lanes_matches", "points_matches", "bk_matches"]:
        for k, v in metrics[name].items():
            result_str.append(format_dict(v, f"{name}_{k}"))
    for k, v in metrics["attributes"].items():
        result_str.append(
            format_list(v["confusion"], v["prec"], v["rec"], f"attr_{k}"))
    result_str = "\n\n".join(result_str)

    return metrics, result_str


def group_by(results_dict, key):
    groups = {}
    for label, results in results_dict.items():
        for result in results:
            scenes = result["meta"][key]
            if isinstance(scenes, str):
                scenes = [scenes]
            for scene in scenes:
                groups.setdefault(scene, {})
                groups[scene].setdefault(label, [])
                groups[scene][label].append(result)
    groups = {k: copy.deepcopy(v) for k, v in groups.items()}
    return groups


def main(
    gt_path,
    pred_path,
    categories,
    metric=[],
    workers=0,
):
    print("Parsing ...")
    #print("===========================gt_path in main(eval) is: ================================: ", gt_path)
    gt_path = parse_results(gt_path, pkl_key="gt")
    pred_path = parse_results(pred_path, pkl_key="pred")
    gts = group_results(gt_path)
    preds = group_results(pred_path)

    print("Matching ...")
    results_dict = eval_parallel(gts, preds, categories, workers)

    print("Evaluating(functions/condlane/core/evaluation) ...")

    metrics_all, markdown_all = eval_group(results_dict, categories)
    markdown = {"all": markdown_all}
    if metric is not None and len(metric) > 0:
        results_groups = {
            **group_by(results_dict, "camera_index"),
            **group_by(results_dict, "scene"),
        }
        for group in metric:
            if group not in results_groups:
                continue
            results = results_groups[group]
            _, markdown_grp = eval_group(results, categories)
            markdown[group] = markdown_grp

    if len(markdown) <= 1:
        markdown = markdown_all
    else:
        markdown = "\n".join([f"\n# {k}\n\n{v}" for k, v in markdown.items()])

    # todo 修改这一部分的返回数值
    # with open("metric.md", "w") as f:
    #     f.write(markdown)
    #     print('markdown_all = {}'.format(markdown_all))
    #     print('save markdown_all >> ./metric.md', flush=True)
    #
    print(markdown, flush=True)
    return metrics_all
    # return markdown

if __name__ == "__main__":
    default_categories = {
        "lane_type": [
            "NO_LANE",
            "OTHER_LANE_TYPE",
            "SOLID_LANE",
            "DASHED_LANE",
            "SHORT_THICK_DASHED_LANE",
            "SPLIT_LANE",
            "MERGE_LANE",
            "LEFT_DASHED_RIGHT_SOLID",
            "LEFT_SOLID_RIGHT_DASHED",
            "DOUBLE_SOLID",
            "DOUBLE_DASHED",
            "FISHBONE_SOLID",
            "FISHBONE_DASHED",
        ],
        "lane_color": [
            "NO_LANE",
            "OTHER_LANE_COLOR",
            "WHITE",
            "YELLOW",
            "ORANGE",
            "BLUE",
        ],
        "roadside": [
            "NO_SIDE",
            "ROAD_CURB_EDGE",
            "FENCE_EDGE",
            "WALL_EDGE_OR_PLANT_EDGE",
            "MOVABLE_EDGE",
            "OTHER_EDGE",
        ],
    }
    ms_alone_categories = {
        "lane_type": [
            "NO_LANE",
            "OTHER_LANE_TYPE",
            "SOLID_LANE",
            "DASHED_LANE",
            "SHORT_THICK_DASHED_LANE",
            "LEFT_DASHED_RIGHT_SOLID",
            "LEFT_SOLID_RIGHT_DASHED",
            "DOUBLE_SOLID",
            "DOUBLE_DASHED",
            "FISHBONE_SOLID",
            "FISHBONE_DASHED",
        ],
        "lane_color": [
            "NO_LANE",
            "OTHER_LANE_COLOR",
            "WHITE",
            "YELLOW",
            "ORANGE",
            "BLUE",
        ],
        "roadside": [
            "NO_SIDE",
            "ROAD_CURB_EDGE",
            "FENCE_EDGE",
            "WALL_EDGE_OR_PLANT_EDGE",
            "MOVABLE_EDGE",
            "OTHER_EDGE",
        ],
        "lane_ms": [
            "NORM_LANE",
            "SPLIT_LANE",
            "MERGE_LANE",
        ]
    }
    parser = argparse.ArgumentParser(
        description="Pytorch Semantic Segmentation Evalutaion")
    parser.add_argument("gt_path", type=str)
    parser.add_argument("pred_path", type=str)
    parser.add_argument("-n", "--workers", default=0, type=int)
    parser.add_argument("--ms", action='store_true', help="alone mergesplit")
    parser.add_argument(
        "--metric",
        type=str,
        nargs="*",
    )
    args = parser.parse_args()
    metrics = main(
        **vars(args),
        categories=ms_alone_categories if args.ms else default_categories,
    )
