import cv2
import ipdb
import numpy as np
from scipy.interpolate import interp1d

from pillar.datasets import PIPELINES
from .lkd_lane_cls_formation import CollectLkdClsLane, gather_attr


def sigmoid(z):
    return np.where(z >= 0, 1 / (1 + np.exp(-z)), np.exp(z) / (1 + np.exp(z)))


def curve_interp(points, y_=None):
    y, x = points[:, 1], points[:, 0]
    if y_ is None:
        y_min = int(np.ceil(y.min()))
        y_max = int(np.floor(y.max()))
        y_ = np.linspace(y_min, y_max, int(y_max - y_min + 1))
    interp = interp1d(y, x, fill_value="extrapolate")
    x_ = interp(y_)
    points_ = np.stack([x_, y_], -1)
    return points_


def mask2points(mask):
    points = []
    idx2insidx = []
    if type(mask) is not np.ndarray:
        return points, idx2insidx

    instances = np.unique(mask).tolist()
    if 0 in instances:
        instances.remove(0)
    if 255 in instances:
        instances.remove(255)

    for instance in instances:
        instance_mask = mask == instance
        y, x = np.where(instance_mask)
        y_max = y.max()
        y_min = y.min()
        y = np.array([*range(y_min, y_max), y_max])
        instance_mask = instance_mask[y]
        row_mask = instance_mask.any(-1)
        if row_mask.sum() <= 1:
            continue
        instance_mask = instance_mask[row_mask]
        x = np.arange(0, instance_mask.shape[-1])
        x = (instance_mask * x).sum(-1) / instance_mask.sum(-1)
        ins_points = np.stack([x, y[row_mask]], -1).astype(np.float32)
        ins_points = curve_interp(ins_points)
        points.append(ins_points)
        idx2insidx.append(instance)
    return points, idx2insidx


def update_visit(line, H, W, th=2):
    visit = np.zeros([H, W], dtype=np.bool)
    L = len(line)

    def expand(ss, es):
        ss_ = []
        es_ = []
        for i, (start, stop) in enumerate(zip(ss, es)):
            if i > 0:
                start = min(start, ss[i - 1])
                stop = max(stop, es[i - 1])
            if i < L - 1:
                start = min(start, ss[i + 1])
                stop = max(stop, es[i + 1])
            ss_.append(start)
            es_.append(stop)
        return ss_, es_

    starts = [x - th for x, y in line]
    stops = [x + th for x, y in line]
    for _ in range(2):
        starts, stops = expand(starts, stops)

    for i, (x, y) in enumerate(line):
        y = int(y)
        if y < 0 or y >= H:
            continue
        start = starts[i]
        stop = stops[i]
        start = max(int(np.floor(start)), 0)
        stop = min(int(np.ceil(stop)), W - 1)
        visit[y, start:stop + 1] = True
        if i == 0 and y > 0:
            visit[y - 1, start:stop + 1] = True
        if i == L - 1 and y < H - 1:
            visit[y + 1, start:stop + 1] = True
    return visit


def default_field(H, W):
    field = dict(
        off_field=np.zeros([H, W], dtype=np.float32),
        len_forward=np.zeros([H, W], dtype=np.float32),
        off_forward=np.zeros([H, W], dtype=np.float32),
        len_backward=np.zeros([H, W], dtype=np.float32),
        off_backward=np.zeros([H, W], dtype=np.float32),
        label=np.zeros([H, W], dtype=np.int64),
        color=np.zeros([H, W], dtype=np.int64),
    )
    return field


def raised_cosine(H, y_min, y_max):
    y = np.ones(H, dtype=np.float32)
    y[:y_min] = 0
    y[y_min:y_max + 1] = np.linspace(0, 1, y_max - y_min + 1)
    y = np.cos(y * np.pi)
    y = (y + 3) * 0.25
    return y


def calc_field(mask, points, H, W, label, color=255):
    field = default_field(H, W)

    field["label"][mask] = label
    field["color"][mask] = color

    ys, xs = np.where(mask)

    y_min = points[:, 1].min()
    y_max = points[:, 1].max()
    field["len_forward"][ys, xs] = ys - y_min
    field["len_backward"][ys, xs] = y_max - ys

    points = curve_interp(points, np.arange(H))
    field["off_field"][ys, xs] = points[ys, 0] - xs
    m = (ys - 1) >= 0
    field["off_forward"][ys[m], xs[m]] = points[ys[m] - 1, 0] - xs[m]
    m = (ys + 1) < H
    field["off_backward"][ys[m], xs[m]] = points[ys[m] + 1, 0] - xs[m]

    return field


def points2field(
    points,
    H,
    W,
    labels,
    colors,
    th=2,
    soft_label=False,
    soft_weight_x=False,
    soft_weight_y=False,
):
    field = default_field(H, W)
    weight = np.ones([H, W], dtype=np.float32)
    line_mask = np.zeros([H, W], dtype=np.float32)
    L = len(points)
    if L <= 0:
        field["weight"] = weight
        field["line_mask"] = line_mask
        return field
    masks = []
    fields = []
    for i, point in enumerate(points):
        mask_i = update_visit(point, H, W, th=th)
        field_i = calc_field(mask_i, point, H, W, labels[i], colors[i])
        masks.append(mask_i)
        fields.append(field_i)
    masks = np.stack(masks)
    fields = {k: np.stack([v[k] for v in fields]) for k in field}
    ys, xs = np.where(masks.sum(0) > 1)
    for yi, xi in zip(ys, xs):
        cs = np.where(masks[:, yi, xi])[0]
        off = [abs(fields["off_field"][ci, yi, xi]) for ci in cs]
        i = np.argmin(off)
        masks[:, yi, xi] = False
        masks[cs[i], yi, xi] = True
    assert not (masks.sum(0) > 1).any()
    cs, ys, xs = np.where(masks)
    for k in field:
        field[k][ys, xs] = fields[k][cs, ys, xs]
    mask = masks.any(0)
    field["line_mask"] = mask.astype(np.float32)
    x_weight = sigmoid(-np.abs(field["off_field"])) + 0.5
    if soft_label:
        field["line_mask"][mask] = x_weight[mask]
    y_weight = raised_cosine(H, ys.min(), ys.max())[..., None]
    field["weight"] = weight
    if soft_weight_x and soft_weight_y:
        soft_weight = weight * np.sqrt(x_weight * y_weight)
    elif soft_weight_x:
        soft_weight = weight * x_weight
    elif soft_weight_y:
        soft_weight = weight * y_weight
    else:
        soft_weight = None
    if soft_weight is not None:
        field["weight"][mask] = soft_weight[mask]
    return field


@PIPELINES.register_module()
class CollectFieldLaneBeta(CollectLkdClsLane):

    def __init__(
        self,
        property_down_rate=1,
        super_grid_ratio=1,
        line_width=2,
        soft_label=True,
        soft_weight_x=False,
        soft_weight_y=False,
        meta_keys=[
            'filename',
            'img_prefix',
            'sub_img_name',
            'img_shape',
            'ori_shape',
            'roi',
            'bk_roi',
            'break_points',
            'img_norm_cfg',
            'hm_shape',
            'mask_shape',
            'down_scale',
            'hm_down_scale',
            'laneline',
            'roadside',
            'scene',
            'camera_index',
        ],
        keys=[
            'img',
            'ignore_laneline',
            'ignore_roadside',
            'laneline_mask',
            'roadside_mask',
            'laneline_line_mask',
            'roadside_line_mask',
            'laneline_off_field',
            'roadside_off_field',
            'laneline_len_forward',
            'roadside_len_forward',
            'laneline_off_forward',
            'roadside_off_forward',
            'laneline_len_backward',
            'roadside_len_backward',
            'laneline_off_backward',
            'roadside_off_backward',
            'laneline_weight',
            'roadside_weight',
            'laneline_type',
            'roadside_type',
            'laneline_color',
        ],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.super_grid_ratio = super_grid_ratio
        self.property_down_rate = property_down_rate
        self.line_width = line_width
        self.soft_label = soft_label
        self.soft_weight_x = soft_weight_x
        self.soft_weight_y = soft_weight_y
        # some default keys, fix it in code to simple the config
        self.meta_keys = meta_keys
        self.keys = keys

    def mask2points(self, anno_mask, w, h, anno_label=255, anno_color=255):
        points, idx2insidx = mask2points(anno_mask)
        points_ori = []
        points_scale = []
        labels = []
        colors = []

        for ins_points in points:
            H, W = anno_mask.shape[-2:]
            ## points scale
            ins_points_scale = curve_interp(ins_points * [w / W, h / H])
            if len(ins_points) <= 1 or len(ins_points_scale) <= 1:
                continue
            points_ori.append(ins_points)
            points_scale.append(ins_points_scale)

            ## gather attributes
            ins_points = ins_points.astype(np.int64)
            labels.append(
                gather_attr(anno_label, ins_points[:, 0], ins_points[:, 1]))
            colors.append(
                gather_attr(anno_color, ins_points[:, 0], ins_points[:, 1]))
        return idx2insidx, points_ori, points_scale, labels, colors

    def process_mask(
        self,
        mask,
        w,
        h,
        anno_label=255,
        anno_color=255,
    ):
        valid_idx2insidx, gt_points, gt_points_scale, gt_labels, gt_colors = self.mask2points(
            mask,
            w,
            h,
            anno_label,
            anno_color,
        )

        field = points2field(
            gt_points_scale,
            h,
            w,
            gt_labels,
            gt_colors,
            th=self.line_width,
            soft_label=self.soft_label,
            soft_weight_x=self.soft_weight_x,
            soft_weight_y=self.soft_weight_y,
        )

        # regression
        res = []
        for insidx in range(len(gt_points)):
            info = dict(
                type=gt_labels[insidx],
                color=gt_colors[insidx],
            )
            res.append(info)
        res = dict(gt_masks=res, key_points=gt_points)

        return res, field, valid_idx2insidx

    def fmt_input(self, results, line_type):
        [hm_h, hm_w] = results["hm_shape"]
        [mask_h, mask_w] = results["mask_shape"]

        mask = results[f"{line_type}_mask"]
        label = results[f"{line_type}_label"]
        color = results.get(f"{line_type}_color", 255)

        res, field, valid_idx2insidx = self.process_mask(
            mask,
            mask_w * self.super_grid_ratio,
            mask_h,
            label,
            color,
        )

        ignore = len(res["gt_masks"]) <= 0
        results[f"{line_type}"] = res
        results[f"ignore_{line_type}"] = ignore
        field["type"] = field.pop("label")
        for k, v in field.items():
            if "mask" in k:
                v = cv2.resize(
                    v.astype(np.float32),
                    (mask_w, mask_h),
                    interpolation=cv2.INTER_NEAREST,
                )[None]
            elif "len" in k or "weight" in k:
                v = cv2.resize(
                    v,
                    (mask_w, mask_h),
                    interpolation=cv2.INTER_NEAREST,
                )[None]
            elif "off" in k:
                v = v.reshape(mask_h, -1, self.super_grid_ratio)
                v = v.transpose(2, 0, 1)
                v = v / self.super_grid_ratio
            elif "type" in k or "color" in k:
                v = cv2.resize(
                    v,
                    (hm_w, hm_h),
                    interpolation=cv2.INTER_NEAREST,
                )[None]
            results[f"{line_type}_{k}"] = v
        results[f"{line_type}_mask"] = results[f"{line_type}_line_mask"] > 0

        # if line_type == "laneline":
        #     self.kps2mask(
        #         mask,
        #         results.get("break_points", []),
        #         gt_masks,
        #         valid_idx2insidx,
        #         mask_h,
        #     )

    def target(self, results):
        output_h = int(results["img_shape"][0])
        output_w = int(results["img_shape"][1])
        mask_h = int(output_h // self.down_scale)  # 8x
        mask_w = int(output_w // self.down_scale)
        hm_h = int(output_h // self.hm_down_scale)  # 16x
        hm_w = int(output_w // self.hm_down_scale)

        results["hm_shape"] = [hm_h, hm_w]
        results["mask_shape"] = [mask_h, mask_w]
        results["down_scale"] = self.down_scale
        results["hm_down_scale"] = self.hm_down_scale

        self.fmt_input(results, "laneline")
        self.fmt_input(results, "roadside")

        return True
