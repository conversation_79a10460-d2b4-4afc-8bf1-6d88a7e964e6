import json

# Load the JSON file
with open("cleaned_cpp_results_with_images.json", "r") as f:
    json_data = json.load(f)

# Function to determine the lane type and color
def get_lane_type_and_color(attributes):
    lane_type_scores = {
        "NO_LANE": attributes[0][0], 
        "OTHER_LANE_TYPE": attributes[0][1], 
        "SOLID_LANE": attributes[0][2], 
        "DASHED_LANE": attributes[0][3], 
        "SHORT_THICK_DASHED_LANE": attributes[0][4], 
        "SPLIT_LANE": attributes[0][5], 
        "MERGE_LANE": attributes[0][6], 
        "LEFT_DASHED_RIGHT_SOLID": attributes[0][7], 
        "LEFT_SOLID_RIGHT_DASHED": attributes[0][8], 
        "DOUBLE_SOLID": attributes[0][9], 
        "DOUBLE_DASHED": attributes[0][10], 
        "FISHBONE_SOLID": attributes[0][11], 
        "FISHBONE_DASHED": attributes[0][12]
    }
    
    lane_color_scores = {
        "NO_LANE": attributes[1][0], 
        "OTHER_LANE_COLOR": attributes[1][1], 
        "WHITE": attributes[1][2], 
        "YELLOW": attributes[1][3], 
        "ORANGE": attributes[1][4], 
        "BLUE": attributes[1][5]
    }
    
    return lane_type_scores, lane_color_scores

# Reformat data
formatted_data = []
for image_name, detections in json_data.items():
    for detection in detections:
        reformatted_entry = {}
        
        # Label based on type
        reformatted_entry["label"] = "LANELINE" if detection["type"] == 0 else "ROADSIDE"
        
        # Extract ROI
        scale = detection["scale"]
        reformatted_entry["roi"] = {
            "top": 0.0,
            "left": 0.0,
            "height": 590,
            "width": 1640,
            "#keson_code": "RCT"
        }
        
        # Extract confidence from score
        reformatted_entry["confidence"] = detection["score"]
        
        # Extract and scale keypoints
        reformatted_entry["key_points"] = {
            "$kesonType": "array",
            "points": [{"x": kp[0]*(1680/216), "y": kp[1]*(540/72)} for kp in detection["key_points"]]    #notice rescaling keypoints
            #"points": [{"x": kp[0], "y": kp[1]} for kp in detection["key_points"]]
        }
        
        # Determine lane type and color
        lane_type_scores, lane_color_scores = get_lane_type_and_color(detection["attributes"])
        reformatted_entry["attribute"] = {
            "lane_type": lane_type_scores,
            "lane_color": lane_color_scores
        }
        
        # Create 'proposal' field
        reformatted_entry["proposal"] = [
            [detection["score"], 1, [detection["key_points"][0][0] * (1680/216), detection["key_points"][0][1] * (540/72)], "START_PROPOSAL"]
        ]
        
        # Extract image path and construct the final image_path
        reformatted_entry["image_path"] = f"filename/{image_name}.jpg" #change
        reformatted_entry["img_prefix"] = "/work/home/<USER>/niloofar/new/data/large_new_data_batch3_test_cropped/filename" #change
        
        formatted_data.append(reformatted_entry)

# Save to a new file
#abs_path = '/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/eval_files/reformatted_cpp_results.txt'
with open("eval_files/reformatted_cpp_results.txt", "w") as f:
    for entry in formatted_data:
        json.dump(entry, f)
        f.write("\n")

#print("Reformatting complete. Results saved to eval_files/reformatted_cpp_results.txt.")