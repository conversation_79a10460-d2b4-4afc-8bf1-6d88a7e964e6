import cv2
import numpy as np
import torch
from pillar.datasets import PIPELINES

@PIPELINES.register_module()
class DepthCompletion(object):

    def __init__(self, max_depth=100, custom_kernel=None, extrapolate=False, blur_type='bilateral'):
        self.max_depth = max_depth
        self.custom_kernel = np.array(custom_kernel, dtype=np.uint8) if custom_kernel is not None else None
        self.extrapolate = extrapolate
        self.blur_type = blur_type
        self.FULL_KERNEL_5 = np.ones((5, 5), dtype=np.uint8)
        self.FULL_KERNEL_7 = np.ones((7, 7), dtype=np.uint8)
        self.FULL_KERNEL_31 = np.ones((31, 31), dtype=np.uint8)

        
    def fill_in_fast(self, depth_map, max_depth=100.0, custom_kernel=None,
                    extrapolate=False, blur_type='bilateral'):
        """Fast, in-place depth completion for a sparse depth map.

        Args:
            depth_map: A 2D numpy array of shape (H, W) containing depth values 
                    at sparse locations (non-zero) and zeros elsewhere.
            max_depth: The maximum depth value used for inversion.
            custom_kernel: The kernel used for the initial dilation.
            extrapolate: If True, extrapolate depth values to the top of the image.
            blur_type: Either 'bilateral' or 'gaussian'. Bilateral preserves edges.

        Returns:
            depth_map: A dense depth map as a 2D numpy array.
        """
        # Inversion: Convert depths so that smaller (closer) depths become larger.
        valid_pixels = (depth_map > 0.1)
        depth_map[valid_pixels] = max_depth - depth_map[valid_pixels]

        # Dilate with custom (diamond) kernel to spread valid depths.
        depth_map = cv2.dilate(depth_map, custom_kernel)

        # Hole closing using a full 5x5 kernel.
        depth_map = cv2.morphologyEx(depth_map, cv2.MORPH_CLOSE, self.FULL_KERNEL_5)

        # Fill empty (invalid) pixels with dilated values.
        empty_pixels = (depth_map < 0.1)
        dilated = cv2.dilate(depth_map, self.FULL_KERNEL_7)
        depth_map[empty_pixels] = dilated[empty_pixels]

        # Optionally extrapolate depth upward.
        if extrapolate:
            top_row_pixels = np.argmax(depth_map > 0.1, axis=0)
            top_pixel_values = depth_map[top_row_pixels, range(depth_map.shape[1])]
            for pixel_col_idx in range(depth_map.shape[1]):
                depth_map[0:top_row_pixels[pixel_col_idx], pixel_col_idx] = top_pixel_values[pixel_col_idx]
            empty_pixels = (depth_map < 0.1)
            dilated = cv2.dilate(depth_map, self.FULL_KERNEL_31)
            depth_map[empty_pixels] = dilated[empty_pixels]

        # Apply median blur to smooth out noise.
        depth_map = cv2.medianBlur(depth_map, 5)

        # Apply bilateral or Gaussian blur.
        if blur_type == 'bilateral':
            depth_map = cv2.bilateralFilter(depth_map, 5, 1.5, 2.0)
        elif blur_type == 'gaussian':
            valid_pixels = (depth_map > 0.1)
            blurred = cv2.GaussianBlur(depth_map, (5, 5), 0)
            depth_map[valid_pixels] = blurred[valid_pixels]

        # Invert back to original scale.
        valid_pixels = (depth_map > 0.1)
        depth_map[valid_pixels] = max_depth - depth_map[valid_pixels]

        return depth_map

    def __call__(self, results):
        sparse_depth = results["depth_map"]
        dense_depth_map = self.fill_in_fast(sparse_depth, self.max_depth, self.custom_kernel, self.extrapolate, self.blur_type)
        dense_depth_map = torch.from_numpy(dense_depth_map).unsqueeze(0).float()
        results["depth_map"] = dense_depth_map
        return results
