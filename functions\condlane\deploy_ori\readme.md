# Deployment

* 自动解析config.py中的数据集、crop、mean和std等信息
* 网络各级输入的batchsize、model_name、version等信息不在从命令行传入，改由config.py配置
* 添加自动生成数据集的子命令，自动创建align和quant子目录，生成对应的json，建议遵照提示用命令上传，一阶段batch控制集子规模
* 自动创建tokestrel子目录，自动生成所需json、onnx并都放入子目录，生成model.tar，可用命令或网页上传
* 如需指定发布的模型名版本号以及参数，详见deploy/__main__.py
* sh脚本中的 **ROOT** 需略作调整

``` bash
# for dataset:
bash deploy.sh Test todataset config.py /path/to/ckpt
# for model:
bash deploy.sh Test tokestrel config.py /path/to/ckpt
```