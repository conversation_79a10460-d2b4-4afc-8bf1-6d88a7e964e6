import copy
import torch
import torch.nn as nn
from abc import ABCMeta, abstractmethod
from mmcv.cnn import ConvModule
from mmcv.runner import BaseModule, auto_fp16, force_fp32
from pillar.models import MODELS


@MODELS.register_module()
class BaseHead(BaseModule, metaclass=ABCMeta):
    def __init__(self, loss_cfg=None, init_cfg=None, train_cfg=None, test_cfg=None, is_auxiliary=False):
        super(BaseHead, self).__init__(init_cfg)
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.is_auxiliary = is_auxiliary

        # build loss
        if isinstance(loss_cfg, (list, tuple)):
            losses_cfg = loss_cfg
        elif isinstance(loss_cfg, dict):
            losses_cfg = [loss_cfg]
        elif loss_cfg is None:
            losses_cfg = []
        else:
            raise TypeError(f'loss_cfg must be a dict or sequence of dict, but got {type(loss_cfg)}')
        
        self.loss_group, self.kwargs_group = [], []
        for loss_cfg in losses_cfg:
            kwargs = loss_cfg.pop("kwargs") if "kwargs" in loss_cfg else {}
            self.kwargs_group.append(kwargs)
            loss = MODELS.build(loss_cfg)
            self.loss_group.append(loss)
        self.loss_group = nn.ModuleList(self.loss_group)
    
    @auto_fp16()
    @abstractmethod
    def forward(self, inputs):
        """Placeholder of forward function."""
        raise NotImplementedError

    @force_fp32(apply_to=('pred', ))
    def losses(self, pred, gt_label):
        loss_dict = dict()
        for loss, kwargs in zip(self.loss_group, self.kwargs_group):
            kwargs = copy.deepcopy(kwargs)
            loss_name = loss.loss_name
            if isinstance(gt_label, dict):
                assert 'label_name' in kwargs, "loss must have 'label_name' in kwargs when gt_label is a dict"
                label_list = [gt_label[key] for key in kwargs['label_name']]
                kwargs.pop('label_name')
                loss_dict[loss_name] = loss(pred, *label_list, **kwargs)
            elif isinstance(gt_label, torch.Tensor):
                loss_dict[loss_name] = loss(pred, gt_label, **kwargs)
            else:
                raise TypeError(f'invalid gt_label type: {type(gt_label)}')
        return loss_dict

    def forward_train(self, x, gt_label):
        """Forward function for training.
        Args:
            x (list[Tensor]): List of multi-level img features.
            gt_label (Tensor): label

        Returns:
            dict[str, Tensor]: a dictionary of loss components
        """
        pred = self.forward(x)
        losses = self.losses(pred, gt_label)
        return losses

    def simple_test(self, x, post_process=True, **kwargs):
        """Forward function for testing. """
        if self.is_auxiliary:
            return dict()
        
        pred = self.forward(x)
        if post_process:
            return self.post_process(pred, **kwargs)
        return pred

    @abstractmethod
    def post_process(self, pred, **kwargs):
        """Placeholder of post_process function."""
        raise NotImplementedError