import torch.nn as nn

from ..builder import MODELS


@MODELS.register_module()
class PoseBackboneAdapter(nn.Module):
    def __init__(self, adaptee_type, init_cfg=None, **kwargs):
        super().__init__()
        kwargs['type'] = adaptee_type
        self.adaptee = MODELS.build(kwargs)
        self.init_cfg = init_cfg

    def init_weights(self):
        pretrained = None
        if self.init_cfg is not None:
            if isinstance(self.init_cfg, dict):
                self.init_cfg = [self.init_cfg]
        
            # ignore other init params in self.init_cfg, because in `mmpose`, all
            # weight init params are specified in Backbone `init_weights` except
            # for `Pretrained`.
            for cfg in self.init_cfg:
                if cfg['type'] == 'Pretrained':
                    pretrained = cfg['checkpoint']
        self.adaptee.init_weights(pretrained)

    def forward(self, x):
        return self.adaptee.forward(x)
    
    def train(self, mode=True):
        self.adaptee.train(mode)
    