import cv2
import numpy as np


def sigmoid(x):
    return 1. / (1. + np.exp(-x))


def draw_lines(img, lane, color, width):
    #print("=======INSIDE DRAW_LINES IMG.SHAPE IS========: ", img.shape)
    #print("=======INSIDE DRAW_LINES IMG IS========: ", img)
    for i in range(len(lane) - 1):
        cv2.line(img, tuple(lane[i]), tuple(lane[i + 1]), color, width)
    return img


def draw_maps(lanes, roi, width, scale):
    h = int(np.ceil(roi["height"] / scale))
    w = int(np.ceil(roi["width"] / scale))
    maps = np.zeros((len(lanes), h, w), dtype=np.uint8)
    for idx, points in enumerate(lanes):
        points = (points / scale).astype(np.int32).tolist()
        draw_lines(maps[idx], points, 1, width)
    return maps.astype(np.bool)


def draw_iou(preds, gts):
    M, H, W = preds.shape
    N = len(gts)
    mid = np.zeros([H, W], dtype=np.uint8)
    mid = cv2.rectangle(mid, (0, 0), (W, H), 1, 2)
    mid = np.tile(mid, (M, N, 1, 1))
    preds = np.tile(preds[:, None], (1, N, 1, 1))
    gts = np.tile(gts[None, :], (M, 1, 1, 1))
    vis = np.stack([preds, mid, gts], -1) * 255
    vis = np.transpose(vis, [0, 2, 1, 3, 4]).reshape(M * H, N * W, 3)
    cv2.imwrite("iou.png", vis.astype(np.uint8))


def draw_match(img, pred, gt, matches, width):
    pred = pred.astype(np.int32).tolist()
    gt = gt.astype(np.int32).tolist()
    draw_lines(img, pred, (255, 0, 0), width * 2)
    draw_lines(img, gt, (0, 0, 255), width * 2)
    for i, j in matches:
        cv2.line(img, tuple(pred[i]), tuple(gt[j]), (0, 255, 0), width // 2)
    return img


def draw_result(img, lines, label_s=0, project=0):
    if project in [1, 3]:
        h, w = img.shape[:2]
        img = trans_img(img, h, w)
    for line in lines:
        if line["label"] in [0, "LANELINE", "laneline"]:
            img = draw_lines_vis(img,
                                 line["key_points"],
                                 0 + label_s,
                                 line["attribute"],
                                 confidence=line.get("confidence", -1))
            img = draw_points(img, line.get("break_points", []))
        else:
            img = draw_lines_vis(img,
                                 line["key_points"],
                                 1 + label_s,
                                 line["attribute"],
                                 confidence=line.get("confidence", -1))

        proposals = line.get("proposal", [])
        if len(proposals):
            proposal = proposals[0]
            if proposal[3] == "START_PROPOSAL":
                color = (255, 0, 0)
            else:
                color = (255, 255, 0)
            thick = 2 if len(line["key_points"]["points"]) else 1
            img = cv2.circle(img, (int(proposal[2][0]), int(proposal[2][1])),
                             2, color, thick)
    return img


def draw_points(img, points):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    for pt in points:
        img = cv2.circle(img, (int(pt[0]), int(pt[1])), 16, (0, 0, 255), 2)
    return img


def draw_lines_vis(img,
                   points,
                   label,
                   attribute=None,
                   off=200,
                   confidence=1.0):
    color_list = [(0, 255, 255), (0, 255, 0), (255, 0, 0), (0, 0, 255)]
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    if points == []:
        return img
    for i in range(len(points) - 1):
        img = cv2.line(img, (int(points[i][0]), int(points[i][1])),
                       (int(points[i + 1][0]), int(points[i + 1][1])),
                       color_list[label], 4)
    if label < 2 and attribute is not None:
        text = [f"conf_{round(sigmoid(confidence), 4)}"]
        x, y = points[-1] if points[-1][-1] >= points[0][-1] else points[0]
        x, y = map(int, (x, y))
        if x > img.shape[1] // 2:
            x -= off
        if label:
            y -= 20
        for k, v in attribute.items():
            if isinstance(v, dict):
                att_name = list(v.keys())
                att_score = list(v.values())
                att = att_name[np.argmax(att_score)]
            else:
                att = v
                att_score = 0
            text.append(f"{att}_{round(np.max(att_score), 4)}")
        #text = ",".join(text)
        for i, tt in enumerate(text):
            img = cv2.putText(
                img,
                tt,
                (x, y + i * 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                color_list[label],
                1,
            )
    return img
