from mmcv.cnn import ConvModule as _ConvModule


class ConvModule(_ConvModule):
    def __init__(self,
                 *args,
                 init_override=True,
                 **kwargs):
        """A conv block inherits from `mmcv.cnn.ConvModule`.

        The default behaviour of `pillar.models.ConvModule` is the same as
        `mmcv.cnn.ConvModule`.
        If `init_override==False`, it will skip the super().init_weights(),
        and use the init_params from parents' init_cfg or torch init.
        """
        self.init_override = init_override
        super().__init__(*args, **kwargs)

    def init_weights(self):
        if self.init_override:
            return super().init_weights()
