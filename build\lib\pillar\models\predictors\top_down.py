from mmpose.models.detectors import TopDown as _TopDown

from ..builder import MODELS


@MODELS.register_module()
class TopDown(_TopDown):
    def __init__(self,
                 backbone,
                 neck=None,
                 keypoint_head=None,
                 train_cfg=None,
                 test_cfg=None,
                 pretrained=None,
                 loss_pose=None,
                 init_cfg=None):
        if init_cfg is not None and 'init_cfg' not in backbone:
            backbone['init_cfg'] = init_cfg
        super().__init__(backbone, neck, keypoint_head, train_cfg,
                         test_cfg, pretrained, loss_pose)

    # it will be called twice in `_TopDown.__init__` and
    # tools/train.py, just a workaround for compatible 
    # input params in `backbone.init_weights()` of other repo.
    def init_weights(self, **kwargs):
        self.backbone.init_weights()
        if self.with_neck:
            self.neck.init_weights()
        if self.with_keypoint:
            self.keypoint_head.init_weights()

    def extract_feat(self, img):
        """Directly extract features from the backbone+neck."""
        x = self.backbone(img)
        if self.with_neck:
            x = self.neck(x)
        return x
    
    def forward_train(self, img, target, target_weight, img_metas, **kwargs):
        x = self.extract_feat(img)

        losses = dict()
        if self.with_keypoint:
            losses = self.keypoint_head.forward_train(x, target, target_weight)

        return losses

    def forward_test(self, imgs, img_metas, post_process=False, **kwargs):
        """Test for post-process"""
        result = self.simple_test(imgs, img_metas, **kwargs)

        if result.get('output_heatmap') is None:
            result.pop('output_heatmap', None)

        if post_process:
            result = self.post_process(result)
        return result

    def simple_test(self, img, img_metas, return_heatmap=False):
        assert img.size(0) == len(img_metas)
        batch_size, _, img_height, img_width = img.shape
        img_size = [img_width, img_height]
        if batch_size > 1:
            assert 'bbox_id' in img_metas[0]

        feat = self.extract_feat(img)

        if self.test_cfg.get('flip_test', True):
            img_flipped = img.flip(3)
            feat_flipped = self.extract_feat(img_flipped)

        result = dict()
        if self.with_keypoint:
            result = self.keypoint_head.simple_test(feat, feat_flipped, img_metas, 
                                                    img_size, return_heatmap)

        return result