import os
import argparse
import shutil

import torch
import onnx
from onnxsim import simplify
import mme
from mmcv import Config
from mmcv.cnn import fuse_conv_bn

from pillar.apis import init_model
from tools.deployment.adela import AdelaClient


def parse_args():
    parser = argparse.ArgumentParser(
        description='Convert pytorch model to backends.')
    parser.add_argument('model_cfg', help='model config path')
    parser.add_argument('deploy_cfg', help='deploy config path')
    parser.add_argument('checkpoint', help='model checkpoint path')
    parser.add_argument('--output-dir', help='the dir to save logs and models')
    parser.add_argument(
        '--device', help='device used for conversion', default='cuda:0')
    parser.add_argument(
        '--overwrite', action='store_true', help='overwrite or not')

    args = parser.parse_args()

    return args


class Deployer:
    def __init__(self, deploy_cfg, model_cfg, checkpoint, work_dir, device='cuda:0'):
        self.deploy_cfg = Config.fromfile(deploy_cfg)
        self.model_cfg = Config.fromfile(model_cfg)
        self.checkpoint = checkpoint
        self.work_dir = work_dir
        self.device = device
        self.set_model()
        if hasattr(self.deploy_cfg, 'adela_config'):
            cfg = self.deploy_cfg.adela_config
            self.adela_client = AdelaClient(cfg.project_id, cfg.deployments, cfg.parameters,
                                            cfg.meta, self.work_dir)
        else:
            self.adela_client = None

    def set_model(self):
        mme.utils.DEFAULT_SCOPE = self.model_cfg.get('default_scope', None)

        # TODO: replace MMSyncBN with BN
        # import modules from string list.
        if self.model_cfg.get('custom_imports', None):
            from mmcv.utils import import_modules_from_strings
            import_modules_from_strings(**self.model_cfg['custom_imports'])
        # set cudnn_benchmark
        if self.model_cfg.get('cudnn_benchmark', False):
            torch.backends.cudnn.benchmark = True

        # build the model and load checkpoint  
        self.model = init_model(self.model_cfg, self.checkpoint, device=self.device)
        self.model.eval()

        forward = getattr(self.deploy_cfg, 'forward', 'forward_dummy')
        if hasattr(self.model, forward):
            self.model.forward = getattr(self.model, forward)
        else:
            raise NotImplementedError(
                f'model deployment is currently not supported with {self.model.__class__.__name__}')
    
    def torch2caffe(self):
        from spring.nart.tools import pytorch
        
        # pytorch model -> onnx export & caffe model
        caffe_name = os.path.join(self.work_dir, 'model')
        nart_config = getattr(self.deploy_cfg, 'nart_config', {})

        with pytorch.convert_mode():
            prototxt, caffemodel = pytorch.convert_v2(
                model=self.model,
                filename=caffe_name,
                verbose=True,
                **nart_config
            )

        # caffe.caffemodel -> caffe-convert.caffemodel (merge bn)
        cmd_str = f'python -m spring.nart.tools.caffe.convert --all {caffemodel}'
        print(f'\n{cmd_str}')
        os.system(cmd_str)

        # check caffe model
        prototxt = prototxt.replace('model.', 'model-convert.')
        caffemodel = caffemodel.replace('model.', 'model-convert.')
        cmd_str = f'python -m spring.nart.tools.caffe.count {caffemodel}'
        print(f'\n{cmd_str}')
        os.system(cmd_str)

        print(f'prototxt: {prototxt}\ncaffemodel: {caffemodel}')
        return prototxt, caffemodel

    def torch2onnx(self):
        if hasattr(self.deploy_cfg, 'input_shapes'):
            input_shapes = self.deploy_cfg['input_shapes']
            datas = tuple([torch.randn(input_shape, device=self.device) \
                                for input_shape in input_shapes])
        else:
            raise NotImplementedError('onnx backend config must set input_shapes')
        
        # pytorch model -> onnx export
        onnx_name = os.path.join(self.work_dir, 'model.onnx')
        onnx_config = getattr(self.deploy_cfg, 'onnx_config', {})

        # merge bn
        self.model = fuse_conv_bn(self.model)

        with torch.no_grad():
            torch.onnx.export(
                self.model,
                datas,
                onnx_name,
                verbose=True,
                **onnx_config 
            )
        print(f'onnx convert finished: {onnx_name}')

        # onnx simplify
        simplified_onnx_name = os.path.join(self.work_dir, 'model-simplified.onnx')
        onnx_model = onnx.load(onnx_name)
        simplified_onnx_model, check = simplify(onnx_model, check_n=1)
        if not check:
            print('check failed, please check the difference between original and simplified model')
        onnx.save(simplified_onnx_model, simplified_onnx_name)

        print(f'onnx simplify finished: {simplified_onnx_name}')
    
    def run(self):
        backend = self.deploy_cfg.backend
        if backend == 'onnx':
            self.torch2onnx()
        elif backend == 'caffe':
            self.torch2caffe()
        else:
            raise NotImplementedError(f'unsupported backend: {backend}')
        if self.adela_client is not None:
            self.adela_client.run()


def main():
    args = parse_args()

    if args.overwrite and os.path.isdir(args.output_dir):
        shutil.rmtree(args.output_dir)
    os.makedirs(args.output_dir, exist_ok=True)

    deployer = Deployer(args.deploy_cfg, args.model_cfg, args.checkpoint,
                        args.output_dir, args.device)

    deployer.run()


if __name__ == '__main__':
    main()
