import math

import cv2
import numpy as np
from scipy import interpolate

from pillar.datasets import PIPELINES
from .lkd_lane_cls_formation import CollectLkdClsLane, draw_label, gaussian2D


def draw_umich_gaussian(heatmap,
                        center,
                        radius,
                        k=1,
                        offmap=None,
                        ypos=None,
                        xpos=None,
                        hm_h=72,
                        hm_w=128,
                        hard_code=True):
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)
    x, y = int(center[0]), int(center[1])
    height, width = heatmap.shape[0:2]
    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)
    masked_heatmap = heatmap[y - top:y + bottom, x - left:x + right]
    if offmap is not None:
        offmap[0, y - top:y + bottom, x - left:x +
               right] = center[1] / hm_h - ypos[y - top:y + bottom]
        offmap[1, y - top:y + bottom, x - left:x +
               right] = center[0] / hm_w - xpos[x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom,
                               radius - left:radius + right]
    if hard_code:
        masked_gaussian[masked_gaussian > 0.4] = 1
    if np.size(masked_gaussian) > 0 and np.size(masked_heatmap) > 0:
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def extend_line(line, rows=1):
    start = line[1]
    end = line[0]
    dx = end[0] - start[0]
    extend = []
    for i in range(1, rows + 1):
        extend_point = np.array([[line[0][0] + dx * i, line[0][1] + i]])
        extend.append(extend_point)
    extended = np.concatenate(extend + [line])
    return extended


@PIPELINES.register_module()
class CollectFiledLane(CollectLkdClsLane):

    def __init__(
        self,
        view_distance=5,
        pre_view_distance=0,
        property_down_rate=1,
        keys=[
            'img', 'ignore_laneline', 'ignore_roadside', 'laneline_gt_hm',
            'laneline_start_point_off', 'roadside_gt_hm',
            'roadside_start_point_off', 'laneline_field', 'laneline_attr',
            'roadside_field', 'roadside_attr'
        ],
        meta_keys=[
            'filename', 'img_prefix', 'sub_img_name', 'img_shape', 'ori_shape',
            'roi', 'bk_roi', 'break_points', 'img_norm_cfg', 'hm_shape',
            'mask_shape', 'down_scale', 'hm_down_scale', 'laneline',
            'roadside', 'scene', 'camera_index'
        ],
        **kwargs,
    ):
        super().__init__(keys=keys, meta_keys=meta_keys, **kwargs)
        self.view_distance = view_distance
        self.pre_view_distance = pre_view_distance
        self.property_down_rate = property_down_rate

    def process_mask(
        self,
        mask,
        gt_hm,
        start_point_off,
        mask_w,
        mask_h,
        hm_w,
        hm_h,
        ratio_hm_mask,
        anno_label=255,
        anno_color=255,
    ):
        assert mask is not None
        gt_points, gt_labels, gt_colors, idx2insidx = self.mask2point(
            mask,
            anno_label,
            anno_color,
        )
        valid_gt = []
        valid_gt_points = []
        valid_idx2insidx = []
        for i in range(len(gt_points)):
            gt_point = gt_points[i]
            if 0 < self.filter_pts < len(gt_point):
                gt_point = gt_point[self.filter_pts:]
            pts = gt_point / self.down_scale
            pts = pts[::-1]
            if len(pts) > 1:
                valid_gt.append(pts)
                valid_gt_points.append(gt_point)
                valid_idx2insidx.append(idx2insidx[i])
        gt_points = valid_gt_points

        # draw gt_hm_lane
        gt_hm_lane_ends = []  # [hm end point,points]
        for l in valid_gt:
            if self.endpoint == "down":
                idx = 0
            elif self.endpoint == "top":
                idx = -1
            elif self.endpoint == "far":
                idx = int(len(l) * 0.75)
            elif self.endpoint == "near":
                idx = int(len(l) * 0.25)
            else:
                idx = len(l) // 2
            point = tuple(l[idx] * ratio_hm_mask)
            gt_hm_lane_ends.append([point, l])  # [end point,lane points]

        radius = [self.radius] * len(gt_hm_lane_ends)
        if len(gt_hm_lane_ends) >= 2:
            endpoints = [p[0] for p in gt_hm_lane_ends]
            for j in range(len(endpoints)):
                dis = self.min_dis_one_point(endpoints, j)
                if dis < 1.5 * radius[j]:
                    radius[j] = int(max(dis / 1.5, 1) + 0.49999)

        # regression
        weight = np.zeros((self.view_distance + 1, mask_h, mask_w), np.float32)
        vector_field = np.zeros((self.view_distance, mask_h, mask_w),
                                np.float32)
        pre_weight = np.zeros((self.pre_view_distance, mask_h, mask_w),
                              np.float32)
        pre_vector_field = np.zeros((self.pre_view_distance, mask_h, mask_w),
                                    np.float32)
        offset_field = np.zeros((1, mask_h, mask_w), np.float32)
        length_field = np.zeros((1, mask_h, mask_w), np.float32)
        pre_length_field = np.zeros((1, mask_h, mask_w), np.float32)
        type_field = np.ones(
            (mask_h // self.property_down_rate,
             mask_w // self.property_down_rate), np.long) * 255
        color_field = np.ones(
            (mask_h // self.property_down_rate,
             mask_w // self.property_down_rate), np.long) * 255
        # attr_weight =
        m = np.zeros((mask_h, mask_w), np.uint8)
        x_poss = [None] * mask_h
        start_ends = [None] * mask_h
        x_pos_range = np.arange(0.5, mask_w, 1)
        x_hm_pos = np.arange(0.5, hm_w, 1) / hm_w
        y_hm_pos = np.arange(0.5, hm_h, 1).reshape(hm_h, 1) / hm_h
        res = []
        for insidx, (end_point, line) in enumerate(gt_hm_lane_ends):
            gt_hm[0] = draw_umich_gaussian(gt_hm[0], end_point, radius[insidx],
                                           1, start_point_off, y_hm_pos,
                                           x_hm_pos, hm_h, hm_w)
            y_min = int(np.min(line[:, 1]))
            y_max = int(np.max(line[:, 1]))

            m.fill(0)
            polygon = extend_line(line)
            polygon_map = draw_label(m, polygon, 1, "line", width=3) > 0
            for y in range(polygon_map.shape[0] - 1, -1, -1):
                x_idxs = np.where(polygon_map[y, :])[0]
                start_ends[y] = (x_idxs[0],
                                 x_idxs[-1] + 1) if len(x_idxs) else None
                x_poss[y] = x_idxs.mean() + 0.5 if len(x_idxs) else None
            for y in range(polygon_map.shape[0] - 1, -1, -1):
                ref_x = x_poss[y]
                if ref_x is None:
                    continue
                s_x, e_x = start_ends[y]
                as_x, ae_x = s_x // self.property_down_rate, e_x // self.property_down_rate
                ae_x = max(as_x + 1, ae_x)
                offset_field[0, y, s_x:e_x] = ref_x - x_pos_range[s_x:e_x]
                for j in range(1, self.view_distance + 1):
                    if y - j > 0 and x_poss[y - j] is not None:
                        next_x_ref = x_poss[y - j]
                        vector_field[
                            j - 1, y,
                            s_x:e_x] = next_x_ref - x_pos_range[s_x:e_x]
                        weight[j, y, s_x:e_x] = 1 + (mask_h - y) / mask_h * 2
                length_field[0, y, s_x:e_x] = max(y - y_min + 1, 0)
                pre_length_field[0, y, s_x:e_x] = max(y_max - y + 1, 0)
                type_field[y // self.property_down_rate,
                           as_x:ae_x] = gt_labels[insidx]
                color_field[
                    y // self.property_down_rate, as_x:
                    ae_x] = -1 if anno_color is None else gt_colors[insidx]
                weight[0, y, s_x:e_x] = 1  # np.exp(- dis * dis / 3)

            info = dict(
                y_max=y_max,
                y_min=y_min,
                type=gt_labels[insidx],
                color=gt_colors[insidx],
            )
            res.append(info)
        field = [
            offset_field, vector_field[0:1], length_field, weight,
            vector_field[1:]
        ]
        if self.pre_view_distance > 0:
            field.extend([pre_vector_field, pre_weight, pre_length_field])
        field = np.concatenate(field)
        attrs = np.stack([type_field, color_field])
        return res, field, attrs, gt_points, valid_idx2insidx

    def target(self, results):
        output_h = int(results["img_shape"][0])
        output_w = int(results["img_shape"][1])
        mask_h = int(output_h // self.down_scale)  # 8x
        mask_w = int(output_w // self.down_scale)
        hm_h = int(output_h // self.hm_down_scale)  # 16x
        hm_w = int(output_w // self.hm_down_scale)
        ratio_hm_mask = self.down_scale / self.hm_down_scale

        results["hm_shape"] = [hm_h, hm_w]
        results["mask_shape"] = [mask_h, mask_w]
        results["down_scale"] = self.down_scale
        results["hm_down_scale"] = self.hm_down_scale

        def resize(im):
            if type(im) is np.ndarray:
                return cv2.resize(
                    im,
                    (mask_w // self.property_down_rate,
                     mask_h // self.property_down_rate),
                    interpolation=cv2.INTER_NEAREST,
                )
            else:
                return np.ones(
                    (mask_h // self.property_down_rate,
                     mask_w // self.property_down_rate), np.uint8) * im

        field_dims = 3 + 2 * self.view_distance
        if self.pre_view_distance > 0:
            field_dims = field_dims + 2 * self.pre_view_distance + 1

        mask = results["laneline_mask"]
        label = results["laneline_label"]
        color = results["laneline_color"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, valid_idx2insidx = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label, color)

            self.kps2mask(mask, results.get("bks", []), gt_masks,
                          valid_idx2insidx, mask_h)

            lane = dict(key_points=gt_points, gt_masks=gt_masks)
            results["laneline_field"] = field
            results["laneline_attr"] = attribute_target
            results["laneline"] = lane
            results["ignore_laneline"] = False
        else:
            results["laneline_field"] = np.zeros((field_dims, mask_h, mask_w),
                                                 np.float32)
            results["laneline_attr"] = np.stack([
                resize(label),
                resize(color),
            ]).astype(np.long)
            results["laneline"] = dict(key_points=[], gt_masks=[])
            results["ignore_laneline"] = True
        results["laneline_start_point_off"] = start_point_off
        results["laneline_gt_hm"] = gt_hm

        mask = results["roadside_mask"]
        label = results["roadside_label"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, _ = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label)

            roadside = dict(key_points=gt_points, gt_masks=gt_masks)
            results["roadside_field"] = field
            results["roadside_attr"] = attribute_target[:1]
            results["roadside"] = roadside
            results["ignore_roadside"] = False
        else:
            results["roadside_field"] = np.zeros((field_dims, mask_h, mask_w),
                                                 np.float32)
            results["roadside_attr"] = resize(label)[None].astype(np.long)
            results["roadside"] = dict(key_points=[], gt_masks=[])
            results["ignore_roadside"] = True
        results["roadside_start_point_off"] = start_point_off
        results["roadside_gt_hm"] = gt_hm

        return True
