import json
import numpy as np

def are_keypoints_similar(kp1, kp2, tolerance=1e-3):
    """
    Check if two keypoints arrays are similar within a certain numerical tolerance.
    
    Parameters:
    - kp1: List of key points (x, y) for the first lane line.
    - kp2: List of key points (x, y) for the second lane line.
    - tolerance: Maximum allowed difference between corresponding points to consider them similar.

    Returns:
    - True if the keypoints arrays are similar, False otherwise.
    """
    # Convert to numpy arrays for easier comparison
    kp1 = np.array(kp1)
    kp2 = np.array(kp2)
    
    # Ensure both keypoint arrays are of the same length
    if kp1.shape != kp2.shape:
        return False

    # Calculate the difference between the two keypoints arrays
    difference = np.linalg.norm(kp1 - kp2, axis=1)
    
    # Check if all differences are within the specified tolerance
    return np.all(difference <= tolerance)

def identify_and_remove_duplicate_lanes(cpp_results):
    """
    Identify and remove duplicate lanes (similar keypoints arrays) for each image.

    Parameters:
    - cpp_results: Dictionary containing C++ results with image names as keys.
    
    Returns:
    - A dictionary with duplicates removed.
    """
    cleaned_results = {}

    for image_name, lanes in cpp_results.items():
        if not lanes or not isinstance(lanes, list):
            cleaned_results[image_name] = lanes  # Keep empty or malformed entries as they are
            continue

        unique_lanes = []
        duplicates_found = False

        # Compare each lane with every other lane in the same image
        for i, lane in enumerate(lanes):
            if 'key_points' not in lane:
                unique_lanes.append(lane)
                continue
            
            is_duplicate = False
            for existing_lane in unique_lanes:
                if 'key_points' in existing_lane and are_keypoints_similar(lane['key_points'], existing_lane['key_points']):
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_lanes.append(lane)
            else:
                if not duplicates_found:
                    print(f"Duplicate lanes found in image: {image_name}")
                    duplicates_found = True
                print(f"  - Removed duplicate lane: Lane {i + 1}")

        # Store the unique lanes for the current image
        cleaned_results[image_name] = unique_lanes

    return cleaned_results

# Load combined C++ JSON results with image names
with open("combined_cpp_results_with_images.json", 'r') as cpp_file:
    cpp_results = json.load(cpp_file)

# Identify and remove duplicated lanes
cleaned_cpp_results = identify_and_remove_duplicate_lanes(cpp_results)

# Save the cleaned results into a new JSON file
with open('cleaned_cpp_results_with_images.json', 'w') as outfile:
    json.dump(cleaned_cpp_results, outfile, indent=4)

#print("Cleaned C++ results saved to 'cleaned_cpp_results_with_images.json'.")