import random
from collections import OrderedDict

import numpy as np
import torch
import torch.nn as nn
import torch.distributed as dist

from pillar.models import BaseModel, MODELS


def sigmoid(z):
    return np.where(z >= 0, 1 / (1 + np.exp(-z)), np.exp(z) / (1 + np.exp(z)))


@MODELS.register_module()
class CondLaneNet(BaseModel):

    def __init__(
        self,
        backbone,
        neck=None,
        decoder=None,
        spatial_head=None,
        head=None,
        train_cfg=None,
        test_cfg=None,
        init_cfg=None,
        pretrained=None,
        pretrained_list=["backbone", "neck", "head"],
        output_scale=4,
        num_classes=2,
        ignore_index=255,
        finetune_break_point_head=False,
        finetune_aux_head=False,
        loss_condlane=dict(type="condlane.CondLaneLoss", weights={}),
        post_processor={},
    ):
        super(CondLaneNet, self).__init__(backbone, neck, decoder, spatial_head, head, train_cfg, test_cfg, init_cfg)
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.finetune_break_point_head = finetune_break_point_head
        self.finetune_aux_head = finetune_aux_head
        if test_cfg is not None and "out_scale" in test_cfg.keys():
            self.output_scale = test_cfg["out_scale"]
        else:
            self.output_scale = 4

        self.pretrained = pretrained
        self.load_pretrain(
            pretrained=pretrained,
            pretrained_list=pretrained_list,
        )
        self.freeze()

        post_processor_type = post_processor.pop("type", "")
        if "beta" in post_processor_type:
            from ...core.post_processing import CondLanePostProcessorBeta as PostProcess
        else:
            from ...core.post_processing import CondLanePostProcessor as PostProcess
        self.post_processor = PostProcess(**post_processor)

        self.losses = MODELS.build(loss_condlane)

    def init_weights(self):
        if self.pretrained:
            return
        self.backbone.init_weights()
        if isinstance(self.neck, nn.Sequential):
            for m in self.neck:
                m.init_weights()
        else:
            self.neck.init_weights()
        self.head.init_weights()
        if hasattr(self, "roadsides_head"):
            self.roadsides_head.init_weights()

    def load_pretrain(self, pretrained=None, pretrained_list=[]):
        backbone_pret = pretrained if "backbone" in pretrained_list else None
        neck_pret = pretrained if "neck" in pretrained_list else None
        head_pret = pretrained if "head" in pretrained_list else None

        self.backbone.init_weights(pretrained=backbone_pret)
        if isinstance(self.neck, nn.Sequential):
            for m in self.neck:
                m.init_weights()
        else:
            self.neck.init_weights(pretrained=neck_pret)
        self.head.init_weights(pretrained=head_pret)
        if hasattr(self, "roadsides_head"):
            self.roadsides_head.init_weights(pretrained=head_pret)

    def freeze(self):
        if self.finetune_break_point_head:
            for name, p in self.named_parameters():
                if "break" not in name:
                    p.requires_grad = False
            self.eval()
            self.head.break_point_head.train()
        if self.finetune_aux_head:
            self.ft_modules = [
                self.head.property_mask_head,
                self.head.property_fc,
                self.head.cls_mpl,
            ]
            for name, p in self.named_parameters():
                p.requires_grad = False
            for ft_module in self.ft_modules:
                for name, p in ft_module.named_parameters():
                    p.requires_grad = True

    def parse_gt(self, gts, device):
        reg = (torch.from_numpy(gts["reg"]).to(device)).unsqueeze(0)
        reg_mask = (torch.from_numpy(gts["reg_mask"]).to(device)).unsqueeze(0)
        row = (torch.from_numpy(
            gts["row"]).to(device)).unsqueeze(0).unsqueeze(0)
        row_mask = (torch.from_numpy(
            gts["row_mask"]).to(device)).unsqueeze(0).unsqueeze(0)
        if "range" in gts:
            lane_range = (torch.from_numpy(gts["range"]).to(device))
        else:
            lane_range = torch.zeros((1, reg.shape[-2]),
                                     dtype=torch.int64).to(device)
        break_target = None
        if "break_target" in gts:
            break_target = torch.from_numpy(
                gts["break_target"]).to(device).type(torch.float32)
            break_target_weight = torch.from_numpy(
                gts["break_target_weight"]).to(device).type(torch.float32)
        else:
            break_target = torch.zeros((reg.shape[-2]),
                                       dtype=torch.float32).to(device)
            break_target_weight = torch.zeros((reg.shape[-2]),
                                              dtype=torch.float32).to(device)
        y_max = gts["y_max"]
        y_min = gts["y_min"]
        gt_type = gts["type"]
        gt_color = None
        if "color" in gts:
            gt_color = gts["color"]
        return reg, reg_mask, row, row_mask, lane_range, gt_type, gt_color, y_min, y_max, break_target, break_target_weight

    def parse_pos(self,
                  gt_all_masks,
                  hm_shape,
                  device,
                  mask_shape=None,
                  ignore_roadside=None):
        b = len(gt_all_masks[0])
        n = self.num_classes
        hm_h, hm_w = hm_shape[:2]
        if mask_shape is None:
            mask_h, mask_w = hm_shape[:2]
        else:
            mask_h, mask_w = mask_shape[:2]
        poses = []
        regs = []
        reg_masks = []
        rows = []
        row_masks = []
        lane_ranges = []
        labels = []
        num_ins = []
        gt_types = []
        gt_colors = []
        y_maxs, y_mins = [], []
        break_point_targets = []
        break_target_weights = []
        ignore_roadside = ignore_roadside.cpu().numpy().tolist()
        for label, gt_masks in enumerate(gt_all_masks):
            for idx, m_img in enumerate(gt_masks):
                num = 0
                for m in m_img:
                    gts = self.parse_gt(m, device=device)
                    reg, reg_mask, row, row_mask, lane_range, gt_type, gt_color, y_min, y_max, break_target, break_target_weight = gts
                    num += len(m["points"])
                    for p in m["points"]:
                        pos = [idx, label, p[1], p[0]]
                        poses.append(pos)
                    for i in range(len(m["points"])):
                        labels.append(label)
                        regs.append(reg)
                        reg_masks.append(reg_mask)
                        rows.append(row)
                        row_masks.append(row_mask)
                        lane_ranges.append(lane_range)
                        gt_types.append(gt_type)
                        y_maxs.append(y_max)
                        y_mins.append(y_min)
                        break_point_targets.append(break_target)
                        break_target_weights.append(break_target_weight)
                        if label == 0:
                            assert gt_color is not None
                            gt_colors.append(gt_color)
                if num == 0 and (label == 0 or not ignore_roadside[idx]):
                    # 有些图片没有线，按原来的代码逻辑，会增加一条长度为0的线，分类时，原想增加一个背景类，后来发现roadside会出现大量背景误报，因此在新的实验中忽略这个增加的实例
                    reg = torch.zeros((1, 1, mask_h, mask_w)).to(device)
                    reg_mask = torch.zeros((1, 1, mask_h, mask_w)).to(device)
                    row = torch.zeros((1, 1, mask_h)).to(device)
                    row_mask = torch.zeros((1, 1, mask_h)).to(device)
                    lane_range = torch.zeros((1, mask_h),
                                             dtype=torch.int64).to(device)
                    break_target = torch.zeros((mask_h),
                                               dtype=torch.float32).to(device)
                    break_target_weight = torch.zeros(
                        (mask_h), dtype=torch.float32).to(device)
                    # pos = idx * n * hm_h * hm_w + random.randint(
                    #     0, n * hm_h * hm_w - 1)
                    pos = [
                        idx, label,
                        random.randint(0, hm_h - 1),
                        random.randint(0, hm_w - 1)
                    ]
                    # assert 0<=pos[2]<hm_h and 0<=pos[3]<hm_w
                    # assert label ==0 or label==1
                    # assert idx< b
                    num = 1
                    labels.append(label)
                    poses.append(pos)
                    regs.append(reg)
                    reg_masks.append(reg_mask)
                    rows.append(row)
                    row_masks.append(row_mask)
                    lane_ranges.append(lane_range)
                    y_maxs.append(mask_h - 1)
                    y_mins.append(0)
                    break_point_targets.append(break_target)
                    break_target_weights.append(break_target_weight)
                    # from IPython import embed
                    # embed()
                    gt_types.append(self.ignore_index)
                    if label == 0:
                        gt_colors.append(self.ignore_index)

                num_ins.append(num)

        if len(regs) > 0:
            regs = torch.cat(regs, 1)
            reg_masks = torch.cat(reg_masks, 1)
            rows = torch.cat(rows, 1)
            row_masks = torch.cat(row_masks, 1)
            lane_ranges = torch.cat(lane_ranges, 0)
            break_point_targets = torch.stack(break_point_targets, 0)
            break_target_weights = torch.stack(break_target_weights, 0)
            gt_types = torch.tensor(gt_types, dtype=torch.long).to(device)
            if gt_colors is not None:
                gt_colors = torch.tensor(gt_colors,
                                         dtype=torch.long).to(device)
            y_maxs = torch.tensor(y_maxs, dtype=torch.long).to(device)
            y_mins = torch.tensor(y_mins, dtype=torch.long).to(device)

        gts = dict(gt_reg=regs,
                   gt_reg_mask=reg_masks,
                   gt_rows=rows,
                   gt_row_masks=row_masks,
                   gt_ranges=lane_ranges,
                   gt_type=gt_types,
                   gt_color=gt_colors,
                   break_point_targets=break_point_targets,
                   break_target_weights=break_target_weights,
                   y_mins=y_mins,
                   y_maxs=y_maxs)

        return poses, labels, num_ins, gts

    def forward_train(self, img, img_metas, **kwargs):
        output, _ = self.extract_feat(img)

        gt_batch_masks = [[m[t]["gt_masks"] for m in img_metas]
                          for t in ["laneline", "roadside"]]
        hm_shape = img_metas[0]["hm_shape"]
        mask_shape = img_metas[0]["mask_shape"]
        poses, labels, num_ins, gts = self.parse_pos(
            gt_batch_masks,
            hm_shape,
            img.device,
            mask_shape=mask_shape,
            ignore_roadside=kwargs["ignore_roadside"],
        )
        pos_tensor = torch.from_numpy(np.array(poses)).to(img.device)
        output = self.head.forward_train(output, pos_tensor, num_ins)
        kwargs.update(
            **gts,
            labels=labels,
            pos_tensor=pos_tensor,
        )
        losses = self.losses(output, img_metas, **kwargs)
        return losses

    def forward_tokestrel(self, *args, **kwargs):
        mode = self.tokestrel
        assert mode in ["e2e", "proposal", "detect", "net"]
        if mode == "detect":
            lane = self.head.conditional_shape_head(**args[0])
            return lane
        elif mode in ["proposal", "net"]:
            output, _ = self.extract_feat(args[0]["image"])
            lane = self.head.proposal_head(output)
            return lane
        else:
            output, _ = self.extract_feat(kwargs["image"])
            lane = self.head.forward_test(output)
            return lane

    def simple_test(self, imgs, img_metas, **kwargs):
        #print("\n===============INSIDE SIMPLE_TEST============\n")
        output, _ = self.extract_feat(imgs)
        output = self.head.forward_test(output)
        return self.post_process(output, img_metas)

    def post_process(self, output, img_metas):
        for k in output.keys():
            if output[k] is not None:
                output[k] = output[k].detach().cpu().numpy()
        proposal_indices = output["proposal_indices"]
        if proposal_indices is not None:
            if "cls_score" in output:
                scores = output["cls_score"]
            else:
                pn = proposal_indices[:, 0]
                pc = proposal_indices[:, 1]
                ph = proposal_indices[:, 2]
                pw = proposal_indices[:, 3]
                scores = output["proposal_heatmap"][pn, pc, ph, pw]
        else:
            scores = None
        preds = self.post_processor(proposal_indices, scores, output)

        ret = []
        for i, meta in enumerate(img_metas):
            sub_img_name = meta["sub_img_name"]
            img_prefix = meta["img_prefix"]
            shape = meta["img_shape"]
            scale = meta["down_scale"]
            roi = meta["roi"]
            bk_roi = meta["bk_roi"]
            scene = meta["scene"]
            camera_index = meta["camera_index"]
            m = meta["pers_trans_m"]

            ret.append(dict(gt=[], pred=[]))
            for j, label in enumerate(["laneline", "roadside"]):
                gts = img_metas[i].get(label, {})
                if label == "laneline" and len(img_metas[i].get("break_points", [])) > 0:
                    gts["break_points"] = img_metas[i].get("break_points", [])
                gts = self.post_processor.fmt_gt(gts, roi, bk_roi, shape[1],
                                                 shape[0], label, scene,
                                                 camera_index)
                for _ in gts:
                    _["image_path"] = sub_img_name
                    _["img_prefix"] = img_prefix
                ret[-1]["gt"].extend(gts)

                dts = [
                    _ for _ in preds
                    if _["image_id"] == i and _["label_id"] == j
                ]
                dts = self.post_processor.fmt_pred(dts, roi, bk_roi, shape[1],
                                                   shape[0], label, scene,
                                                   camera_index, scale)
                if m != 0:
                    for dt in dts:
                        dt['key_points']['points'] = back_trans_pts(dt['key_points']['points'], m[1])
                for _ in dts:
                    _["image_path"] = sub_img_name
                    _["img_prefix"] = img_prefix
                ret[-1]["pred"].extend(dts)
        return ret

def back_trans_pts(pts, m):
    pts = np.array([[pt['x'] for pt in pts], [pt['y'] for pt in pts]])

    dst = np.zeros([pts.shape[1], 2])
    _ = np.zeros([pts.shape[1], 2])
    _[:, 0] = pts[0]
    _[:, 1] = pts[1]
    for i in range(dst.shape[0]):
        dst[i, 0] = (m[0, 0] * _[i, 0] + m[0, 1] * _[i, 1] + m[0, 2]) / (m[2, 0] * _[i, 0] + m[2, 1] * _[i, 1] + m[2, 2])
        dst[i, 1] = (m[1, 0] * _[i, 0] + m[1, 1] * _[i, 1] + m[1, 2]) / (m[2, 0] * _[i, 0] + m[2, 1] * _[i, 1] + m[2, 2])
    dst_ = [dict(x=pt[0], y=pt[1]) for pt in dst]

    return dst_
