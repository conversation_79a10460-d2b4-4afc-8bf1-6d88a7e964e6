import torch.nn as nn
import torch.nn.functional as F

from pillar.models import MODELS


class SpatialContextualBranch(nn.Module):
    def __init__(self, in_channels, out_channels=1):
        super(SpatialContextualBranch, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, 64, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(64, 32, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(32, out_channels, kernel_size=3, padding=1)  # output: δz map
        
    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        depth_pred = self.conv3(x)
        #delta_z = self.conv3(x)  # (B, 1, H', W')
        return depth_pred


@MODELS.register_module()
class DepthRegressionModel(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(DepthRegressionModel, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
       # self.mit_b2 = mit_b2()
       # self.segformer_head = SegFormerHead(
       #             in_channels=[64, 128, 320, 512],
       #             in_index=[0, 1, 2, 3],
       #             feature_strides=[4, 8, 16, 32],
       #             channels=128,
       #             dropout_ratio=0.1,
       #             num_classes=150,
       #             #norm_cfg=norm_cfg,
       #             align_corners=False,
       #             decoder_params=dict(embed_dim=768),
       #             loss_decode=dict(type='CrossEntropyLoss', use_sigmoid=False, loss_weight=1.0)
       # )
        self.spatial_branch = SpatialContextualBranch(in_channels=self.in_channels, out_channels=self.out_channels)
    
    def forward(self, x):
       # encoder_feats = self.mit_b2(x)
       # decoder_feats = self.segformer_head(x)
        depth_pred = self.spatial_branch(x)
        
        print(f"Shape of depth_pred: {depth_pred.shape}")
            
        return depth_pred
        
    def forward_train(self, x):
        return self.forward(x)
