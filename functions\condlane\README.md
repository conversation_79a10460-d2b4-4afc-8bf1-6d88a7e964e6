# CondLane
## Overview
### Background

Lane and roadside detector, based on `mmdet`.

### Network

TODO

## Evaluation
### Metrics

TODO

### Results

TODO

## Data
### Data Revision

TODO

### Data Path
#### Images

real image path: `functions/condlane/data/lustre`

#### Annotations

annotation path: `functions/condlane/data/seg`

### Data Format

For infer phase, only `filename` and `camera_index` are required.

Example:
```json
{
    "filename": "your/image/path/00000011.jpg",
    "laneline": "your/image/laneline/label/00000011.png",
    "laneline_type": "your/image/lanetype/label/path/00000011.png",
    "laneline_color": "your/image/lanecolor/label/00000011.png",
    "roadside": "your/image/roadside/label/00000011.png",
    "sidetype": "your/image/sidetype/label/00000011.png",
    "scene": [
        "city"
    ],
    "camera_index": "front_fov30"
}
```

### Label Instructions

There are 2 labels, `laneline` and `roadside`.

A `laneline` has 2 groups of property, `type` and `color`, like following.

| index | type                    | index | color            |
| :---: | :---------------------: | :---: | :--------------: |
|   0   | NO_LANE                 |   0   | NO_LANE          |
|   1   | OTHER_LANE_TYPE         |   1   | OTHER_LANE_COLOR | 
|   2   | SOLID_LANE              |   2   | WHITE            |
|   3   | DASHED_LANE             |   3   | YELLOW           |
|   4   | SHORT_THICK_DASHED_LANE |   4   | ORANGE           |
|   5   | SPLIT_LANE              |   5   | BLUE             |
|   6   | MERGE_LANE              |
|   7   | LEFT_DASHED_RIGHT_SOLID |
|   8   | LEFT_SOLID_RIGHT_DASHED |
|   9   | DOUBLE_SOLID            |
|   10  | DOUBLE_DASHED           |
|   11  | FISHBONE_SOLID          | 
|   12  | FISHBONE_DASHED         |

A `roadside` has only 1 group of property `type` like following.

| index | type                    |
| :---: | :---------------------: |
|   0   | NO_SIDE                 |
|   1   | ROAD_CURB_EDGE          |
|   2   | FENCE_EDGE              |
|   3   | WALL_EDGE_OR_PLANT_EDGE |
|   4   | MOVABLE_EDGE            |
|   5   | OTHER_EDGE              |

## Reproduction procedure

### Dependencies

For deployment, see for requirements/deploy.txt

* onnx==1.11.0
* onnxruntime==1.10.0
* onnxoptimizer==0.2.5
* onnx-simplifier==0.3.7
* protobuf==3.12.2

### Train

GPUS=8 PARTITION=Test PHASE=train bash tools/run.sh functions/condlane/configs/config_r331_one_stage_11.6MB.py

### Test

``` bash
GPUS=8 PARTITION=Test PHASE=test \
bash tools/run.sh \
    functions/condlane/configs/config_r331_one_stage_11.6MB.py \
    functions/condlane/checkpoint/condlane_r331_one_stage_11.6MB.pth \
    all [front_fov30 front_fov120]
```

### Infer

``` bash
GPUS=8 PARTITION=Test PHASE=infer \
bash tools/run.sh \
    functions/condlane/configs/config_r331_one_stage_11.6MB.py \
    functions/condlane/checkpoint/condlane_r331_one_stage_11.6MB.pth \
    ./lane_data/eval.json \
    ""
```

### Deploy

functions/condlane/deploy/readme.md
