from mmcv import Config
from mme.datasets import build_dataloader
from pillar.datasets import build_dataset
from mmcv.runner import load_checkpoint
from mme.models import build_model

cfg = Config.fromfile('/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/functions/condlane/configs/config_r331_one_stage_11.6MB.py')

testing_dataset = build_dataset(cfg.data.test)

testing_dataloader = build_dataloader(
    testing_dataset,
    samples_per_gpu=1,   # This is equivalent to batch_size=1
    workers_per_gpu=1,   # Number of data loading workers
    dist=False,          # Set to True if in distributed environment
    shuffle=False
)

CKPT_PTH = '/work/home/<USER>/niloofar/new/condlane_checkpoints/ftuned_model_batch1_exp4.pth'
model = build_model(cfg.model)
load_checkpoint(model, CKPT_PTH, map_location='cuda')

print(type(model))

