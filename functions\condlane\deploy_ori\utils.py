import os
import json
from argparse import ArgumentParser

import cv2
import numpy as np
import torch
from torch.nn import functional as F
import mme
from mmcv import Config

from pillar.apis import init_model
from .process import pre_process, parse_roi


def parse_args():
    parser = ArgumentParser(usage="""
    for dataset:
    python -m deploy todataset config.py /path/to/ckpt

    for deployment:
    python -m deploy tokestrel config.py /path/to/ckpt
    """)
    parser.add_argument(
        "cmd",
        default="tokestrel",
        choices=["tokestrel", "todataset", "vis_video"],
    )
    parser.add_argument("config", help="Config file")
    parser.add_argument("checkpoint", default=None, help="Checkpoint file")
    parser.add_argument(
        "-c",
        "--client_conf",
        default="/mnt/lustre/share/liuhao1/petreloss/petreloss_lane.conf",
        help="config for petrel client",
    )
    parser.add_argument("-r", "--random", type=bool, default=False)
    parser.add_argument(
        "--condconv",
        default="matmul",
        choices=["matmul", "muladd", "muladdsum"],
    )
    parser.add_argument("--crop", action="store_true")
    parser.add_argument("--maxpool", type=bool, default=True)
    parser.add_argument("--u8", action="store_true")
    parser.add_argument("--nhwc", action="store_true")
    parser.add_argument("-v", "--version", type=int, default=2, choices=[1, 2])
    parser.add_argument("--dup_i", type=int, default=1)
    parser.add_argument("-o", "--outdir", default="tokestrel")
    parser.add_argument("--video", default="")
    parser.add_argument("--camera", 
                        choices=['front_fov120', 'front_fov30', 'rear_fov60', 'rear_fov100'], 
                        default="front_fov120")
    args = parser.parse_args()
    if not os.path.exists(args.checkpoint):
        args.checkpoint = None
    return args


def get_data(config, client, crop=False, random=False, camera="front_fov120"):
    data_test = config.data.test
    img_prefix = data_test.get("img_prefix", data_test.get("dir_prefix", ""))
    ann_file = data_test.get("ann_file", data_test.get("data_root", ""))
    lines = open(ann_file).readlines()
    if random:
        np.random.shuffle(lines)
    batch_size = config.get("tokestrel", {}).get("batch_size", {}).get("image", 1)

    def abspath(file):
        if "s3://" in file or file.startswith("/"):
            return file
        if len(img_prefix) <= 0 or file.startswith(img_prefix):
            return file
        return os.path.join(img_prefix, file)

    def relpath(file):
        if "s3://" in file:
            file = file.split("s3://", 1)[-1]
            file = file.split("/", 1)[-1]
        else:
            if len(img_prefix) <= 0:
                file = os.path.relpath(file, "/")
            else:
                file = os.path.relpath(file, img_prefix)
        return file

    data = []
    others = []
    for line in lines:
        line = json.loads(line)
        if line.get('camera_index', 'front_fov120') != camera: continue
        fullpath = abspath(line["filename"])
        subpath = relpath(fullpath)
        if "s3://" in fullpath:
            img_bytes = client.get(fullpath)
            if img_bytes is None:
                raise FileNotFoundError(fullpath)
            img_array = np.frombuffer(img_bytes, np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        else:
            img = fullpath
        image, info = pre_process(
            img,
            config.camera_roi.get(camera, {}),
            config.get("break_point_range", (0, 1)),
            config.img_scale,
            config.img_norm_cfg.mean,
            config.img_norm_cfg.std,
            not crop,
        )
        data.append(image)
        others.append(info + [subpath])
        print("load", fullpath)
        if len(data) == batch_size: break
    assert len(data), "No target camera in current data!"
    data = np.stack(data)
    return data, others


class WrapperModel(torch.nn.Module):

    def __init__(self, args, config):
        super().__init__()
        self.model_cfg = config
        self.checkpoint = args.checkpoint
        self.device = "cuda"
        self.set_model()
        self.roi = config.camera_roi.get(args.camera, {})
        self.with_crop = args.crop
        self.with_maxpool = args.maxpool
        self.dup_i = args.dup_i

    def set_model(self):
        mme.utils.DEFAULT_SCOPE = self.model_cfg.get('default_scope', None)

        # TODO: replace MMSyncBN with BN
        # import modules from string list.
        if self.model_cfg.get('custom_imports', None):
            from mmcv.utils import import_modules_from_strings
            import_modules_from_strings(**self.model_cfg['custom_imports'])
        # set cudnn_benchmark
        if self.model_cfg.get('cudnn_benchmark', False):
            torch.backends.cudnn.benchmark = True

        # build the model and load checkpoint
        self.model = init_model(self.model_cfg,
                                self.checkpoint,
                                device=self.device)

    def crop(self, image):
        if not self.with_crop:
            return image
        weight = torch.eye(3).view(3, 3, 1, 1).cuda()
        image = F.conv2d(image, weight)
        H, W = image.shape[-2:]
        l, t, r, b = parse_roi(W, H, **self.roi)
        if 0 < t or b < H:
            image = image[..., t:b, :]
        if 0 < l or r < W:
            image = image[..., :, l:r]
        return image

    def cat_dup_i(self, data_dict):
        if self.dup_i <= 1:
            return data_dict["image"]
        images = [data_dict["image"]]
        for i in range(1, self.dup_i):
            images.append(data_dict["image" + str(i + 1)])
        images = torch.cat(images)
        return images

    def forward(self, *args, **kwargs):
        if self.tokestrel == "e2e":
            image = self.cat_dup_i(kwargs)
            cropped = self.crop(image)
            kwargs["image"] = cropped
        if self.tokestrel in ["proposal", "net"]:
            image = self.cat_dup_i(args[0])
            cropped = self.crop(image)
            args[0]["image"] = cropped
        outputs = self.model.forward_tokestrel(*args, **kwargs)
        if self.tokestrel in ["e2e", "proposal", "net"] and self.with_maxpool:
            if isinstance(outputs, dict):
                proposal = outputs
            else:
                proposal = outputs[0]
            if "proposal_heatmap" in outputs and "maxpool" not in proposal:
                proposal["maxpool"] = F.max_pool2d(
                    proposal["proposal_heatmap"],
                    (3, 3),
                    stride=1,
                    padding=1,
                )
            if "line_mask" in proposal and "mask_maxpool" not in proposal:
                proposal["mask_maxpool"] = F.max_pool2d(
                    proposal["line_mask"],
                    (3, 3),
                    stride=1,
                    padding=1,
                )
        return outputs


def get_model(args):
    config = Config.fromfile(args.config)
    model = WrapperModel(args, config)
    model.eval()
    return model, config


def model_infer(model, batch, dup_i, *args, **kwargs):
    proposal_i = {"image": torch.from_numpy(batch).cuda()}
    for i in range(1, dup_i):
        proposal_i["image" + str(i + 1)] = proposal_i["image"]
    proposal_o = model(*args, **kwargs, **proposal_i)
    outputs = {
        "proposal": {
            "input": proposal_i,
            "output": proposal_o,
        }
    }
    if not isinstance(proposal_o, dict):
        proposal_o, detect_i, detect_o = proposal_o
        outputs["proposal"]["output"] = proposal_o
        outputs["detect"] = {
            "input": detect_i,
            "output": detect_o,
        }
    else:
        outputs = {"net": outputs["proposal"]}
    return outputs
