import math

import cv2
import numpy as np
from scipy import interpolate

from pillar.datasets import PIPELINES
from .lkd_lane_cls_formation import CollectLkdClsLane, draw_label, gaussian2D


def draw_umich_gaussian(heatmap,
                        center,
                        radius,
                        k=1,
                        offmap=None,
                        ypos=None,
                        xpos=None,
                        hm_h=72,
                        hm_w=128,
                        hard_code=True):
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)
    x, y = int(center[0]), int(center[1])
    height, width = heatmap.shape[0:2]
    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)
    masked_heatmap = heatmap[y - top:y + bottom, x - left:x + right]
    if offmap is not None:
        offmap[1, y - top:y + bottom, x - left:x +
               right] = center[1] / hm_h - ypos[y - top:y + bottom]
        offmap[0, y - top:y + bottom, x - left:x +
               right] = center[0] / hm_w - xpos[x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom,
                               radius - left:radius + right]
    if hard_code:
        masked_gaussian[masked_gaussian > 0.05] = 1
    if np.size(masked_gaussian) > 0 and np.size(masked_heatmap) > 0:
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def extend_line(line, rows=1):
    start = line[1]
    end = line[0]
    dx = end[0] - start[0]
    extend = []
    for i in range(1, rows + 1):
        extend_point = np.array([[line[0][0] + dx * i, line[0][1] + i]])
        extend.append(extend_point)
    extended = np.concatenate(extend + [line])
    return extended


@PIPELINES.register_module()
class CollectBilateralFiledLane(CollectLkdClsLane):

    def __init__(
        self,
        view_distance=5,
        inv_view_distance=0,
        property_down_rate = 1,
        num_ref_point = 1,
        num_drop_remote_point = 0,
        num_start_rows = 0,
        bg_weight = 0,
        draw_r = 3,
        draw_r_max_ratio=-1,
        soft_weight_len_alpha=2,
        soft_weight_off_alpha=2,
        soft_weight_x=True,
        soft_weight_y=True,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.view_distance = view_distance
        self.inv_view_distance = inv_view_distance
        self.property_down_rate = property_down_rate
        self.num_ref_point = num_ref_point
        self.num_drop_remote_point = num_drop_remote_point
        self.num_start_rows = num_start_rows
        self.bg_weight = bg_weight
        self.draw_r = draw_r
        self.draw_r_max_ratio = draw_r_max_ratio
        self.soft_weight_len_alpha = soft_weight_len_alpha
        self.soft_weight_off_alpha = soft_weight_off_alpha
        self.soft_weight_x = soft_weight_x
        self.soft_weight_y = soft_weight_y
        # some default keys, fix it in code to simple the config
        self.meta_keys = [
            'filename', 'img_prefix', 'sub_img_name', 'img_shape', 'ori_shape',
            'roi', 'bk_roi', 'break_points', 'img_norm_cfg', 'hm_shape',
            'mask_shape', 'down_scale', 'hm_down_scale', 'laneline',
            'roadside', 'scene', 'camera_index', 'pers_trans_m'
        ]
        self.keys = [
            'img', 'ignore_laneline', 'ignore_roadside', 'laneline_gt_hm',
            'laneline_start_point_off', 'depth_map', 'roadside_gt_hm',
            'roadside_start_point_off', 'laneline_field', 'laneline_attr',
            'roadside_field', 'roadside_attr'
        ]
        self.zero_field = None
    def get_zero_field(self, mask_h, mask_w):
        if self.zero_field is None:
            self.zero_field = dict(
                offset = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32), 
                field = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),
                view = np.zeros((self.num_ref_point * (self.view_distance - 1), \
                    mask_h, mask_w), np.float32),

                offset_weight = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),
                field_weight = np.zeros((self.num_ref_point, mask_h, mask_w), np.float32),
                view_weight = np.zeros((self.num_ref_point * (self.view_distance - 1), \
                    mask_h, mask_w), np.float32),

                inv_vector_field = np.zeros((self.num_ref_point * (self.inv_view_distance), \
                    mask_h, mask_w), np.float32),
                inv_field_weight = np.zeros((self.num_ref_point * (self.inv_view_distance), \
                    mask_h, mask_w), np.float32),
                inv_length = np.zeros((1, mask_h, mask_w), np.float32),

                length =  np.zeros((1, mask_h, mask_w), np.float32), 
                length_weight = np.zeros((1, mask_h, mask_w), np.float32),
                inv_length_weight = np.zeros((1, mask_h, mask_w), np.float32),
                gt_mask = np.zeros((1, mask_h, mask_w), np.float32),
            )
        return self.zero_field

    def process_mask(
        self,
        mask,
        gt_hm,
        start_point_off,
        mask_w,
        mask_h,
        hm_w,
        hm_h,
        ratio_hm_mask,
        anno_label=255,
        anno_color=255,
    ):
        assert mask is not None
        gt_points, gt_labels, gt_colors, idx2insidx = self.mask2point(
            mask,
            anno_label,
            anno_color,
        )
        valid_gt = []
        valid_gt_points = []
        valid_idx2insidx = []
        for i in range(len(gt_points)):
            gt_point = gt_points[i]
            if 0 < self.filter_pts < len(gt_point):
                gt_point = gt_point[self.filter_pts:]
            # pts = (gt_point + 0.5) / self.down_scale
            pts = gt_point / self.down_scale
            pts = pts[::-1]
            if len(pts) > 1:
                valid_gt.append(pts)
                valid_gt_points.append(gt_point)
                valid_idx2insidx.append(idx2insidx[i])
        gt_points = valid_gt_points

        # collect proposal gts
        gt_hm_lane_ends = []  # [hm end point,points]
        for l in valid_gt:
            if self.endpoint == "down":
                idx = 0
            elif self.endpoint == "top":
                idx = -1
            elif self.endpoint == "far":
                idx = int(len(l) * 0.75)
            elif self.endpoint == "near":
                idx = int(len(l) * 0.25)
            else:
                idx = len(l) // 2
            point = tuple(l[idx] * ratio_hm_mask)
            gt_hm_lane_ends.append([point, l])  # [end point,lane points]
        radius = [self.radius] * len(gt_hm_lane_ends)
        if len(gt_hm_lane_ends) >= 2:
            endpoints = [p[0] for p in gt_hm_lane_ends]
            for j in range(len(endpoints)):
                dis = self.min_dis_one_point(endpoints, j)
                if dis < 1.5 * radius[j]:
                    radius[j] = int(max(dis / 1.5, 1) + 0.49999)

        # init weight & gt maps & ref pos
        mask_w_ref = mask_w * self.num_ref_point
        max_fill_value = mask_w_ref + 1
        length_weight = np.arange(mask_h + 1, 1, -1, dtype=np.float32) / mask_h
        length_weight = length_weight.reshape(1, mask_h, 1).repeat(mask_w, axis=2)
        inv_length_weight = np.arange(1, mask_h + 1, 1, dtype=np.float32) / mask_h
        inv_length_weight = inv_length_weight.reshape(1, mask_h, 1).repeat(mask_w, axis=2)
        field_weight = np.ones((self.view_distance + 1, mask_h, mask_w_ref), np.float32) * self.bg_weight
        inv_field_weight = np.ones((self.inv_view_distance, mask_h, mask_w_ref), np.float32) * self.bg_weight

        gt_mask = np.zeros((1, mask_h, mask_w), np.float32)
        vector_field = np.zeros((self.view_distance + 1, mask_h, mask_w_ref), np.float32)
        inv_vector_field = np.zeros((self.inv_view_distance, mask_h, mask_w_ref), np.float32)
        length_field = np.zeros((1, mask_h, mask_w), np.float32)
        inv_length_field = np.zeros((1, mask_h, mask_w), np.float32)
        type_field = np.ones((mask_h // self.property_down_rate, mask_w // self.property_down_rate), np.long) * 255
        color_field = np.ones((mask_h // self.property_down_rate,  mask_w // self.property_down_rate), np.long) * 255

        len_off_grid = np.ones((mask_h, mask_w), np.float32) * max_fill_value
        vector_off_grid = np.ones((mask_h, mask_w_ref), np.float32) * max_fill_value
        attr_off_grid = np.ones(type_field.shape, np.float32) * max_fill_value

        x_field_ref_pos = np.arange(0.5 / self.num_ref_point, mask_w, 1 / self.num_ref_point)
        x_ref_pos = np.arange(0.5, mask_w, 1) / mask_w
        x_attr_ref_pos = np.arange(0.5, type_field.shape[1], 1) / type_field.shape[1]
        x_hm_pos = np.arange(0.5, hm_w, 1) / hm_w
        y_hm_pos = np.arange(0.5, hm_h, 1).reshape(hm_h, 1) / hm_h

        # fill weight and gt maps
        hm_down_rate = mask_w // hm_w
        res = []
        # import ipdb
        # ipdb.set_trace()
        for insidx, (end_point, line) in enumerate(gt_hm_lane_ends):
            if self.num_start_rows <= 0:
                gt_hm[0] = draw_umich_gaussian(gt_hm[0], end_point, radius[insidx],
                                               1, start_point_off, y_hm_pos, x_hm_pos, hm_h, hm_w)
            xs = line[:, 0]
            ys = line[:, 1]     # descending order
            y_min = int(np.min(ys))
            y_max = int(np.max(ys))
            start_row, end_row = y_max + 1, y_min - 2   # todo(liuhao1): check valid?  y_max, y_min - 1
            while start_row - end_row < 2:  # todo(liuhao1): may out of bounds
                end_row -= 1
            grid_y = np.arange(start_row, end_row, -1) + 0.5
            kind_id = min(len(line), 4)
            inter_kind = {2: 'slinear', 3: 'quadratic', 4: 'cubic'}[kind_id]
            grid_x = interpolate.interp1d(ys, xs, kind=inter_kind, fill_value="extrapolate", bounds_error=False)(grid_y)
            if kind_id >= 3:    # todo(liuhao1): check useful?
                # use linear mode intead of cubic for bound point
                grid_x[0] = grid_x[1] * 2 - grid_x[2]
            start_point_row = start_row
            delta_row = start_row - start_point_row
            if len(grid_x) > 2:
                while start_point_row >= mask_h or grid_x[delta_row] < 0 or grid_x[delta_row] >= mask_w:
                    if delta_row + 1 >= len(grid_x):
                        break
                    start_point_row -= 1
                    delta_row = start_row - start_point_row
            norm_start_x, norm_start_y = grid_x[delta_row] / mask_w, (start_point_row + 0.5) / mask_h
            for y in range(start_row, end_row, -1):
                if y >= mask_h:
                    continue
                ref_x = grid_x[start_row - y]
                norm_ref_x = ref_x / mask_w
                draw_r = self.draw_r
                if start_row >= y > end_row + 1:
                    angle = math.atan2(1, np.abs(grid_x[start_row - y + 1] - grid_x[start_row - y]))
                    half_line_width = draw_r * y / mask_h
                    draw_r = int(np.round(half_line_width / math.sin(angle))) + 1
                    if self.draw_r_max_ratio >= 1:
                        draw_r = min(draw_r, half_line_width * self.draw_r_max_ratio + 1)
                field_r = draw_r * self.num_ref_point

                s_x = int(np.clip(ref_x - draw_r + 1, 0, mask_w - 3))
                e_x = int(np.clip(ref_x + draw_r, s_x + 3, mask_w - 1))
                hm_s_x = min(max(s_x // hm_down_rate - 1, 0), mask_w // hm_down_rate - 1)
                hm_e_x = min(e_x // hm_down_rate + 1, mask_w // hm_down_rate)
                field_s_x = int(np.clip(ref_x * self.num_ref_point - field_r + 1, 0, mask_w_ref - 3))
                field_e_x = int(np.clip(ref_x * self.num_ref_point + field_r, field_s_x + 3, mask_w_ref))
                attr_s_x, attr_e_x = s_x // self.property_down_rate, e_x // self.property_down_rate
                attr_e_x = max(attr_s_x + 1, attr_e_x)

                gt_mask[0, y, s_x:e_x] = 1
                if self.num_start_rows > 0 and start_point_row - y < self.num_start_rows * hm_down_rate:
                    hm_y = y // hm_down_rate
                    start_point_off[0, hm_y, hm_s_x:hm_e_x] = norm_start_x - x_hm_pos[hm_s_x:hm_e_x]
                    start_point_off[1, hm_y, hm_s_x:hm_e_x] = norm_start_y - y_hm_pos[hm_y][0]
                    gt_hm[0, hm_y, hm_s_x:hm_e_x] = 1
                # vector field
                if y > end_row + self.num_drop_remote_point:
                    pre_off = vector_off_grid[y]
                    cur_off = ref_x - x_field_ref_pos
                    seleted_idxs = np.abs(pre_off) > np.abs(cur_off)
                    vector_off_grid[y, seleted_idxs] = cur_off[seleted_idxs]
                    seleted_idxs = seleted_idxs[field_s_x:field_e_x]
                    cur_off = cur_off[field_s_x:field_e_x]
                    for j in range(0, self.view_distance + 1):
                        if y - j > self.num_drop_remote_point and y - j > end_row:
                            next_x_ref = grid_x[start_row - (y - j)]
                            next_off = next_x_ref - x_field_ref_pos[field_s_x:field_e_x][seleted_idxs]
                            vector_field[j, y, field_s_x:field_e_x][seleted_idxs] = next_off
                            off_w = 1
                            if self.soft_weight_x:
                                # todo(liuhao1): cur_off = cur_off[field_s_x:field_e_x]
                                off_w = off_w * np.exp(- (self.soft_weight_off_alpha / draw_r) * cur_off ** 2)[seleted_idxs]
                            if self.soft_weight_y:
                                off_w = off_w * (2 * mask_h - y) / mask_h
                            field_weight[j, y, field_s_x:field_e_x][seleted_idxs] = off_w
                    for j in range(0, self.inv_view_distance):
                        pre_row_idx = y + j + 1
                        if pre_row_idx < start_row:
                            pre_x_ref = grid_x[start_row - pre_row_idx]
                            pre_x_off = pre_x_ref - x_field_ref_pos[field_s_x:field_e_x][seleted_idxs]
                            inv_vector_field[j, y, field_s_x:field_e_x][seleted_idxs] = pre_x_off
                            off_w = 1
                            if self.soft_weight_x:
                                off_w = off_w * np.exp(- (self.soft_weight_off_alpha / draw_r) * cur_off ** 2)[seleted_idxs]
                            inv_field_weight[j, y, field_s_x:field_e_x][seleted_idxs] = off_w
                # len field
                cur_off = norm_ref_x - x_ref_pos
                seleted_idxs = np.abs(cur_off) < np.abs(len_off_grid[y])
                len_off_grid[y, :][seleted_idxs] = cur_off[seleted_idxs]
                seleted_idxs = seleted_idxs[s_x:e_x]
                cur_off = cur_off[s_x:e_x]
                length_field[0, y, s_x:e_x][seleted_idxs] = max(y - int(y_min) + 1, 0)
                len_w = 1
                if self.soft_weight_x:
                    len_w = len_w * np.exp(- (self.soft_weight_len_alpha / draw_r) * (cur_off * mask_w) ** 2)[seleted_idxs]
                length_weight[0, y, s_x:e_x][seleted_idxs] = len_w
                inv_length_field[0, y, s_x:e_x][seleted_idxs] = max(start_row - y + 1, 0)
                len_w = 1
                if self.soft_weight_x:
                    len_w = len_w * np.exp(- (self.soft_weight_len_alpha / draw_r) * (cur_off * mask_w) ** 2)[seleted_idxs]
                inv_length_weight[0, y, s_x:e_x][seleted_idxs] = len_w

                attr_y = y // self.property_down_rate
                cur_off = norm_ref_x - x_attr_ref_pos
                seleted_idxs = np.abs(cur_off) < np.abs(attr_off_grid[attr_y])
                attr_off_grid[attr_y, seleted_idxs] = cur_off[seleted_idxs]
                seleted_idxs = seleted_idxs[attr_s_x:attr_e_x]
                type_field[attr_y, attr_s_x:attr_e_x][seleted_idxs] = gt_labels[insidx]
                color_field[attr_y, attr_s_x:attr_e_x][seleted_idxs] = -1 if anno_color is None else gt_colors[insidx]

            info = dict(
                y_max=y_max,
                y_min=y_min,
                type=gt_labels[insidx],
                color=gt_colors[insidx],
            )
            res.append(info)

        # import ipdb
        # ipdb.set_trace()
        d, h, w = vector_field.shape
        vector_field = vector_field.reshape(d, h, w // self.num_ref_point, self.num_ref_point)
        vector_field = vector_field.transpose(0, 3, 1, 2).reshape(
            d * self.num_ref_point, h, w // self.num_ref_point)
        field_weight = field_weight.reshape(d, h, w // self.num_ref_point, self.num_ref_point)
        field_weight = field_weight.transpose(0, 3, 1, 2).reshape(
            d * self.num_ref_point, h, w // self.num_ref_point)
        d, h, w = inv_vector_field.shape
        inv_vector_field = inv_vector_field.reshape(d, h, w // self.num_ref_point, self.num_ref_point)
        inv_vector_field = inv_vector_field.transpose(0, 3, 1, 2).reshape(
            d * self.num_ref_point, h, w // self.num_ref_point)
        inv_field_weight = inv_field_weight.reshape(d, h, w // self.num_ref_point, self.num_ref_point)
        inv_field_weight = inv_field_weight.transpose(0, 3, 1, 2).reshape(
            d * self.num_ref_point, h, w // self.num_ref_point)
        field = dict(
            offset=vector_field[0:self.num_ref_point],
            field=vector_field[self.num_ref_point:self.num_ref_point * 2],
            view=vector_field[self.num_ref_point * 2:],

            offset_weight=field_weight[:self.num_ref_point],
            field_weight=field_weight[self.num_ref_point:self.num_ref_point * 2],
            view_weight=field_weight[self.num_ref_point * 2:],

            length=length_field,
            length_weight=length_weight,

            inv_vector_field=inv_vector_field,
            inv_field_weight=inv_field_weight,
            inv_length=inv_length_field,
            inv_length_weight=inv_length_weight,

            gt_mask=gt_mask,
        )
        # if self.pre_view_distance > 0:
        #     field.extend([pre_vector_field, pre_weight, pre_length_field])
        # assert self.inv_view_distance == 0
        attrs = np.stack([type_field, color_field])
        # import ipdb
        # ipdb.set_trace()
        return res, field, attrs,  gt_points, valid_idx2insidx

    def target(self, results):
        output_h = int(results["img_shape"][0])
        output_w = int(results["img_shape"][1])
        mask_h = int(output_h // self.down_scale)  # 4x
        mask_w = int(output_w // self.down_scale)
        hm_h = int(output_h // self.hm_down_scale)  # 16x
        hm_w = int(output_w // self.hm_down_scale)
        ratio_hm_mask = self.down_scale / self.hm_down_scale

        results["hm_shape"] = [hm_h, hm_w]
        results["mask_shape"] = [mask_h, mask_w]
        results["down_scale"] = self.down_scale
        results["hm_down_scale"] = self.hm_down_scale

        def resize(im):
            if type(im) is np.ndarray:
                return cv2.resize(
                    im,
                    (mask_w // self.property_down_rate,
                     mask_h // self.property_down_rate),
                    interpolation=cv2.INTER_NEAREST,
                )
            else:
                return np.ones(
                    (mask_h // self.property_down_rate,
                     mask_w // self.property_down_rate), np.uint8) * im

        mask = results["laneline_mask"]
        label = results["laneline_label"]
        color = results["laneline_color"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, valid_idx2insidx = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label, color)

            # collect breakpoint gts
            self.kps2mask(mask, results.get("bks", []), gt_masks,
                          valid_idx2insidx, mask_h)

            lane = dict(key_points=gt_points, gt_masks=gt_masks)
            results["laneline_field"] = field
            results["laneline_attr"] = attribute_target
            results["laneline"] = lane
            results["ignore_laneline"] = False
        else:
            results["laneline_field"] = self.get_zero_field(mask_h, mask_w)
            results["laneline_attr"] = np.stack([
                resize(label),
                resize(color),
            ]).astype(np.long)
            results["laneline"] = dict(key_points=[], gt_masks=[])
            results["ignore_laneline"] = True
        results["laneline_start_point_off"] = start_point_off
        results["laneline_gt_hm"] = gt_hm

        mask = results["roadside_mask"]
        label = results["roadside_label"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        start_point_off = np.zeros((2, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, field, attribute_target, gt_points, _ = self.process_mask(
                mask, gt_hm, start_point_off, mask_w, mask_h, hm_w, hm_h,
                ratio_hm_mask, label)

            roadside = dict(key_points=gt_points, gt_masks=gt_masks)
            results["roadside_field"] = field
            results["roadside_attr"] = attribute_target[:1]
            results["roadside"] = roadside
            results["ignore_roadside"] = False
        else:
            results["roadside_field"] = self.get_zero_field(mask_h, mask_w)
            results["roadside_attr"] = resize(label)[None].astype(np.long)
            results["roadside"] = dict(key_points=[], gt_masks=[])
            results["ignore_roadside"] = True
        results["roadside_start_point_off"] = start_point_off
        results["roadside_gt_hm"] = gt_hm
        results["depth_map"] = results["depth_map"]
        # img = results['img'].data
        # img = img.cpu().detach().numpy()
        # img = img * np.array([50.5, 53.8, 54.3]).reshape(3, 1, 1)
        # img = img + np.array([75.3, 76.6, 77.6]).reshape(3,1,1)
        # img = img.clip(0,255).astype(np.uint8)
        # img = img.transpose(1,2,0)
        # from PIL import Image
        # Image.fromarray(img).save('img_{}.png'.format(np.random.randint(100)))

        return True
