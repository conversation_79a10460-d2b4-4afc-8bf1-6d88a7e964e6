import os
import json

from pillar.datasets import DATASETS
from pillar.datasets import BaseDataset
from ..core.evaluation.condlane_metric import main as eval


@DATASETS.register_module()
class LkdCropClsDataset(BaseDataset):

    def __init__(
            self,
            pipeline,
            ann_file="",
            img_prefix="",
            test_mode=False,
            no_anno=False,
            default_type="default",
            camera_roi={
                "front_fov30": {},
                "front_fov120": {},
                "rear_fov60": {}
            },
            camera_filter=False,
            categories={},
            break_point_range=(0, 1),
            aux_data_list={},
            blacklists=[],
            **kwargs,
    ):
        self.test_mode = test_mode
        self.no_anno = no_anno
        self.img_prefix = img_prefix
        self.ann_file = ann_file
        self.default_type = default_type
        self.camera_roi = camera_roi
        self.camera_filter = camera_filter
        self.categories = categories
        self.bk_roi = dict(
            t=break_point_range[0],
            b=break_point_range[1],
            l=0,
            r=1,
        )
        self.blacklists = blacklists
        # call load_annotations in super().__init__
        super().__init__(pipeline)

    def load_annotations(self):
        data_infos = []
        for i, line in enumerate(open(self.ann_file)):
            metas = json.loads(line)
            in_blacklists = False
            for filename in self.blacklists:
                if filename in metas["filename"]:
                    in_blacklists = True
                    break
            if in_blacklists:
                print(f"{metas['filename']} in blacklists")
                continue
            camera_index = metas.get("camera_index", self.default_type)
            if camera_index not in self.camera_roi:
                if self.camera_filter:
                    continue
                else:
                    camera_index = self.default_type
            laneline = metas.get("laneline", "")
            ignore_laneline = len(laneline) <= 0 or "ignore" in laneline
            roadside = metas.get("roadside", "")
            ignore_roadside = len(roadside) <= 0 or "ignore" in roadside
            depth_map = metas.get("depth_map", "")
            ignore_depthmap = len(depth_map) <= 0 or "ignore" in depth_map
            if (not self.test_mode) and ignore_laneline and ignore_roadside:
                # 训练模式下，既无roadside也无laneline，则不加入训练集
                continue
            break_points = metas.get("breakPoints", [])
            scene = metas.get("scene", [])
            if isinstance(scene, str):
                scene = ",".split(scene)
            img_info = dict(
                filename=metas["filename"],
                roi=self.camera_roi.get(camera_index, {}),
                bk_roi=self.bk_roi,
                scene=scene,
                camera_index=camera_index,
                break_points=break_points,
            )
            img_info["laneline"] = laneline
            img_info["laneline_type"] = metas.get("laneline_type", "")
            img_info["laneline_color"] = metas.get("laneline_color", "")
            img_info["roadside"] = roadside
            img_info["sidetype"] = metas.get("sidetype", "")
            img_info["depth_map"] = depth_map
            data_infos.append(
                dict(
                    img_info=img_info,
                    img_prefix=self.img_prefix,
                ))
        return data_infos

    def evaluate(self, results, metric=[], **kwargs):
        gts, preds = [], []
        for res in results["gt"]:
            gts.extend(res)
        for res in results["pred"]:
            preds.extend(res)
        return eval(gts, preds, self.categories.attr, metric, 64)

    def format_results(self, results, out_dir, **kwargs):
        if out_dir is None: out_dir = "result"
        os.makedirs(out_dir, exist_ok=True)
        print("\n" + out_dir)
        with open(os.path.join(out_dir,"ground_truth.txt"), "w") as f:
            for res in results["gt"]:
                for _ in res:
                    f.write(json.dumps(_) + "\n")
        with open(os.path.join(out_dir,"runner_result.txt"), "w") as f:
            for res in results["pred"]:
                for _ in res:
                    f.write(json.dumps(_) + "\n")
