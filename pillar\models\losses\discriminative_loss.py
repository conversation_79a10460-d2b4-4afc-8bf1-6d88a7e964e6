import torch
import torch.nn as nn

from ..builder import MODELS


@MODELS.register_module()
class DiscriminativeLoss(nn.Module):
    """ calculate discriminative loss """

    def __init__(self,
                 delta_d=3.0,
                 delta_v=0.5,
                 dist_term_weight=1.0,
                 var_term_weight=1.0,
                 reg_term_weight=0.001,
                 ignore_index=255,
                 loss_weight=1.0,
                 loss_name="discriminative_loss"):
        super(DiscriminativeLoss, self).__init__()
        # hyper parameter
        self.delta_d = delta_d
        self.delta_v = delta_v
        # loss weight of each part
        self.dist_w = dist_term_weight
        self.var_w = var_term_weight
        self.reg_w = reg_term_weight
        self.ignore_index = ignore_index
        # total loss weight
        self.loss_weight = loss_weight
        self._loss_name = loss_name

    def forward(self, embeddings, target, **kwargs):
        """
        embeddings: torch.Tensor, shape is [N, C, H, W]
        target: torch.Tensor, shape is [N, 1, H, W], value is instance id
        """
        N, C, H, W = embeddings.shape
        self.device = embeddings.device

        # calculate mean embeddings
        instance_means, mean2idx = self.calculate_means(embeddings, target, **kwargs)
        # calculate inter-instance loss
        dist_term = self.calculate_inter_dist(instance_means, mean2idx, **kwargs)
        # calculate inner-instance loss
        var_term = self.calculate_inner_var(
            embeddings, target, instance_means, mean2idx, **kwargs
        )
        # calculate regularization loss
        reg_term = self.calculate_regularization(instance_means, mean2idx, **kwargs)

        dis_loss = self.loss_weight * (
            self.dist_w * dist_term
            + self.var_w * var_term
            + self.reg_w * reg_term
        )
        return dis_loss

    def calculate_means(self, embeddings, target, **kwargs):
        """
        calculate mean of each instance

        embeddings: torch.Tensor, shape is [N, C, H, W]
        target: torch.Tensor, shape is [N, 1, H, W], value is instance id

        return: list of mean Tensor, list length is N, tensor shape is [M, C], M is instance nums
        """
        N, C, H, W = embeddings.shape
        embeddings = embeddings.view(N, C, -1)
        target = target.view(N, -1)

        batch_means, batch_mean2idx = [], []
        for batch_i in range(N):
            sample_means, sample_mean2idx = [], {}
            for ins_id in torch.unique(target[batch_i]):
                # ignore some ids, such as: backgound 0, ignore 255, etc
                if ins_id in [0, self.ignore_index]:
                    continue

                mask = target[batch_i] == ins_id
                ins_mean = torch.mean(embeddings[batch_i, :, mask], axis=-1)

                sample_mean2idx[ins_id.item()] = len(sample_means)
                sample_means.append(ins_mean)

            # if there is no valid instance, use a zero tensor to backward
            if len(sample_means) == 0:
                sample_means.append(0.0 * embeddings[batch_i, :, 0])
                sample_mean2idx[-1] = 0

            # sample_means shape is [M, C]
            sample_means = torch.stack(sample_means, dim=0)
            batch_means.append(sample_means)
            batch_mean2idx.append(sample_mean2idx)

        return batch_means, batch_mean2idx

    def calculate_inter_dist(self, instance_means, batch_mean2idx, norm=2, **kwargs):
        """
        push between instance means

        instance_means: list of mean Tensor, list length is N, tensor shape is [M, C], M is instance nums
        """
        N = len(instance_means)
        dist_term = 0.0

        for batch_i in range(N):
            means = instance_means[batch_i]
            sample_mean2idx = batch_mean2idx[batch_i]
            # no valid instance or only one instance in cur image
            if -1 in sample_mean2idx.keys() or means.shape[0] == 1:
                dist_term = dist_term + 0.0 * torch.mean(means)
                continue

            M, C = means.shape
            means_1 = means.unsqueeze(1).expand(M, M, C)
            means_2 = means_1.permute(1, 0, 2)

            # calculate euclidean distance of each mean pair
            diff = means_1 - means_2
            _norm = torch.norm(diff, norm, 2)

            # push away if closer than delta_d
            margin = self.delta_d * (1.0 - torch.eye(M, device=self.device))
            dist_term_sample = torch.sum(
                torch.clamp(margin - _norm, min=0.0) ** 2
            )

            # average by mean pair nums
            dist_term_sample = dist_term_sample / (M * (M - 1.0))
            dist_term = dist_term + dist_term_sample

        dist_term = dist_term / N
        return dist_term

    def calculate_inner_var(self, embeddings, target, instance_means,
                            batch_mean2idx, norm=2, **kwargs):
        """
        pull in each instance

        embeddings: torch.Tensor, shape is [N, C, H, W]
        target: torch.Tensor, shape is [N, 1, H, W], value is instance id
        instance_means: list of mean Tensor, list length is N, tensor shape is [M, C], M is instance nums
        """
        N, C, H, W = embeddings.shape
        embeddings = embeddings.view(N, C, -1).permute(0, 2, 1)
        target = target.view(N, -1)

        var_term = 0.0
        for batch_i in range(N):
            sample_means = instance_means[batch_i]
            sample_mean2idx = batch_mean2idx[batch_i]
            # no instance in cur image
            if -1 in sample_mean2idx.keys():
                var_term = var_term + 0.0 * torch.mean(sample_means)
                continue

            M, C = sample_means.shape
            var_term_sample = torch.tensor(
                0.0, device=self.device, requires_grad=True
            )
            for ins_id in torch.unique(target[batch_i]):
                # ignore unvalid instance
                if ins_id in [0, self.ignore_index]:
                    continue

                mask = target[batch_i] == ins_id
                ins_embs = embeddings[batch_i, mask, :]
                ins_mean = sample_means[sample_mean2idx[ins_id.item()]]

                # pull if farther than delta_v
                dist = (
                    torch.clamp(
                        torch.norm(ins_embs - ins_mean, p=norm, dim=-1)
                        - self.delta_v,
                        min=0.0,
                    )
                    ** 2
                )

                var_term_sample = var_term_sample + torch.sum(dist)

            # get binary foreground
            foreground_mask = torch.zeros_like(target[batch_i]) == 0
            for invalid_id in [0, self.ignore_index]:
                foreground_mask = foreground_mask & (
                    target[batch_i] != invalid_id
                )

            # average in sample
            var_term_sample = var_term_sample / torch.sum(foreground_mask)
            var_term = var_term + var_term_sample

        # average in batch
        var_term = var_term / N
        return var_term

    def calculate_regularization(self, instance_means, batch_mean2idx, norm=2, **kwargs):
        """
        pull instance means to origin

        instance_means: list of mean Tensor, list length is N, tensor shape is [M, C], M is instance nums
        """
        reg_term = 0.0
        for batch_i in range(len(instance_means)):
            sample_means = instance_means[batch_i]
            sample_mean2idx = batch_mean2idx[batch_i]
            # no instance in cur image
            if -1 in sample_mean2idx.keys():
                reg_term = reg_term + torch.mean(sample_means)
                continue
            _norm = torch.norm(sample_means, dim=-1)
            reg_term = reg_term + torch.mean(_norm)

        reg_term = reg_term / len(instance_means)
        return reg_term

    @property
    def loss_name(self):
        """Loss Name.
        This function must be implemented and will return the name of this
        loss function. This name will be used to combine different loss items
        by simple sum operation. In addition, if you want this loss item to be
        included into the backward graph, `loss_` must be the prefix of the
        name.
        Returns:
            str: The name of this loss item.
        """
        return self._loss_name
