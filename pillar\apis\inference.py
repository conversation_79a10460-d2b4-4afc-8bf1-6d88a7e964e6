import os
import time
import warnings
import mmcv
import torch
from torch.utils.data import ConcatDataset
from mmcv.utils import import_modules_from_strings
from mmcv.runner import load_checkpoint, wrap_fp16_model, get_dist_info
from mmcv.parallel import DataContainer as DC
from mme.models import build_model

from pillar.datasets import build_dataset


def init_model(config, checkpoint=None, device='cuda:0'):
    """Initialize a model from config file.

    Args:
        config (str or :obj:`mmcv.Config`): Config file path or the config
            object.
        checkpoint (str, optional): Checkpoint path. If left as None, the model
            will not load any weights.
        device (str, optional) CPU/CUDA device option. Default 'cuda:0'.
            Use 'cpu' for loading model on CPU.
    Returns:
        nn.Module: The constructed model.
    """
    if isinstance(config, str):
        config = mmcv.Config.fromfile(config)
    elif not isinstance(config, mmcv.Config):
        raise TypeError('config must be a filename or Config object, '
                        'but got {}'.format(type(config)))
    config.model.train_cfg = None
    if config.model.get('pretrained') is not None:
        config.model.pretrained = None
    model = build_model(config.model)
    if config.get('fp16'):
        wrap_fp16_model(model)
    if checkpoint is not None:
        map_loc = 'cpu' if device == 'cpu' else None
        checkpoint = load_checkpoint(model, checkpoint, map_location=map_loc)
        if 'CLASSES' in checkpoint.get('meta', {}):
            model.CLASSES = checkpoint['meta']['CLASSES']
        if 'PALETTE' in checkpoint.get('meta', {}):
            model.PALETTE = checkpoint['meta']['PALETTE']
    # an illegal memory access would be encountered for F.gelu once fusing
    # model = fuse_conv_bn(model)

    model.cfg = config  # save the config in the model for convenience
    model.to(device)
    return model


def concat_dataset(config, data_list, anno_flag, img_root=None, replace_petrel_conf_path=None):
    """Build dataset for inference

    Args:
        config (str or mmcv.Config): Config file path or the config object.
        data_list (list[str]): list of data files.
        anno_flag (bool): annotation or image files.
    Returns:
        Dataset: the concatenated dataset.
    """
    if isinstance(config, str):
        config = mmcv.Config.fromfile(config)
    elif not isinstance(config, mmcv.Config):
        raise TypeError('config must be a filename or Config object, '
                        'but got {}'.format(type(config)))

    # build the data pipeline
    demo_pipeline = getattr(config.get('demo_setting'), 'demo_pipeline',
                            config.data.test.pipeline)

    for op in demo_pipeline:
        if replace_petrel_conf_path is not None and hasattr(op, 'file_client_args') \
            and op.file_client_args.backend == 'petrel':
            op.file_client_args.conf_path = replace_petrel_conf_path
    
    if anno_flag:  # list of structured datasets
        demo_cfgs = list()
        for anno_file in data_list:
            demo_cfg = config.data.test.copy()
            if hasattr(demo_cfg, 'ann_file'):
                demo_cfg.ann_file = anno_file
            else:
                raise NotImplementedError
            if hasattr(demo_cfg, 'img_prefix'):
                demo_cfg.img_prefix = img_root
            elif hasattr(demo_cfg, 'data_prefix'):
                demo_cfg.data_prefix = img_root
            elif hasattr(demo_cfg, 'data_root'):
                demo_cfg.data_root = img_root
            else:
                raise NotImplementedError
            demo_cfg.pipeline = demo_pipeline.copy()
            demo_cfgs.append(dict(demo_cfg))
    else:  # imglist dataset
        data_infos = [
            dict(img_info=dict(filename=filename), img_prefix=img_root)
            for filename in data_list
        ]

        demo_cfgs = [
            dict(type='pillar.BaseDataset',
                 data_infos=data_infos,
                 pipeline=demo_pipeline.copy())
        ]

    # concatenate datasets
    datasets = list(map(build_dataset, demo_cfgs))
    dataset = ConcatDataset(
        datasets=datasets) if len(datasets) > 1 else datasets[0]
    return dataset


def _collect_results_func(*args, **kwargs):
    """
        Build collect function and then call it with given arguments.
        (Private Function)
    """
    collect_mode, scope = kwargs.pop('collect_mode'), kwargs.pop('scope')
    func_name = {
        'cpu': 'collect_results_cpu',
        'gpu': 'collect_results_gpu'
    }[collect_mode]
    scope = 'mmdet' if scope in ['pillar', None] else scope  # default scope
    # TODO: use unified test API
    pac_name = f'{scope}.apis.test'
    collect_func = getattr(import_modules_from_strings(pac_name), func_name)
    return collect_func(*args, **kwargs)


def inference_model(model,
                    data_loader,
                    work_dir,
                    function,
                    post_process=None,
                    collect_mode='cpu',
                    show_results=False):
    """Inference image with the model.

    Args:
        model (nn.Module): Model to demo.
        data_loader (nn.Dataloader): Pytorch data loader.
        work_dir (str, optional): Path to save results.
        post_process (bool, optional): Whether call post-processs or not.
        collect_mode (str or bool, optional): Mode to collect results.

    Returns:
        (list[dict]): The inference results.
    """
    # compatibility for parallel or distributed model
    module = getattr(model, 'module', model)
    if getattr(module, 'cfg', None) is not None:
        config = module.cfg
    else:
        raise NotImplementedError
    demo_setting = config.get('demo_setting')
    forward_kwargs = getattr(demo_setting, 'forward_kwargs',
                             dict(return_loss=False))
    if post_process is not None:
        if getattr(module, 'post_process', None) is None:
            warnings.warn(
                f'post_process function is not implemented for model: {module._get_name()}'
            )
        else:
            forward_kwargs['post_process'] = post_process
    save_sample = not bool(collect_mode)

    model.eval()

    rank, world_size = get_dist_info()
    sample_num = len(data_loader.dataset)
    if rank == 0:
        prog_bar = mmcv.ProgressBar(sample_num)
    time.sleep(2)  # This line can prevent deadlock problem in some cases.

    mmcv.mkdir_or_exist(work_dir)
    results = list()
    for batch_idx, data in enumerate(data_loader):
        with torch.no_grad():
            outputs = model(**forward_kwargs, **data)
        flip_struc = isinstance(outputs, dict)

        offset = batch_idx * data_loader.batch_size * world_size

        img_metas = data['img_metas']
        if isinstance(img_metas, list):
            img_metas = img_metas[0]
        
        if isinstance(img_metas, DC):
            img_metas = img_metas.data[0]
        else:
            raise NotImplementedError(
                f"unsupport img_metas type: {type(data['img_metas'])}")
        
        for idx, img_meta in enumerate(img_metas):
            output = {key: val[idx] for key, val in outputs.items(
            )} if flip_struc else outputs[idx]
            if not isinstance(output, dict):  # plain tensor
                output = dict(output=output)
            img_meta.update(dict(function=function))
            output['img_meta'] = img_meta
            output['idx'] = offset + idx * \
                world_size + rank  # DistributedSampler
            if save_sample:
                filename = img_meta.get('filename', img_meta.get(
                    'image_file', os.path.join(work_dir, str(output['idx']))))
                mmcv.dump(output, os.path.splitext(filename)[0] + '.pkl')
                continue
            results.append(output)
        if show_results:
            model.module.show_results(data, outputs, work_dir)
        if rank == 0:
            prog_bar.update(num_tasks=min(
                data_loader.batch_size * world_size, sample_num - prog_bar.completed))

    if save_sample:
        return None
    # collect results from all ranks
    if world_size > 1:
        results = _collect_results_func(
            results, sample_num, collect_mode=collect_mode,
            scope=getattr(demo_setting, 'repo', None))
    if results is None:
        return None

    assert len(results) == sample_num, \
        'mismatch between source data and output results'
    return results
