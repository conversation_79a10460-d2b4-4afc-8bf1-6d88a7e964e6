import numpy as np
import torch
from torch import nn
from torch.nn import functional as F
from mmcv.cnn import ConvModule, build_norm_layer
from mmcv.utils import print_log

from pillar.models import MODELS
from .ctnet_head import CtnetHead


class MLP(nn.Module):
    """ Very simple multi-layer perceptron (also called FFN)"""

    def __init__(self, input_dim, hidden_dim, output_dim, num_layers):
        super().__init__()
        self.num_layers = num_layers
        h = [hidden_dim] * (num_layers - 1)
        self.layers = nn.ModuleList(
            nn.Conv2d(n, k, 1)
            for n, k in zip([input_dim] + h, h + [output_dim]))

    def forward(self, x):
        for i, layer in enumerate(self.layers):
            x = F.relu(layer(x)) if i < self.num_layers - 1 else layer(x)
        return x


class AuxMaskHead(nn.Module):

    def __init__(
            self,
            in_channels,
            property_group,
            norm_cfg=dict(type="BN2d", requires_grad=True),
    ):
        super().__init__()
        self.property_group = property_group
        self.trans = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1),
            build_norm_layer(norm_cfg, in_channels)[1],
        )
        for i, out_dim in enumerate(property_group):
            net = nn.Sequential(
                nn.Conv2d(in_channels,
                          in_channels * 4,
                          kernel_size=3,
                          padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channels * 4, out_dim, kernel_size=1, padding=0),
            )
            self.add_module(f"aux_head_{i}", net)

    def forward(self, x):
        x = self.trans(x)
        outs = []
        for i in range(len(self.property_group)):
            outs.append(self.__getattr__(f"aux_head_{i}")(x))
        return torch.cat(outs, dim=1)


class LocationOp(nn.Module):

    def __init__(self, compute_locations_pre, location_configs):
        super().__init__()
        self.conv = nn.Conv2d(1, 1, 1, bias=False)
        nn.init.zeros_(self.conv.weight)
        _, _, H, W = location_configs["size"]
        self.x = nn.Parameter(torch.arange(0, W, dtype=torch.float32), False)
        self.y = nn.Parameter(torch.arange(0, H, dtype=torch.float32), False)
        self.conv.requires_grad = False

    def forward(self, data):
        data = self.conv(data[:, :1])
        x = data + self.x.view(1, 1, 1, -1) / self.x.size(0)
        y = data + self.y.view(1, 1, -1, 1) / self.y.size(0)
        data = torch.cat([x, y], 1)
        return data, self.x


class BreakHead(nn.Module):

    def __init__(self, in_channels, width):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, 3, padding=1)
        self.fc = nn.Conv2d(width, 1, 1)
        self.width = width

    def forward(self, x):
        N, C, H, W = x.shape
        # x = self.conv(x)
        x = x.view(-1, W, 1, 1)
        x = self.fc(x)
        x = x.view(-1, H)
        return x


@MODELS.register_module()
class CondLaneHead(nn.Module):

    def __init__(
        self,
        heads,
        in_channels,
        num_classes,
        property_group,
        multi_head=False,
        with_break_point=False,
        with_multi_aux_head=False,
        finetune_aux_head=False,
        finetune_break_point_head=False,
        mask_property_group=[2, 13, 6, 6, 2],
        head_channels=64,
        head_layers=1,
        disable_coords=False,
        branch_in_channels=288,
        branch_channels=64,
        branch_out_channels=64,
        reg_branch_channels=32,
        cls_embed_dim=256,
        vertical_mid_dim=64,
        branch_num_conv=1,
        norm_cfg=dict(type="BN2d", requires_grad=True),
        hm_idx=-1,
        mask_idx=0,
        compute_locations_pre=True,
        location_configs=None,
        mask_norm_act=True,
        regression=True,
        train_cfg=None,
        test_cfg=None,
        final_kernel=1,
        proposal_heatmap_thr=0.45,
        range_method="len_pred",
    ):
        super().__init__()
        self.num_classes = num_classes
        self.hm_idx = hm_idx  #1
        self.mask_idx = mask_idx  #0
        self.regression = regression
        self.with_break_point = with_break_point
        self.disable_coords = disable_coords
        if mask_norm_act:
            final_norm_cfg = dict(type="BN", requires_grad=True)
            final_act_cfg = dict(type="ReLU")
        else:
            final_norm_cfg = None
            final_act_cfg = None
        # mask branch
        mask_branch = []
        mask_branch.append(
            ConvModule(branch_in_channels,
                       branch_channels,
                       kernel_size=3,
                       padding=1,
                       norm_cfg=norm_cfg))
        for i in range(branch_num_conv):
            mask_branch.append(
                ConvModule(branch_channels,
                           branch_channels,
                           kernel_size=3,
                           padding=1,
                           norm_cfg=norm_cfg))
        mask_branch.append(
            ConvModule(branch_channels,
                       branch_out_channels * num_classes,
                       kernel_size=3,
                       padding=1,
                       norm_cfg=final_norm_cfg,
                       act_cfg=final_act_cfg))
        self.add_module("mask_branch", nn.Sequential(*mask_branch))
        self.branch_out_channels = branch_out_channels

        self.mask_weight_nums, self.mask_bias_nums = self.cal_num_params(
            head_layers, branch_out_channels, out_channels=1)

        self.num_mask_params = sum(self.mask_weight_nums) + sum(
            self.mask_bias_nums)

        self.reg_weight_nums, self.reg_bias_nums = self.cal_num_params(
            head_layers, branch_out_channels, out_channels=1)

        self.num_reg_params = sum(self.reg_weight_nums) + sum(
            self.reg_bias_nums)
        if self.regression:
            self.num_gen_params = self.num_mask_params + self.num_reg_params
        else:
            self.num_gen_params = self.num_mask_params
            self.num_reg_params = 0

        self.locations = LocationOp(
            compute_locations_pre=compute_locations_pre,
            location_configs=location_configs,
        )

        self.multi_head = multi_head
        if not multi_head:
            if "params" not in heads:
                heads["params"] = num_classes * (self.num_mask_params +
                                                 self.num_reg_params)
        else:
            assert num_classes == 2
            heads.pop("hm")
            heads["hm_1"] = 1
            heads["hm_2"] = 1
            heads["params_1"] = (self.num_mask_params + self.num_reg_params)
            heads["params_2"] = (self.num_mask_params + self.num_reg_params)
        self.ctnet_head = CtnetHead(
            heads,
            channels_in=in_channels[0],  # 64 in culane
            final_kernel=final_kernel,
            head_conv=head_channels)  # gen parameter

        self.cls_embed_dim = cls_embed_dim
        in_channels = in_channels[0]
        self.cls_mpl = nn.Sequential(
            nn.Conv2d(in_channels, in_channels * 4, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(in_channels * 4,
                      num_classes * cls_embed_dim,
                      kernel_size=3,
                      padding=1),
        )
        self.property_group = property_group
        self.property_fc = nn.Conv2d(
            cls_embed_dim,
            sum(property_group),
            1,
            bias=False,
        )
        self.mask_property_group = mask_property_group
        if with_multi_aux_head:
            self.property_mask_head = AuxMaskHead(in_channels,
                                                  mask_property_group,
                                                  norm_cfg)
        else:
            self.property_mask_head = nn.Sequential(
                nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Conv2d(in_channels,
                          sum(mask_property_group),
                          kernel_size=3,
                          padding=1),
            )
        self.finetune_aux_head = finetune_aux_head
        self.feat_width = location_configs["size"][-1]

        self.range_method = range_method
        assert range_method in ["vrange", "len_pred"]

        if with_break_point:
            if range_method == "len_pred":
                self.break_point_head = nn.Sequential(
                    nn.Conv2d(in_channels,
                              in_channels,
                              kernel_size=3,
                              padding=1),
                    nn.ReLU(),
                    nn.Conv2d(in_channels,
                              in_channels,
                              kernel_size=3,
                              padding=1),
                    build_norm_layer(norm_cfg, in_channels)[1],
                    nn.Conv2d(in_channels,
                              in_channels,
                              kernel_size=3,
                              padding=1),
                    nn.ReLU(),
                    nn.Conv2d(in_channels, 1, kernel_size=3, padding=1),
                )
            if range_method == "vrange":
                self.break_point_head = BreakHead(1, self.feat_width)
        self.finetune_break_point_head = finetune_break_point_head
        self.mlp = MLP(self.feat_width, vertical_mid_dim, 2, 2)
        self.proposal_heatmap_thr = proposal_heatmap_thr

    def cal_num_params(self, num_layers, channels, out_channels=1):
        weight_nums, bias_nums = [], []
        for l in range(num_layers):
            if l == num_layers - 1:
                if num_layers == 1 and not self.disable_coords:
                    weight_nums.append((channels + 2) * out_channels)
                else:
                    weight_nums.append(channels * out_channels)
                bias_nums.append(out_channels)
            elif l == 0:
                if not self.disable_coords:
                    weight_nums.append((channels + 2) * channels)
                else:
                    weight_nums.append(channels * channels)
                bias_nums.append(channels)

            else:
                weight_nums.append(channels * channels)
                bias_nums.append(channels)
        return weight_nums, bias_nums

    def maxpooling(self, heatmap, kernel=3):
        pad = (kernel - 1) // 2
        maxpool = nn.functional.max_pool2d(
            heatmap,
            (kernel, kernel),
            stride=1,
            padding=pad,
        )
        return maxpool

    def nms(self, heat, maxpool=None):
        if maxpool is None:
            maxpool = self.maxpooling(heat)
        heat = (heat >= maxpool).float() * heat
        return heat

    def ctdet_decode(self, heat, maxpool=None, thr=0.1):

        def _format(heat, inds):
            num_ins = [0] * heat.shape[0]
            ret = []
            for i, (n, c, y, x) in enumerate(zip(*inds)):
                id_class = c + 1
                coord = [x, y]
                score = heat[n, c, y, x]
                ret.append({
                    "coord": coord,
                    "id_class": id_class,
                    "score": score
                })
                num_ins[n] += 1

            return ret, num_ins

        heat_nms = self.nms(heat, maxpool)
        heat_nms = heat_nms.detach().cpu().numpy()
        inds = np.where(heat_nms > thr)
        seeds, num_ins = _format(heat_nms, inds)
        inds = torch.from_numpy(np.stack(inds, -1)).to(heat.device)
        return seeds, num_ins, inds

    def proposal_head(self, inputs, with_sigmoid=True):
        x_list = list(inputs)
        f_hm = x_list[self.hm_idx]
        f_mask = x_list[self.mask_idx]

        shared_branch = self.mask_branch(f_mask)

        z = self.ctnet_head(f_hm)
        if self.multi_head:
            proposal_heatmap, parameter_map = torch.cat(
                [z["hm_1"], z["hm_2"]],
                dim=1), torch.cat([z["params_1"], z["params_2"]], dim=1)
        else:
            proposal_heatmap, parameter_map = z["hm"], z["params"]
        maxpool = None
        if with_sigmoid:
            proposal_heatmap = proposal_heatmap.sigmoid()
            maxpool = self.maxpooling(proposal_heatmap)

        ret = {
            "proposal_heatmap": proposal_heatmap,
            "maxpool": maxpool,
            "feat_map": shared_branch,
            "parameter_map": parameter_map,
        }

        if self.range_method == "vrange":
            ret["cls_feat_map"] = self.cls_mpl(f_hm)
        else:
            aux_mask = self.property_mask_head(f_mask)
            if self.with_break_point:
                bk_mask = self.break_point_head(f_mask)
                aux_mask = torch.cat([aux_mask, bk_mask], dim=1)
            ret["aux_mask"] = aux_mask

        return ret

    def mid_process(
        self,
        pos_tensor,
        feat_map,
        parameter_map,
        cls_feat_map,
    ):
        pos_tensor = pos_tensor.long()
        assert pos_tensor.ndim > 1
        if pos_tensor.ndim > 1:
            pn = pos_tensor[:, 0]
            pc = pos_tensor[:, 1]
            ph = pos_tensor[:, 2]
            pw = pos_tensor[:, 3]
        _, _, H, W = list(parameter_map.size())
        parameter_map = parameter_map.view(-1, self.num_classes,
                                           self.num_gen_params, H, W)
        parameter_map = parameter_map.permute(0, 1, 3, 4, 2)
        if pos_tensor.ndim > 1:
            parameter = parameter_map[pn, pc, ph, pw]
        else:
            parameter_map = parameter_map.reshape(-1, self.num_gen_params)
            parameter = parameter_map[pos_tensor]
        parameter = parameter.view(-1, self.num_gen_params, 1, 1)

        cls_feat_map = cls_feat_map.view(-1, self.num_classes,
                                         self.cls_embed_dim, H, W)
        cls_feat_map = cls_feat_map.permute(0, 1, 3, 4, 2)
        if pos_tensor.ndim > 1:
            cls_feat = cls_feat_map[pn, pc, ph, pw]
        else:
            cls_feat_map = cls_feat_map.reshape(-1, self.cls_embed_dim)
            cls_feat = cls_feat_map[pos_tensor]
        cls_feat = cls_feat.view(-1, self.cls_embed_dim, 1, 1)
        _, _, H, W = list(feat_map.size())
        if pos_tensor.ndim > 1:
            feat_map = feat_map.view(-1, self.num_classes,
                                     self.branch_out_channels, H, W)
            feat_map = feat_map[pn, pc]
            # feat, x = self.cat_locations(feat)
        return parameter, cls_feat, feat_map

    def condconv_op(self, x, weights, biases):
        assert x.dim() == 4
        n_layers = len(weights)
        N, C, H, W = list(x.size())
        condconv = getattr(self, "condconv", "matmul")
        for i, (w, b) in enumerate(zip(weights, biases)):
            if condconv.startswith("matmul"):
                x = x.view(-1, 1, C, H * W)
                w = w.view(-1, 1, 1, C)
                x = w @ x + b
                x = x.view(-1, 1, H, W)  #* C
            elif condconv.startswith("muladd"):
                # x = (x * w) + b
                x = (x * w) + b
                if condconv.endswith("sum"):
                    x = x.sum(1, True)
            if i < n_layers - 1:
                x = F.relu(x)
        return x

    def param_slice(self, parameter, *args):
        # for ti, torch.split not support well
        i = 0
        params = []
        for arg in args:
            group = []
            for n in arg:
                group.append(parameter[:, i:i + n])
                i += n
            params.append(group)
        return params

    def vrange_head(self, loc_map, off_map, cls_feat=None):
        N, C, H, W = loc_map.shape
        ret = {
            "loc_map": loc_map,
            "off_map": off_map,
        }
        if cls_feat is not None:
            if self.with_break_point and self.range_method == "vrange":
                ret["break_mask"] = self.break_point_head(loc_map)
            loc_map = loc_map.view(-1, W, 1, 1)
            vertical_range = self.mlp(loc_map)
            ret["vertical_range"] = vertical_range.view(-1, H, 2)
            property_score = self.property_fc(cls_feat)
            property_score = property_score.squeeze(-1).squeeze(-1)
            if not self.training:
                groups = torch.split(property_score, self.property_group, -1)
                groups = [torch.softmax(_, -1) for _ in groups]
                property_score = torch.cat(groups, -1)
            ret["property_score"] = property_score
        return ret

    def conditional_shape_head(self, feat, parameter, cls_feat=None):
        loc_w, loc_b, off_w, off_b = self.param_slice(
            parameter,
            self.mask_weight_nums,
            self.mask_bias_nums,
            self.reg_weight_nums,
            self.reg_bias_nums,
        )

        locations, x = self.locations(feat)
        if not self.disable_coords:
            feat = torch.cat([locations, feat], dim=1)
        off_map = self.condconv_op(feat, off_w, off_b)
        loc_map = self.condconv_op(feat, loc_w, loc_b)
        ret = {
            "loc_map": loc_map,
            "off_map": off_map,
        }
        if cls_feat is not None:
            ret = self.vrange_head(**ret, cls_feat=cls_feat)

        if getattr(self, "tokestrel", False):
            loc_map = torch.softmax(loc_map, -1)
            if self.range_method == "vrange":
                H, W = loc_map.shape[-2:]
                loc_map = F.conv2d(loc_map.view(-1, W, 1, 1),
                                   x.view(1, W, 1, 1))
                loc_map = loc_map.view(-1, H)
                ret["off_map"] = off_map.squeeze(1)
            ret["loc_map"] = loc_map
        return ret

    def forward_train(self, inputs, pos_tensor, num_ins):
        """
        inputs: feature map pyramid, tuple(torch.Tensor(N, C, H, W), ...)
        pos_tensor: point indices, (in, ic, ih, iw), torch.Tensor(M, 4)
        num_ins: list of point counts
        """
        proposal_o = self.proposal_head(inputs, False)
        if self.range_method == "len_pred":
            proposal_o["cls_feat_map"] = self.cls_mpl(inputs[self.hm_idx])
        else:
            proposal_o["aux_mask"] = self.property_mask_head(
                inputs[self.mask_idx])

        if pos_tensor.size(0) <= 0:
            return proposal_o

        parameter, cls_feat, feat = self.mid_process(
            pos_tensor,
            proposal_o["feat_map"],
            proposal_o["parameter_map"],
            proposal_o["cls_feat_map"],
        )
        detect_i = {
            # detect input
            "feat": feat,
            "parameter": parameter,
        }
        if self.range_method == "vrange":
            detect_i["cls_feat"] = cls_feat
        detect_o = self.conditional_shape_head(**detect_i)
        if self.range_method == "len_pred":
            detect_o = self.vrange_head(**detect_o, cls_feat=cls_feat)

        return dict(**proposal_o, **detect_o)

    def forward_test(self, inputs):
        proposal_o = self.proposal_head(inputs)
        if self.range_method == "len_pred":
            cls_feat_map = self.cls_mpl(inputs[self.hm_idx])
        else:
            cls_feat_map = proposal_o["cls_feat_map"]

        seeds, num_ins, pos_tensor = self.ctdet_decode(
            proposal_o["proposal_heatmap"],
            proposal_o["maxpool"],
            thr=self.proposal_heatmap_thr,
        )
        if pos_tensor.size(0) == 0:
            return dict(proposal_indices=pos_tensor, **proposal_o)

        parameter, cls_feat, feat = self.mid_process(
            pos_tensor,
            proposal_o["feat_map"],
            proposal_o["parameter_map"],
            cls_feat_map,
        )
        detect_i = {
            # detect input
            "feat": feat,
            "parameter": parameter,
        }
        if self.range_method == "vrange":
            detect_i["cls_feat"] = cls_feat
        detect_o = self.conditional_shape_head(**detect_i)
        if getattr(self, "tokestrel", False):
            return proposal_o, detect_i, detect_o

        if self.range_method == "len_pred":
            detect_o = self.vrange_head(**detect_o, cls_feat=cls_feat)
        loc_map = torch.softmax(detect_o["loc_map"], -1)
        if self.range_method == "vrange":
            H, W = loc_map.shape[-2:]
            x = torch.arange(0, W, dtype=loc_map.dtype, device=loc_map.device)
            loc_map = F.linear(loc_map.view(-1, W), x.view(1, W)).view(-1, H)
            detect_o["off_map"] = detect_o["off_map"].squeeze(1)
        detect_o["loc_map"] = loc_map
        return dict(
            proposal_indices=pos_tensor,
            **proposal_o,
            **detect_i,
            **detect_o,
        )

    def inference_mask(self, pos):
        pass

    def forward(
        self,
        x_list,
        hm_thr=0.3,
    ):
        return self.forward_test(x_list, )

    def load_pretrained_dict(self, pretrained=None):
        renamed_dict = {}
        pretrained_dict = torch.load(pretrained,
                                     map_location="cpu")["state_dict"]
        for key, value in pretrained_dict.items():
            new_key = ".".join(key.split(".")[1:])
            renamed_dict[new_key] = value
        return renamed_dict

    def check_keys(self, own_keys, other_keys, own_name):
        own_keys = set(own_keys)
        other_keys = set(other_keys)
        shared_keys = own_keys & other_keys
        unexpected_keys = other_keys - own_keys
        missing_keys = own_keys - other_keys

        info = "Loading {}:{} shared keys, {} unexpected keys, {} missing keys.".format(
            own_name, len(shared_keys), len(unexpected_keys),
            len(missing_keys))

        if len(missing_keys) > 0:
            info += "\nmissing keys are as follows:\n    {}".format(
                "\n    ".join(missing_keys))
        print_log(info, logger="root")

    def init_weights(self, pretrained=None):
        if isinstance(pretrained, str):
            print_log(f"CondLaneHead: load model from: {pretrained}",
                      logger="root")
            pretrained_dict = self.load_pretrained_dict(pretrained)
            _ = pretrained_dict.pop("locations.y")
            _ = pretrained_dict.pop("locations.x")
            self.load_state_dict(pretrained_dict, strict=False)
            self.check_keys(self.state_dict().keys(), pretrained_dict.keys(),
                            "CondLaneHead")


@MODELS.register_module()
class CondLaneHead3x3(CondLaneHead):

    def __init__(self, final_kernel=3, *args, **kwargs):
        final_kernel = 3
        super().__init__(final_kernel=final_kernel, *args, **kwargs)
