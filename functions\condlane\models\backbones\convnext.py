import torch
import torch.nn as nn
from mmcv.cnn import build_norm_layer
from mmcv.utils import print_log
from timm.models.layers import trunc_normal_, DropPath

from pillar.models import MODELS


class Block(nn.Module):
    r""" ConvNeXt Block. There are two equivalent implementations:
    (1) DwConv -> LayerNorm (channels_first) -> 1x1 Conv -> GELU -> 1x1 Conv; all in (N, C, H, W)
    (2) DwConv -> Permute to (N, H, W, C); LayerNorm (channels_last) -> Linear -> GELU -> Linear; Permute back
    We use (2) as we find it slightly faster in PyTorch

    Args:
        dim (int): Number of input channels.
        drop_path (float): Stochastic depth rate. Default: 0.0
        layer_scale_init_value (float): Init value for Layer Scale. Default: 1e-6.
    """

    def __init__(
        self,
        dim,
        drop_path=0.,
        layer_scale_init_value=1e-6,
        norm_cfg=dict(type="BN2d", requires_grad=True),
        Activation=nn.GELU,
        kernel_size=1,
        depthwise_conv=True
    ):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3,
                                groups=dim if depthwise_conv else 1)  # depthwise conv
        self.norm = build_norm_layer(norm_cfg, dim)[1]
        # pointwise/1x1 convs, implemented with linear layers
        padding = int(kernel_size // 2)
        self.pwconv1 = nn.Conv2d(dim, 4 * dim, kernel_size, 1, padding)
        self.pwconv2 = nn.Conv2d(4 * dim, dim, kernel_size, 1, padding)
        self.act = Activation()
        self.gamma = nn.Parameter(
            layer_scale_init_value * torch.ones(dim),
            requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(
            drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = self.norm(x)
        # _, _, H, W = x.size()
        # x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        # x = x.reshape(-1, self.pwconv1.in_features)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = x * self.gamma.view(-1, self.pwconv2.out_channels, 1, 1)
        # x = x.view(-1, H, W, self.pwconv2.out_features)
        # x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x


@MODELS.register_module()
class ConvNeXt(nn.Module):
    r""" ConvNeXt
        A PyTorch impl of : `A ConvNet for the 2020s`  -
          https://arxiv.org/pdf/2201.03545.pdf

    Args:
        in_chans (int): Number of input image channels. Default: 3
        num_classes (int): Number of classes for classification head. Default: 1000
        depths (tuple(int)): Number of blocks at each stage. Default: [3, 3, 9, 3]
        dims (int): Feature dimension at each stage. Default: [96, 192, 384, 768]
        drop_path_rate (float): Stochastic depth rate. Default: 0.
        layer_scale_init_value (float): Init value for Layer Scale. Default: 1e-6.
        head_init_scale (float): Init scaling value for classifier weights and biases. Default: 1.
    """

    def __init__(
        self,
        in_chans=3,
        depths=[3, 3, 9, 3],
        dims=[96, 192, 384, 768],
        drop_path_rate=0.,
        layer_scale_init_value=1e-6,
        freeze_layer=0,
        norm_cfg=dict(type="BN2d", requires_grad=True),
        ActType="GELU",
        kernel_size=1,
        depthwise_conv=True,
    ):
        super().__init__()
        Activation = nn.GELU if ActType == "GELU" else nn.ReLU
        self.downsample_layers = nn.ModuleList(
        )  # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4), #kernel_size=4,stride=4
            build_norm_layer(norm_cfg, dims[0])[1],
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                build_norm_layer(norm_cfg, dims[i])[1],
                nn.Conv2d(dims[i], dims[i + 1], kernel_size=2, stride=2), #kernel_size=2, stride=2
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList()
        # 4 feature resolution stages, each consisting of multiple residual blocks
        dp_rates = [
            x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))
        ]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(*[
                Block(
                    dim=dims[i],
                    drop_path=dp_rates[cur + j],
                    layer_scale_init_value=layer_scale_init_value,
                    norm_cfg=norm_cfg,
                    Activation=Activation,
                    kernel_size=kernel_size,
                ) for j in range(depths[i])
            ])
            self.stages.append(stage)
            cur += depths[i]
        for i in range(freeze_layer):
            for p in self.downsample_layers[i].parameters():
                p.requires_grad = False
            for p in self.stages[i].parameters():
                p.requires_grad = False

    def load_pretrained_dict(self, pretrained=None):
        renamed_dict = {}
        pretrained_dict = torch.load(pretrained,
                                     map_location="cpu")["state_dict"]
        for key, value in pretrained_dict.items():
            new_key = ".".join(key.split(".")[1:])
            renamed_dict[new_key] = value
        return renamed_dict

    def check_keys(self, own_keys, other_keys, own_name):
        own_keys = set(own_keys)
        other_keys = set(other_keys)
        shared_keys = own_keys & other_keys
        unexpected_keys = other_keys - own_keys
        missing_keys = own_keys - other_keys

        info = "Loading {}:{} shared keys, {} unexpected keys, {} missing keys.".format(
            own_name, len(shared_keys), len(unexpected_keys),
            len(missing_keys))

        if len(missing_keys) > 0:
            info += "\nmissing keys are as follows:\n    {}".format(
                "\n    ".join(missing_keys))
        print_log(info, logger="root")

    def init_weights(self, pretrained=None):
        if isinstance(pretrained, str):
            print_log(f"backbone: load model from: {pretrained}",
                      logger="root")
            pretrained_dict = self.load_pretrained_dict(pretrained)
            self.load_state_dict(pretrained_dict, strict=False)
            self.check_keys(self.state_dict().keys(), pretrained_dict.keys(),
                            "backbone")

        elif pretrained is None:
            for m in self.modules():
                if isinstance(m, (nn.Conv2d, nn.Linear)):
                    trunc_normal_(m.weight, std=.02)
                    nn.init.constant_(m.bias, 0)
                # elif isinstance(m, (_BatchNorm, nn.GroupNorm)):
                #     nn.init.constant_(m, 1)
                #

    def forward(self, x):
        out = []
        for i in range(4):
            #print("==========BEFORE downsample_layers x.shape is: ", x.shape)
            x = self.downsample_layers[i](x)
            #print("==========AFTER downsample_layers AND BEFORE stages x.shape is: ", x.shape)
            x = self.stages[i](x)
            #print("==========AFTER stages x.shape is: ", x.shape)
            out.append(x)
       	
#        print("===============OUT in convnext.forward==============")
#        for i,each_out in enumerate(tuple(out)):
#            print(f"out{i} in out: {out[i].shape}")
        return tuple(out)


@MODELS.register_module()
class ConvNeXt3x3(ConvNeXt):

    def __init__(self, kernel_size=3, **kwargs):
        super().__init__(kernel_size=3, **kwargs)


@MODELS.register_module()
class ConvNeXtTI(ConvNeXt):

    def __init__(
        self,
        in_chans=3,
        depths=[3, 3, 9, 3],
        dims=[96, 192, 384, 768],
        drop_path_rate=0.,
        layer_scale_init_value=1e-6,
        freeze_layer=0,
        norm_cfg=dict(type="BN2d", requires_grad=True),
        NormType="",
        ActType="ReLU",
        depthwise_conv=True,
    ):
        if NormType == "BatchNorm":
            norm_cfg = dict(type="BN2d", requires_grad=True)
        if NormType == "LayerNorm":
            norm_cfg = dict(type="LN", requires_grad=True)
        super().__init__()
        if ActType == "GELU":
            Activation = nn.GELU
        elif ActType == "PReLU":
            Activation = nn.PReLU
        elif ActType == "ReLU6":
            Activation = nn.ReLU6
        else:
            Activation = nn.ReLU
        self.downsample_layers = nn.ModuleList(
        )  # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=5, stride=2, padding=2),
            build_norm_layer(norm_cfg, dims[0])[1],
            Activation(),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                build_norm_layer(norm_cfg, dims[i])[1],
                nn.Conv2d(dims[i],
                          dims[i + 1],
                          kernel_size=3,
                          stride=2,
                          padding=1),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList()
        # 4 feature resolution stages, each consisting of multiple residual blocks
        dp_rates = [
            x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))
        ]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(*[
                Block(dim=dims[i],
                      drop_path=dp_rates[cur + j],
                      layer_scale_init_value=layer_scale_init_value,
                      norm_cfg=norm_cfg,
                      Activation=Activation,
                      depthwise_conv=depthwise_conv) for j in range(depths[i])
            ])
            self.stages.append(stage)
            cur += depths[i]
        for i in range(freeze_layer):
            for p in self.downsample_layers[i].parameters():
                p.requires_grad = False
            for p in self.stages[i].parameters():
                p.requires_grad = False
