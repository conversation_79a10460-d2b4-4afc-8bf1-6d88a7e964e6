import warnings
from .cli import (click, COMMAND_SETTINGS, train as _train,
                  test as _test, infer as _infer, run as _run)


def __parse_default_param(params: list):
    options, arguments = dict(), list()
    for param in params:
        if getattr(param, 'help', None) is None:
            arguments.append(param.name)
            continue
        options[param.name] = param.default
    return options, arguments


def decorator(func):
    """Decorator for scripting interface"""
    def wrapper(*args, **kwargs):
        options, arguments = __parse_default_param(func.params)
        assert len(
            arguments) == 2 and arguments[-1] == 'other_args', 'invalid parameters'
        params = dict(**options)
        args = list(args)
        if len(args) > 0:
            assert arguments[0] not in kwargs, f'Argument {arguments[0]} is assigned twice'
            params[arguments[0]] = args.pop(0)
        else:
            params[arguments[0]] = None
        if len(args) > 0:
            assert arguments[1] not in kwargs, f'Argument {arguments[1]} is assigned twice'
        params[arguments[1]] = tuple(args)
        for p_name, p_val in kwargs.items():
            if p_name in params:
                params[p_name] = p_val
                continue
            warnings.warn(
                f'Argument {p_name} is unknown for function {func.name}')
        with click.Context(command=func, **COMMAND_SETTINGS['context_settings']).scope():
            try:
                return func.callback(**params)
            except click.exceptions.Exit as exc:
                assert exc.exit_code == 0, f'Abnormal exit occurs with code {exc.exit_code}'

    wrapper.__doc__ = func.__doc__
    return wrapper


train = decorator(_train)
test = decorator(_test)
infer = decorator(_infer)
run = decorator(_run)


__all__ = ['train', 'test', 'infer', 'run']
