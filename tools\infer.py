import argparse
import os

from mmcv import DictAction

from pillar.algorithm_backend.builder import build_algorithm_backend


def parse_args():
    """
        parse arguments
    """
    parser = argparse.ArgumentParser(description='Pillar Inference')
    parser.add_argument('config', help='config file')
    parser.add_argument('checkpoint', help='checkpoint file')
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.')
    parser.add_argument('--data', nargs='+', help='image data')
    parser.add_argument('--img-root', default='', help='root of data')
    parser.add_argument('--launcher',
                        default='none',
                        choices=['none', 'pytorch', 'slurm', 'mpi'],
                        help='job launcher')
    parser.add_argument('--local_rank', type=int, default=0)
    parser.add_argument('--ckpt-device',
                        default='cpu',
                        choices=['cpu', 'cuda'],
                        help='device to load checkpoint')
    parser.add_argument('--batch-size', type=int, help='#samples per gpu')
    parser.add_argument('--worker-num', type=int, help='#workers per gpu')
    parser.add_argument('--pin-memory',
                        action='store_true',
                        help='copy tensors into cuda pinned memory or not')
    parser.add_argument('--post-process',
                        action='store_true',
                        help='call post-process or not')
    parser.add_argument('--show-results',
                        action='store_true',
                        help='show results visualization or not')
    parser.add_argument(
        '--work-dir', help='the directory to save the file containing outputs')
    parser.add_argument('--algorithm-backend',
                        choices=['OpenMMLab', 'Spring'],
                        default='OpenMMLab',
                        help='algorithm library backend')
    parser.add_argument('--function', type=str, help='pillar function name')
    parser.add_argument('--petrel-conf-path',
                        type=str,
                        default='~/petreloss.conf',
                        help='config path of petrel client')

    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)
    return args


def main():
    args = parse_args()

    backend_cfg = dict(type=args.algorithm_backend, args=args)
    backend = build_algorithm_backend(backend_cfg)
    backend.inference()


if __name__ == '__main__':
    main()
