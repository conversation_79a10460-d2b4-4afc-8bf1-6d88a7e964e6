from __future__ import division
import re
import os.path as osp
import sys
import importlib

import torch.multiprocessing as mp
from pod.utils.dist_helper import finalize, setup_distributed, gpu_check
from pod.utils.log_helper import default_logger as logger
from pod.utils.yaml_loader import load_yaml
from pod.utils.user_analysis_helper import send_info
from pod.utils.registry_factory import POD_HELPER_REGISTRY

from .base_backend import BaseAlgorithmBackend
from .builder import ALGORITHM_BACKEND
from .utils import generate_datalist


@ALGORITHM_BACKEND.register_module()
class Spring(BaseAlgorithmBackend):
    def __init__(self, args):
        super().__init__(args)

        setup_distributed()
        self.cfg = load_yaml(self.args.config)
        self._import_module()
    
    def _import_module(self):
        config = osp.realpath(self.args.config)
        regex = 'pillar/functions/(\w+)/'
        match = re.search(regex, config)
        if match:
            importlib.import_module(match.group(1))
    
    def train(self):
        mp.set_start_method(self.args.fork_method, force=True)
        fork_method = mp.get_start_method(allow_none=True)
        assert fork_method == self.args.fork_method
        logger.info('fork method:{}'.format(fork_method))
        sys.stdout.flush()

        if self.args.test_gpu:
            gpu_check()

        self.cfg['saver']['save_dir'] = self.args.work_dir

        self.cfg['args'] = {
            'ddp': self.args.ddp,
            'config_path': self.args.config,
            'asynchronize': self.args.asynchronize,
            'nocudnn': self.args.nocudnn,
            'display': self.args.display,
            'no_running_config': self.args.no_running_config,
            'allow_dead_parameter': self.args.allow_dead_parameter,
            'opts': self.args.opts
        }
        train_phase = self.args.phase
        pod_helper_type = self.cfg.get('pod_helper_type', 'base')
        pod_helper_class = POD_HELPER_REGISTRY.get(pod_helper_type)
        inference_only = False
        if self.args.evaluate:
            inference_only = True
            train_phase = "eval"
        pod_helper = pod_helper_class(self.cfg, inference_only=inference_only)
        send_info(self.cfg, train_phase)
        train_func = {"train": pod_helper.train, "eval": pod_helper.evaluate}
        if 'bignas' in pod_helper_type:
            bignas_func = {
                "train_supnet": pod_helper.train,
                "sample_flops": pod_helper.sample_multiple_subnet_flops,
                "sample_accuracy":
                    pod_helper.sample_multiple_subnet_accuracy,
                "evaluate_subnet": pod_helper.evaluate_subnet,
                "finetune_subnet": pod_helper.finetune_subnet,
                "sample_subnet": pod_helper.sample_subnet_weight
            }
            assert train_phase in bignas_func, f"{train_phase} is not supported"
            train_func.update(bignas_func)
        train_func[train_phase]()

        finalize()

    def inference(self):
        # generate data list
        data_list, anno_num = generate_datalist(self.args.data, self.args.img_root)
        if anno_num:
            meta_file = data_list
        else:
            meta_file = [dict(filename=filename, instances=[]) for filename in data_list]
        
        self.cfg['dataset']['test']['dataset']['kwargs']['meta_file'] = meta_file
        self.cfg['dataset']['test']['dataset']['kwargs']['image_reader']['kwargs']['image_dir'] = self.args.img_root
        self.cfg['saver']['save_dir'] = self.args.work_dir
        self.cfg['saver']['results_dir'] = self.args.work_dir
        self.cfg['saver']['resume_model'] = self.args.checkpoint

        send_info(self.cfg, func='inference')
        pod_helper_type = self.cfg.get('pod_helper_type', 'base')
        pod_helper = POD_HELPER_REGISTRY[pod_helper_type](self.cfg, inference_only=True)

        pod_helper.inference()

        finalize()


    def test(self):
        print("===============INSIDE SPRING==================")
        self.cfg['saver']['save_dir'] = self.args.work_dir
        self.cfg['saver']['results_dir'] = self.args.work_dir
        self.cfg['saver']['resume_model'] = self.args.checkpoint

        send_info(self.cfg, func='test')
        pod_helper_type = self.cfg.get('pod_helper_type', 'base')
        pod_helper = POD_HELPER_REGISTRY[pod_helper_type](self.cfg, inference_only=True)

        pod_helper.evaluate()

        finalize()
