import os
import json

def combine_cpp_results_with_image_names(directory):
    combined_results = {}

    for filename in os.listdir(directory):
        print(f"Working on: {filename}")
        if filename.endswith('.json'):
            file_path = os.path.join(directory, filename)
            with open(file_path, 'r') as file:
                try:
                    data = json.load(file)
                    if data is None:
                        print(f"Skipping empty or 'null' file: {filename}")
                        continue
                except json.JSONDecodeError:
                    print(f"Skipping invalid JSON file: {filename}")
                    continue

                image_name = filename.split('.')[0]+"."+filename.split('.')[1]
                print(f"Combined {image_name} results")
                combined_results[image_name] = data
    #json_path = '/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/combined_cpp_results_with_images.json'
    #save the merged json
    with open('combined_cpp_results_with_images.json', 'w') as outfile:
        json.dump(combined_results, outfile, indent=4)

    #print("Combined C++ results saved to 'combined_cpp_results_with_images.json'.")

#abs_path = "/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/cpp_res"
combine_cpp_results_with_image_names("cpp_res")