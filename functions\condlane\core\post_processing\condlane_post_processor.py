import ipdb
import numpy as np
from functools import cmp_to_key


def sigmoid(z):
    return np.where(z >= 0, 1 / (1 + np.exp(-z)), np.exp(z) / (1 + np.exp(z)))


def relu6(z):
    return np.where(np.where(z < 0, 0, z) > 6, 6, np.where(z < 0, 0, z))


def Hsigmoid(z):
    return relu6(z + 3.0) / 6.0


def gather_row(row, x_i, kernel=3):
    W = row.shape[-1]
    values = [row[x_i]] * kernel
    for dx in range(1, 1 + int(kernel // 2)):
        if x_i + dx < W:
            values[+dx] = row[x_i + dx]
        if x_i - dx >= 0:
            values[-dx] = row[x_i - dx]
    # return sum(values) / kernel
    return max(values)


def softmax(scores, axis=-1):
    exp = np.exp(scores - np.max(scores, axis, keepdims=True))
    p = exp / np.sum(exp, axis, keepdims=True)
    return p


def update_overlap(dp, pts, r):
    H, W = dp.shape[-2:]
    overlap_weight = [3, 2, 1, 0.5, 0.2, 0.1, 0.01, 0.01, 0.01, 0.01
                      ] + [0.01] * 9
    for (x_idx, y_idx) in pts:
        dp[y_idx, x_idx] = max(dp[y_idx, x_idx], 3)
        for dx in range(-r, r + 1, 1):
            for dy in range(-r // 2, r // 2 + 1, 1):
                new_x_idx, new_y_idx = x_idx + dx, y_idx + dy
                if 0 <= new_x_idx < W and 0 <= new_y_idx < H:
                    dp[new_y_idx, new_x_idx] = max(
                        dp[new_y_idx, new_x_idx],
                        overlap_weight[max(np.abs(dx), np.abs(dy))])


def update_visit(visit, line, value=+1, th=2, repeat=1):
    H, W = visit.shape[-2:]
    L = len(line)

    def expand(ss, es):
        ss_ = []
        es_ = []
        for i, (start, stop) in enumerate(zip(ss, es)):
            if i > 0:
                start = min(start, ss[i - 1])
                stop = max(stop, es[i - 1])
            if i < L - 1:
                start = min(start, ss[i + 1])
                stop = max(stop, es[i + 1])
            ss_.append(start)
            es_.append(stop)
        return ss_, es_

    starts = [x - th for x, y in line]
    stops = [x + th for x, y in line]
    for _ in range(repeat):
        starts, stops = expand(starts, stops)

    for i, (x, y) in enumerate(line):
        y = int(y)
        if y < 0 or y >= H:
            continue
        start = starts[i]
        stop = stops[i]
        start = max(int(np.floor(start)), 0)
        stop = min(int(np.ceil(stop)), W - 1)
        visit[y, start:stop + 1] += value
        if i == 0 and y > 0:
            visit[y - 1, start:stop + 1] += value
        if i == L - 1 and y < H - 1:
            visit[y + 1, start:stop + 1] += value
    return visit


def check_k(dx_cur, dx_last, th=1):
    if abs(dx_last) <= th:
        return abs(dx_cur) <= th * 2
    else:
        if dx_last > 0:
            return dx_last / 2 <= dx_cur <= dx_last * 4
        else:
            return dx_last * 4 <= dx_cur <= dx_last / 2


def check_len(points, th_len, th_kps_min, th_kps_max):
    if len(points) < th_kps_min:
        return False
    else:
        if len(points) >= th_kps_max:
            return True
        else:
            line_len = np.linalg.norm(points[:-1] - points[1:], 2, -1).sum()
            return line_len >= th_len


def get_mask_thr_by_pos(cur_pos, sigma=0.75, max_thr=0.9):
    thr = 0
    if max_thr > 0:
        cur_x, cur_y = cur_pos
        assert cur_y <= 1 and cur_x <= 1, "cur pose should be norm into [0, 1]"
        thr = np.exp(-((cur_x - 0.5) * (cur_x - 0.5) + (1 - cur_y) *
                       (1 - cur_y)) / (2 * sigma * sigma)) * max_thr
    return thr


def remap_property_ms(line_type):
    if line_type < 5:
        line_ms = 0
    if 5 <= line_type < 7:
        line_ms = line_type - 4
        line_type = 2
    elif 7 <= line_type < 13:
        line_type -= 2
        line_ms = 0
    else:
        line_ms = 255
    return line_type, line_ms


class CondLanePostProcessor(object):

    def __init__(
        self,
        key_points_size_thr=35,
        side_key_points_size_thr=35,
        range_method="len_pred",
        cls_method="row_att",
        cls_points_size=0,
        with_breakpoints=True,
        breakpoints_thr=0.4,
        range_thr=0,
        range_continue_tole=0,
        use_offset=True,
        nms_thr=0.0,
        categories={},
        laneline_indices={},
        roadside_indices={},
        view_distance=1,
        inv_view_distance=1,
        hm_16x=False,
        mask_upsample_rate=1,
        num_row_ref_point=1,
        length_scale=None,
        field_scale=None,
        overlap_thr=3,  # 0: off;
        num_forward_point=2,
        th_len=30,
        th_dp_radius_laneline=2,
        th_dp_radius_roadside=9,
        with_hm=True,
        mask_sigma=0.75,
        mask_max_thr=0,
        pos_fix=[0, 0.5],
        check_k=1,
        with_mask_thr=False,
        alone_ms=False,
        maskAct="sigmoid",
        v4_simplify=False,
        mask_ignores=0,
        len_kernel=5,
        **kwargs,
    ):
        self.with_hm = with_hm
        self.th_len = th_len
        self.th_dp_radius_laneline = th_dp_radius_laneline
        self.th_dp_radius_roadside = th_dp_radius_roadside
        self.key_points_size_thr = key_points_size_thr
        self.side_key_points_size_thr = side_key_points_size_thr
        self.breakpoints_thr = breakpoints_thr
        self.use_offset = use_offset
        self.range_thr = range_thr
        self.range_continue_tole = range_continue_tole
        assert range_method in ["vrange", "len_pred", "field"]
        self.range_method = range_method
        assert cls_method in ["start_point", "mask", "row_att"]
        self.cls_method = cls_method
        self.cls_points_size = cls_points_size
        self.with_breakpoints = with_breakpoints
        self.momentum = 0.4
        self.nms_thr = nms_thr
        self.categories = categories
        self.view_distance = view_distance
        self.inv_view_distance = inv_view_distance
        self.num_forward_point = num_forward_point
        self.hm_16x = hm_16x
        self.mask_upsample_rate = mask_upsample_rate
        self.length_scale = length_scale    # None: sigmoid; 0: ReLU6 / 6.0; else: regress
        self.field_scale = field_scale
        self.overlap_thr = overlap_thr if overlap_thr > 0 else np.inf
        self.mask_sigma = mask_sigma
        self.mask_max_thr = mask_max_thr
        self.categories.split = [
            len(self.categories.attr[k]) for k in self.categories.group
        ]
        self.categories.split = np.cumsum(self.categories.split)[:-1]
        if len(laneline_indices) <= 0:
            laneline_indices = dict(
                length=(27, 28),
                field=(29, 30),
                offset=(30, 31),
            )
        self.laneline_indices = laneline_indices
        if len(roadside_indices) <= 0:
            roadside_indices = dict(
                length=(28, 29),
                field=(31, 32),
                offset=(32, 33),
            )
        self.roadside_indices = roadside_indices
        self.num_row_ref_point = num_row_ref_point
        self.pos_fix = pos_fix
        self.check_k = check_k
        self.with_mask_thr = with_mask_thr
        self.alone_ms = alone_ms
        self.maskAct = sigmoid if maskAct == "sigmoid" else Hsigmoid
        self.v4_simplify = v4_simplify
        self.mask_ignores = mask_ignores
        self.len_kernel = len_kernel

    def gather_bk(self, break_mask, points):
        bk_vec = sigmoid(break_mask)
        bk_vec[:-1] *= bk_vec[:-1] >= bk_vec[1:]
        bk_vec[1:] *= bk_vec[:-1] <= bk_vec[1:]
        bk = points[bk_vec[points[:, 1].astype(np.int)] > self.breakpoints_thr]
        return bk

    def get_range(self, ranges):
        max_rows = len(ranges)
        min_idx = max_idx = None
        for row_idx, valid in enumerate(ranges):
            if valid:
                min_idx = row_idx - 1
                break
        for row_idx, valid in enumerate(ranges[::-1]):
            if valid:
                max_idx = len(ranges) - row_idx
                break
        if max_idx is not None:
            max_idx = min(max_rows - 1, max_idx)
        if min_idx is not None:
            min_idx = max(0, min_idx)
            min_idx_ = max_idx
            if self.range_continue_tole > 0:
                tolerance = self.range_continue_tole
                for valid in ranges[max_idx:min_idx:-1]:
                    if valid:
                        tolerance = self.range_continue_tole
                    else:
                        if tolerance > 0:
                            tolerance -= 1
                        else:
                            break
                    min_idx_ -= 1
                min_idx = min_idx_
        return min_idx, max_idx

    def process_v1(
        self,
        proposal_indices,
        cls_score,
        loc_map,
        off_map,
        property_score,
        vertical_range,
        break_mask=None,
    ):
        # ti not support reduce sum
        lanes = []
        M, H, W = off_map.shape
        for m in range(M):
            pn, pc, ph, pw = proposal_indices[m]
            if self.range_thr > 0:
                vrange = softmax(vertical_range[m])[:, 1] >= self.range_thr
            else:
                vrange = np.argmax(vertical_range[m], 1)
            y_min, y_max = self.get_range(vrange)
            if y_max is None or y_min is None:
                continue
            if y_max + 1 - y_min <= self.key_points_size_thr:
                continue

            y = np.arange(y_min, y_max + 1)
            x = loc_map[m, y].astype(np.int)
            off = off_map[m, y, x] if self.use_offset else 0.5
            points = np.stack((x + off, y), -1)

            attr = property_score[m]

            lane = dict(
                image_id=pn,
                label_id=pc,
                key_points=points,
                confidence=float(cls_score[m]),
                attribute=self.attr_group(attr, False),
            )

            if self.with_breakpoints and pc == 0 and break_mask is not None:
                lane["break_points"] = self.gather_bk(break_mask[pn], points)

            lanes.append(lane)
        return lanes

    def len_pred(self, start_row, len_range):
        start_row = start_row * 2 + 1
        remain_length = len_range[start_row]
        cur_row = start_row
        while remain_length > 1.0 and cur_row > 0:
            remain_length = self.momentum * (remain_length - 1) + (
                1 - self.momentum) * len_range[cur_row]
            cur_row -= 1
        return cur_row, start_row

    def process_v2(
        self,
        proposal_indices,
        cls_score,
        aux_mask,
        loc_map,
        off_map,
        break_mask=None,
    ):
        # ti not support reduce sum
        if loc_map.shape[1] > 1:
            loc_map = loc_map.sum(1, keepdims=True)
            off_map = off_map.sum(1, keepdims=True)
            loc_map = softmax(loc_map)
        lanes = []
        M, _, H, W = loc_map.shape
        for m in range(M):
            pn, pc, ph, pw = proposal_indices[m]
            # [30, H] or [29, H]
            aux = (loc_map[m] * aux_mask[pn]).sum(-1)
            y_min, y_max = self.len_pred(ph, sigmoid(aux[27:29][pc]) * H)
            if y_max + 1 - y_min <= self.key_points_size_thr:
                continue

            y = np.arange(y_min, y_max + 1)
            x = (loc_map[m, 0, y] * np.arange(W)).sum(-1).astype(np.int)
            off = off_map[m, 0, y, x] if self.use_offset else 0.5
            points = np.stack((x + off, y), -1)

            if self.cls_method == "row_att":
                if 0 < self.cls_points_size < len(y):
                    y_cls = y[-1 * self.cls_points_size:]
                else:
                    y_cls = y
                attr = aux[2:27, y_cls].mean(-1)
            elif self.cls_method == "mask":
                idx = points.astype(np.int)
                inbound = (idx[:, 0] < W) & (idx[:, 1] < H)
                idx = idx[inbound]
                attr = aux_mask[pn, 2:27, idx[:, 0], idx[:, 1]].mean(-1)

            lane = dict(
                image_id=pn,
                label_id=pc,
                key_points=points,
                confidence=float(cls_score[m]),
                attribute=self.attr_group(attr),
            )
            if self.with_breakpoints and pc == 0:
                if break_mask is None and aux.shape[0] > 29:
                    break_mask = aux[29]
                if break_mask is not None:
                    lane["break_points"] = self.gather_bk(break_mask, points)

            lanes.append(lane)
        return lanes

    def field_pass(
        self,
        start,
        len_field,
        vec_field,
        off_field,
        attr_field,
        bk=None,
    ):
        # TODO detect breakpoints
        H, W = len_field.shape[-2:]
        points = []
        attr_scores = []
        x_idx, y_idx = start
        if self.hm_16x:
            x_idx, y_idx = x_idx * 2, y_idx * 2 + 1
        if y_idx >= 1 and False:
            next_x = vec_field[y_idx, x_idx] + x_idx + 0.5
            cur_x = next_x - vec_field[y_idx - 1, int(next_x)]
            x_idx = int(cur_x)
        else:
            cur_x = x_idx + 0.5  # + off_field[y_idx, x_idx]
        y_idx = min(y_idx, H - 1)
        x_idx = min(x_idx, W - 1)
        length = len_field[y_idx, x_idx]
        points.append([cur_x, y_idx + 0.5])
        length = H
        while length >= 1.0:
            x_idx, y_idx = int(np.clip(x_idx, 0,
                                       W - 1)), int(np.clip(y_idx, 0, H - 1))
            attr_scores.append(attr_field[:, y_idx, x_idx])
            dx, dy = vec_field[y_idx, x_idx], -1

            cur_len = len_field[y_idx, x_idx]
            length -= 1
            if length == H - 2:
                length = cur_len
            elif length < H - 2:
                length = self.momentum * length + (1 - self.momentum) * cur_len

            cur_x, cur_y = x_idx + dx + 0.5, y_idx + 0.5 + dy
            x_idx, y_idx = int(cur_x), int(cur_y)
            if 0 <= x_idx < W:
                cur_x = (off_field[y_idx, x_idx] + x_idx + 0.5 + cur_x) / 2
                x_idx = int(cur_x)
            points.append([cur_x, cur_y])
        if len(points) >= 8:
            for j in range(3, -1, -1):
                points[j][0] = 2 * points[j + 1][0] - points[j + 2][0]
        points = np.array(points)

        return points, attr_scores

    def parse_indices(self, x, indices):
        ret = {}
        for k, start in indices.items():
            if isinstance(start, (list, tuple)):
                start, stop = start
            else:
                stop = start + 1
            if start >= x.shape[0]:
                continue
            ret[k] = x[start:stop].squeeze()
        return ret

    def process_v3(
        self,
        proposal_indices,
        cls_score,
        aux_mask,
    ):
        # TODO new process to merge
        # [N, 33, H, W] / [N, 34, H, W]
        N, C, H, W = aux_mask.shape
        lanes = []
        for (pn, pc, ph, pw), s in zip(proposal_indices, cls_score):
            bk = None
            if self.with_breakpoints and C > 33 and pc == 0:
                bk = aux_mask[pn, 33]
            start = (pw, ph)
            # start = (pw * 2 + 1, ph * 2 + 1)
            field = self.parse_indices(
                aux_mask[pn],
                self.laneline_indices if pc == 0 else self.roadside_indices,
            )
            points, attr_scores = self.field_pass(
                start,
                len_field=sigmoid(field["length"]) * H,
                vec_field=field["field"],
                off_field=field["offset"],
                attr_field=aux_mask[pn, 2:27],
                bk=bk,
            )
            if len(points) <= self.key_points_size_thr:
                continue
            attr_scores = np.stack(attr_scores).mean(0)
            lane = dict(
                image_id=pn,
                label_id=pc,
                key_points=points,
                confidence=float(s),
                attribute=self.attr_group(attr_scores),
            )
            lanes.append(lane)
        return lanes

    def field_pass_v4(
        self,
        start,
        start_point_pos,
        proposal_type,
        len_field,
        inv_len_field,
        vec_field,
        inv_vec_field,
        attr_field,
        line_mask,
        bk=None,
        dp=None,
        pc=0,
        img_name="",
    ):
        H, W = len_field.shape[-2:]
        num_row_point = W * self.num_row_ref_point
        vec_field = vec_field.reshape(-1, self.num_row_ref_point, H,
                                      W).transpose(0, 2, 3, 1).reshape(
                                          -1, H, num_row_point)
        if inv_vec_field is not None:
            inv_vec_field = inv_vec_field.reshape(
                -1, self.num_row_ref_point, H,
                W).transpose(0, 2, 3, 1).reshape(-1, H, num_row_point)

        overlap_points = 0
        weights = [1, 0.8, 0.6, 0.4, 0.2]
        idxs = []
        points = []
        line_pos = np.zeros(shape=[H], dtype=np.float32)
        line_vector_weight = np.zeros(shape=[H], dtype=np.float32)

        # todo(liuhao1): simple;
        cur_x, cur_y = start_point_pos
        x_idx, y_idx = int(cur_x * self.num_row_ref_point), int(cur_y)
        # move to next row to refine the start point position
        for _ in range(self.num_forward_point):
            ref_x = x_idx / self.num_row_ref_point + 0.5 / self.num_row_ref_point
            dx = vec_field[0, y_idx, x_idx]
            if self.field_scale is not None:
                dx *= (W / self.field_scale)
            cur_x = ref_x + 0.5 + dx
            cur_y = y_idx - 1
            x_idx, y_idx = int(
                np.clip(cur_x * self.num_row_ref_point, 0,
                        num_row_point - 1)), y_idx - 1
            if dp is not None:
                if dp[y_idx, x_idx] >= 1:
                    return None, None
        points.append([cur_x, cur_y])
        if dp is not None:
            start_x, start_y = cur_x, cur_y
            start_x_idx, start_y_idx = x_idx, y_idx
            overlap_points += dp[y_idx, x_idx]
            # the grid have been visited, skip it
            if overlap_points >= 1:
                return None, None
            idxs.append([x_idx, y_idx])

        mem_length = H
        cur_len = mem_length
        cnt = 0
        accu_score = 0
        while mem_length >= 2 and cur_len >= 2:
            x_idx = int(np.clip(x_idx, 0, num_row_point - 1))
            ref_x = x_idx / self.num_row_ref_point + 0.5 / self.num_row_ref_point
            y_idx = int(np.clip(y_idx, 0, H - 1))
            for j in range(vec_field.shape[0]):
                if y_idx - j - 1 >= 0:
                    w = weights[j]
                    dx = vec_field[j, y_idx, x_idx]
                    if self.field_scale is not None:
                        dx *= (W / self.field_scale)
                    line_pos[y_idx - j - 1] += (dx + ref_x) * w
                    line_vector_weight[y_idx - j - 1] += w
            cur_len = len_field[y_idx, int(np.clip(ref_x, 0, W - 1))]
            if cur_len < 2:
                k = self.len_kernel
                if len(points) > 1:
                    k = max(k, int(abs(points[-1][0] - points[-2][0]) * self.len_kernel))
                cur_len = gather_row(len_field[y_idx],
                                     int(np.clip(ref_x, 0, W - 1)), k)
            mem_length -= 1
            if mem_length == H - 4:
                mem_length = cur_len
            elif mem_length < H - 4:
                mem_length = self.momentum * mem_length + (
                    1 - self.momentum) * cur_len
            if y_idx - 1 >= 0:
                cur_x = line_pos[y_idx - 1] / line_vector_weight[y_idx - 1]
            else:
                break
            # (self.view_distance if cnt >= self.view_distance else cnt)
            cur_y = y_idx - 1
            x_idx = np.clip(int(cur_x * self.num_row_ref_point), 0,
                            W * self.num_row_ref_point - 1)
            y_idx = np.clip(int(cur_y), 0, H - 1)
            mask_score = line_mask[
                y_idx,
                int(np.clip(x_idx / self.num_row_ref_point, 0, W - 1))]
            if dp is not None:
                overlap_score = dp[y_idx, x_idx]
                overlap_points += overlap_score
                # drop the line iff overlapping with previous line( which has higher confidence) and remainded_length < 3
                # if (mem_length > 3 or len(points) < 6) and overlap_points >= self.overlap_thr:
                #     return None, None
                # stop when overlap with previous line( which has higher confidence)
                if overlap_points >= self.overlap_thr:
                    break
                idxs.append([x_idx, y_idx])
            # stop when reaching the point with very low score
            if self.with_mask_thr:
                #mask_thr = get_mask_thr_by_h(y_idx / H)
                mask_thr = get_mask_thr_by_pos(
                    (float(x_idx) / num_row_point, float(y_idx) / H),
                    self.mask_sigma, self.mask_max_thr)
                if pc == 0 and mask_score < mask_thr:
                    break
            # drop the point with low score
            #if mask_score > 0.5:
            accu_score += mask_score
            points.append([cur_x, cur_y])
            cnt += 1

        if y_idx < H - 1 and inv_len_field is not None:
            inv_idxs = idxs[::-1]
            inv_points = points[::-1]
            cur_x, cur_y = start_x, start_y
            x_idx, y_idx = start_x_idx, start_y_idx
            length = inv_len_field[y_idx, int(np.clip(cur_x, 0, W - 1))]
            if length < 2:
                k = self.len_kernel
                if len(inv_points) > 1:
                    k = max(k, int(abs(inv_points[-1][0] - inv_points[-2][0]) * self.len_kernel))
                length = gather_row(inv_len_field[y_idx],
                                    int(np.clip(cur_x, 0, W - 1)), k)
            while length >= 1.0 and y_idx < H - 1 and 0 <= x_idx < num_row_point:
                x_idx = int(np.clip(x_idx, 0, num_row_point - 1))
                ref_x = x_idx / self.num_row_ref_point + 0.5 / self.num_row_ref_point
                y_idx = int(np.clip(y_idx, 0, H - 1))
                for j in range(vec_field.shape[0]):
                    if y_idx + j + 1 < H:
                        w = weights[j]
                        dx = inv_vec_field[j, y_idx, x_idx]
                        if self.field_scale is not None:
                            dx *= (W / self.field_scale)
                        line_pos[y_idx + j + 1] += (dx + ref_x) * w
                        line_vector_weight[y_idx + j + 1] += w
                cur_len = inv_len_field[y_idx, int(np.clip(ref_x, 0, W - 1))]
                if cur_len < 2:
                    k = self.len_kernel
                    if len(inv_points) > 1:
                        k = max(k, int(abs(inv_points[-1][0] - inv_points[-2][0]) * self.len_kernel))
                    cur_len = gather_row(inv_len_field[y_idx],
                                         int(np.clip(ref_x, 0, W - 1)), k)
                length -= 1
                length = self.momentum * length + (1 - self.momentum) * cur_len
                cur_x = line_pos[y_idx + 1] / line_vector_weight[y_idx + 1]
                cur_y = y_idx + 1
                if cur_x <= 1 or cur_y <= 1 or cur_x >= W - 1 or cur_y >= H - 1:
                    break
                if self.check_k > 0 and len(inv_points) > 2:
                    k_last = inv_points[-1][0] - inv_points[-2][0]
                    k_cur = cur_x - inv_points[-1][0]
                    if not check_k(k_cur, k_last, self.check_k):
                        break
                x_idx, y_idx = int(cur_x * self.num_row_ref_point), y_idx + 1
                mask_score = line_mask[
                    y_idx,
                    int(np.clip(x_idx / self.num_row_ref_point, 0, W - 1))]
                if self.with_mask_thr:
                    #mask_thr = get_mask_thr_by_h(y_idx / H)
                    mask_thr = get_mask_thr_by_pos(
                        (float(x_idx) / num_row_point, float(y_idx) / H),
                        self.mask_sigma, self.mask_max_thr)
                    if pc == 0 and mask_score < mask_thr:
                        break
                cnt += 1
                accu_score += mask_score
                if dp is not None:
                    if x_idx >= num_row_point:
                        break
                    overlap_points += dp[y_idx, x_idx]
                    if dp[y_idx,
                          x_idx] >= 2 or overlap_points >= self.overlap_thr:
                        break
                    if overlap_points >= 4:
                        return None, None
                    inv_idxs.append([x_idx, y_idx])
                inv_points.append([cur_x, cur_y])
            points = inv_points[::-1]
            idxs = inv_idxs[::-1]
        points = np.array(points)
        points = points + self.pos_fix
        kp_thr = self.key_points_size_thr if pc == 0 else self.side_key_points_size_thr
        if not check_len(points, self.th_len, 5, kp_thr):
            return None, None
        th_dp_radius = self.th_dp_radius_laneline if pc == 0 else self.th_dp_radius_roadside
        # update_overlap(dp, idxs, r=th_dp_radius)
        update_visit(dp, idxs, self.overlap_thr, th_dp_radius, th_dp_radius)
        attr_scores = []
        for x_, y_ in points:
            x_ = int(x_ / self.mask_upsample_rate)
            y_ = int(y_ / self.mask_upsample_rate)
            if x_ < 0 or x_ >= attr_field.shape[2] or y_ >= attr_field.shape[1]:
                continue
            attr_scores.append(attr_field[:, y_, x_])
        return points, attr_scores

    def field_pass_v4_simp(
            self,
            start,
            start_point_pos,
            proposal_type,
            len_field,
            inv_len_field,
            vec_field,
            inv_vec_field,
            attr_field,
            line_mask,
            bk=None,
            dp=None,
            pc=0,
            kernel=3,
            img_name="",
    ):
        H, W = vec_field.shape[-2:]
        num_row_point = W * self.num_row_ref_point
        vec_field = vec_field.reshape(-1, self.num_row_ref_point, H,
                                      W).transpose(0, 2, 3, 1).reshape(
            -1, H, num_row_point)
        if inv_vec_field is not None:
            inv_vec_field = inv_vec_field.reshape(
                -1, self.num_row_ref_point, H,
                W).transpose(0, 2, 3, 1).reshape(-1, H, num_row_point)

        weights = [1, 0.8, 0.6, 0.4, 0.2]
        idxs = []
        points = []
        line_pos = np.zeros(shape=[H], dtype=np.float32)
        line_vector_weight = np.zeros(shape=[H], dtype=np.float32)

        start_x, start_y = start_point_pos
        start_x_idx = int(np.clip(start_x * self.num_row_ref_point, 0, num_row_point - 1))
        start_y_idx = int(np.clip(start_y, 0, H - 1))

        points.append([start_x, start_y])  # todo(liuhao1): 为何不是idx，没有用超分？
        if dp is not None:
            # the grid have been visited, skip it
            if dp[start_y_idx, start_x_idx] >= 1:
                return None, None
            idxs.append([start_x_idx, start_y_idx])

        mem_length = H
        mem_score = 1
        forward_go = True
        x_idx, y_idx = start_x_idx, start_y_idx
        while forward_go:
            forward_go = False
            ref_x = np.clip((x_idx + 0.5) / self.num_row_ref_point, 0, W - 1)

            # length (only for roadside)
            if len_field is not None:
                mem_length -= 1
                cur_len = len_field[y_idx, int(ref_x)]
                if cur_len < 2:
                    cur_len = gather_row(len_field[y_idx], int(ref_x), kernel)
                if mem_length == H - 4:  # todo(liuhao1): why 4?
                    mem_length = cur_len
                elif mem_length < H - 4:
                    mem_length = self.momentum * mem_length + (
                            1 - self.momentum) * cur_len
            else:
                mem_length = 0
                cur_len = 0

            # next position
            for j in range(vec_field.shape[0]):
                if y_idx - j - 1 >= 0:
                    w = weights[j]
                    dx = vec_field[j, y_idx, x_idx]
                    if self.field_scale is not None:
                        dx *= (W / self.field_scale)
                    line_pos[y_idx - j - 1] += (dx + ref_x) * w
                    line_vector_weight[y_idx - j - 1] += w
            cur_y = y_idx - 1
            if cur_y < 0 or cur_y > H - 1:
                break
            cur_x = line_pos[y_idx - 1] / line_vector_weight[y_idx - 1]
            if cur_x < 0 or cur_x > W - 1:
                break
            x_idx = int(cur_x * self.num_row_ref_point)
            y_idx = int(cur_y)
            if x_idx < 0 or y_idx < 0 or x_idx > num_row_point - 1 or y_idx > H - 1:
                break
            if dp is not None:
                if dp[y_idx, x_idx] >= 1:
                    break
                idxs.append([x_idx, y_idx])

            # next mask score (only for laneline)
            if pc == 0:
                mask_score = line_mask[  # todo(liuhao1): mask map also super resolution
                    y_idx,
                    int(x_idx / self.num_row_ref_point)]
                mem_score = self.momentum * mem_score + (1 - self.momentum) * mask_score
                # if self.with_mask_thr:  # deprecated
                #     mask_thr = get_mask_thr_by_pos(
                #         (float(x_idx) / num_row_point, float(y_idx) / H),
                #         self.mask_sigma, self.mask_max_thr)
                #     if mask_score < mask_thr:
                #         break
            else:
                mem_score = 0
            points.append([cur_x, cur_y])
            if 0 < y_idx < H - 1 and 0 < x_idx < num_row_point - 1:
                if (pc == 1 and mem_length >= 2 and cur_len >= 2) or (pc == 0 and mem_score >= 0.5):
                    forward_go = True

        if pc == 0 and proposal_type == "MASK_PROPOSAL" and inv_vec_field is not None:
            inv_idxs = idxs[::-1]
            inv_points = points[::-1]
            # inv_mem_len = inv_len_field[int(start_y), int(start_x)]
            # if inv_mem_len < 1:
            #     inv_mem_len = gather_row(inv_len_field[int(start_y)], int(start_x), kernel)
            inv_go = True
            mem_score = 1
            x_idx, y_idx = start_x_idx, start_y_idx
            while inv_go:  # length >= 1.0 and y_idx < H - 1 and 0 <= x_idx < num_row_point:
                inv_go = False
                ref_x = np.clip((x_idx + 0.5) / self.num_row_ref_point, 0, W - 1)

                # inv length
                # inv_mem_len -= 1
                # inv_cur_len = inv_len_field[y_idx, int(ref_x)]
                # if inv_cur_len < 1:
                #     inv_cur_len = gather_row(inv_len_field[y_idx], int(ref_x), kernel)
                # inv_mem_len = self.momentum * inv_mem_len + (1 - self.momentum) * inv_cur_len

                # next position
                for j in range(vec_field.shape[0]):  # todo(liuhao1): inv_vec_field
                    if y_idx + j + 1 < H:
                        w = weights[j]
                        dx = inv_vec_field[j, y_idx, x_idx]
                        if self.field_scale is not None:
                            dx *= (W / self.field_scale)
                        line_pos[y_idx + j + 1] += (dx + ref_x) * w
                        line_vector_weight[y_idx + j + 1] += w
                cur_y = y_idx + 1
                if cur_y < 0 or cur_y > H -1:
                    break
                cur_x = line_pos[y_idx + 1] / line_vector_weight[y_idx + 1]
                if cur_x < 0 or cur_x > W - 1:
                    break
                # if self.check_k > 0 and len(inv_points) > 2:
                #     k_last = inv_points[-1][0] - inv_points[-2][0]
                #     k_cur = cur_x - inv_points[-1][0]
                #     if not check_k(k_cur, k_last, self.check_k):
                #         break
                x_idx, y_idx = int(cur_x * self.num_row_ref_point), int(cur_y)
                if x_idx < 0 or y_idx < 0 or x_idx > num_row_point - 1 or y_idx > H - 1:
                    break
                if dp is not None:
                    if dp[y_idx, x_idx] >= 1:
                        break
                    inv_idxs.append([x_idx, y_idx])

                # next mask score
                mask_score = line_mask[
                    y_idx,
                    int(x_idx / self.num_row_ref_point)]
                mem_score = self.momentum * mem_score + (1 - self.momentum) * mask_score
                # if self.with_mask_thr:
                #     mask_thr = get_mask_thr_by_pos(
                #         (float(x_idx) / num_row_point, float(y_idx) / H),
                #         self.mask_sigma, self.mask_max_thr)
                #     if mask_score < mask_thr:
                #         break
                inv_points.append([cur_x, cur_y])
                if 0 < y_idx < H - 1 and 0 < x_idx < num_row_point - 1:
                    if mem_score > 0.5:
                        inv_go = True

            points = inv_points[::-1]
            idxs = inv_idxs[::-1]
        points = np.array(points)
        points = points + self.pos_fix
        kp_thr = self.key_points_size_thr if pc == 0 else self.side_key_points_size_thr
        if not check_len(points, self.th_len, 6, kp_thr):
            return None, None
        th_dp_radius = self.th_dp_radius_laneline if pc == 0 else self.th_dp_radius_roadside
        # update_overlap(dp, idxs, r=th_dp_radius)
        update_visit(dp, idxs, self.overlap_thr, th_dp_radius, th_dp_radius)
        attr_scores = []
        for x_, y_ in points:
            x_ = int(x_ / self.mask_upsample_rate)
            y_ = int(y_ / self.mask_upsample_rate)
            if x_ < 0 or x_ >= attr_field.shape[2] or y_ >= attr_field.shape[1]:
                continue
            attr_scores.append(attr_field[:, y_, x_])
        return points, attr_scores

    def scale_length(self, x):
        if x is None:
            return x
        H = x.shape[-2]
        if self.length_scale is None:
            x = sigmoid(x) * H
        elif self.length_scale > 0:
            x = np.clip(x * (H / self.length_scale), 0, H)
        else:
            x = Hsigmoid(x) * H
        return x

    def process_v4(
        self,
        proposal_indices,
        cls_score,
        output,
    ):
        lines = {}
        H, W = output["laneline_field"].shape[2:]

        # proposals from heatmap
        nms_proposal_indices = {}
        if proposal_indices is not None and self.with_hm:
            _, _, hm_h, hm_w = output["proposal_offset"].shape
            proposal_hm_ratio = H / hm_h
            for (pn, pc, ph, pw), s in zip(proposal_indices, cls_score):
                off_x, off_y = sigmoid(output["proposal_offset"][
                    pn, pc * 2:pc * 2 + 2, ph, pw]) * 2 - 1
                off_x, off_y = off_x * hm_w, off_y * hm_h
                start_x, start_y = np.clip(pw + 0.5 + off_x, 0, hm_w -1), np.clip(ph + 0.5 + off_y, 0, hm_h - 1)
                img_id = (pn, pc)
                if img_id not in nms_proposal_indices:
                    nms_proposal_indices[img_id] = {}
                proposal = (int(start_x), int(start_y))
                if proposal not in nms_proposal_indices[img_id]:
                    nms_proposal_indices[img_id][proposal] = (s, 1, [[
                        start_x * proposal_hm_ratio,
                        start_y * proposal_hm_ratio
                    ]])
                else:
                    score, cnt, start_points = nms_proposal_indices[img_id][
                        proposal]
                    start_points.append([
                        start_x * proposal_hm_ratio,
                        start_y * proposal_hm_ratio
                    ])
                    nms_proposal_indices[img_id][proposal] = (score + s,
                                                              cnt + 1,
                                                              start_points)

        # proposals from mask only for lane
        mask_proposal_indices = {}
        if 'mask_proposal_indices' in output:
            for (pn, pc, ph, pw), s in zip(output['mask_proposal_indices'],
                                           output['mask_scores']):
                if pc == 1: continue
                if ph >= H - self.mask_ignores: continue 
                img_id = (pn, pc)
                if img_id not in mask_proposal_indices:
                    mask_proposal_indices[(pn, pc)] = [
                        (s, 1, np.array([pw + 0.5, ph]), 'MASK_PROPOSAL')
                    ]
                else:
                    mask_proposal_indices[(pn, pc)].append(
                        (s, 1, np.array([pw + 0.5, ph]), 'MASK_PROPOSAL'))
        ordered_proposals_dict = {}
        for img_id in nms_proposal_indices:
            # img_id contains (img_idx, lane_type), so dp will be cleared when handling roadside and lane in single image
            ordered_proposals = [
                nms_proposal_indices[img_id][k]
                for k in nms_proposal_indices[img_id]
            ]
            ordered_proposals = sorted(
                [(v[0], v[1], np.array(v[2]).mean(axis=0), 'START_PROPOSAL')
                 for v in ordered_proposals],
                # key=cmp_to_key(lambda a, b: -a[0] + b[0] if a[2][1] == b[2][1] else -a[2][1] + b[2][1]))
                #key=cmp_to_key(lambda a, b: -a[0] + b[0]))
                key=cmp_to_key(lambda a, b: -a[0] * a[2][1] + b[0] * b[2][1]))
            ordered_proposals_dict[img_id] = ordered_proposals
        for img_id in mask_proposal_indices:
            mask_proposals = mask_proposal_indices[img_id]
            mask_ordered_proposals = sorted(
                mask_proposals,
                # key=lambda x: -x[2][1])
                key=cmp_to_key(lambda a, b: -a[0] * a[2][1] + b[0] * b[2][1]))
            ordered_proposals_dict[img_id] = ordered_proposals_dict.get(
                img_id, []) + mask_ordered_proposals
        for img_id, ordered_proposals in ordered_proposals_dict.items():
            pn, pc = img_id
            dp = np.zeros(
                shape=(H, W * self.num_row_ref_point),
                dtype=np.float32) if self.overlap_thr is not np.inf else None
            for k, proposal in enumerate(ordered_proposals):
                if proposal[-1] == "MASK_PROPOSAL":
                    #fastly filter mask proposal
                    x, y = proposal[2]
                    x, y = int(x * self.num_row_ref_point), int(y)
                    if dp[y, x] > 0: continue
                s, cnt, start_point_pos, proposal_type = proposal
                bk = None
                if self.with_breakpoints and "break_mask" in output:
                    bk = output["break_mask"]
                start = None
                # start = (pw * 2 + 1, ph * 2 + 1)
                linetype = ["laneline", "roadside"][pc]
                len_field = output[f"{linetype}_length"][pn][0] if f"{linetype}_length" in output else None
                inv_len_field = output[f"{linetype}_inv_length"][pn][0] if f"{linetype}_inv_length" in output else None
                vec_field = output[f"{linetype}_field"][pn] if f"{linetype}_field" in output else None
                inv_vec_field = output[f"{linetype}_inv_vector_field"][pn] if f"{linetype}_inv_vector_field" in output else None
                if self.v4_simplify:
                    points, attr_scores = self.field_pass_v4_simp(
                        start,
                        start_point_pos,
                        proposal_type,
                        len_field=self.scale_length(len_field),
                        inv_len_field=self.scale_length(inv_len_field),
                        vec_field=vec_field,
                        inv_vec_field=inv_vec_field,
                        attr_field=output["attributes"][pn],
                        line_mask=self.maskAct(output["line_mask"][pn][pc]),
                        bk=bk,
                        dp=dp if dp is not None else None,
                        pc=pc,
                        # img_name=output["img_name"][pn],
                    )
                else:
                    points, attr_scores = self.field_pass_v4(
                        start,
                        start_point_pos,
                        proposal_type,
                        len_field=self.scale_length(len_field),
                        inv_len_field=self.scale_length(inv_len_field),
                        vec_field=vec_field,
                        inv_vec_field=inv_vec_field,
                        attr_field=output["attributes"][pn],
                        line_mask=self.maskAct(output["line_mask"][pn][pc]),
                        bk=bk,
                        dp=dp if dp is not None else None,
                        pc=pc,
                        # img_name=output["img_name"][pn],
                    )
                # if point is None:     # 为了可视化起始点，sdk中point为None直接 continue
                #     continue
                lane = dict(
                    image_id=pn,
                    label_id=pc,
                    confidence=float(s),
                    proposal=list(proposal),
                    key_points=[],
                    attribute=[],
                )
                if points is not None:
                    #if len(attr_scores) >= self.key_points_size_thr:
                    #    attr_scores = attr_scores[self.key_points_size_thr //
                    #                              4:self.key_points_size_thr //
                    #                              4 * 3]
                    attr_scores = np.stack(attr_scores).mean(0)
                    lane.update({
                        "key_points": points,
                        "attribute": self.attr_group(attr_scores),
                    })
                if (pn, pc) not in lines:
                    lines[(pn, pc)] = []
                lines[(pn, pc)].append(lane)
        nms_lines = []
        for k in lines:
            # nms_lines.extend(self.line_nms(lines[k]))
            nms_lines.extend(lines[k])
        return nms_lines

    def line_nms(self, lines):
        new_lines = []
        for line in lines:
            line["key_points"] = np.array(
                sorted(line["key_points"], key=lambda x: x[1]))
            new_lines.append(line)
        new_lines = sorted(
            new_lines, key=lambda x: -len(x["key_points"]) * x["confidence"])

        def line_iou(pts1, pts2):
            if pts1[0][1] > pts2[0][1]:
                pts1, pts2 = pts2, pts1
            i = 0
            while i < len(pts1) and int(pts1[i][1]) < int(pts2[0][1]):
                i += 1
            total_point = i
            overlap_points = 0
            common_row = 0
            i = 0
            distance = 0
            while i + total_point < len(pts1) and i < len(pts2):  # \
                # and int(pts1[i + total_point][1]) == int(pts2[0][1]):
                common_row += 1
                diff = abs(pts1[i + total_point][0] - pts2[i][0])
                distance += diff
                if diff < 1.0:
                    overlap_points += 1
                i += 1
            total_point += max(len(pts1) - total_point, len(pts2))
            return overlap_points / min(
                len(pts1), len(pts2)), distance / (common_row + 0.00001)

        droped = [False] * len(new_lines)
        i = 0
        for i in range(len(new_lines)):
            if droped[i]: continue
            for j in range(i + 1, len(new_lines)):
                if droped[j]: continue
                iou, com_mean_dis = line_iou(new_lines[i]["key_points"],
                                             new_lines[j]["key_points"])
                if com_mean_dis < 2 or iou > 0.8:
                    droped[j] = True
        return [line for i, line in enumerate(new_lines) if not droped[i]]

    def __call__(self, proposal_indices, cls_score, output):
        if (proposal_indices is None or proposal_indices.shape[0] <= 0
            ) and 'mask_proposal_indices' not in output:
            return []
        if self.range_method == "vrange" and "vertical_range" in output:
            lanes = self.process_v1(
                proposal_indices,
                cls_score,
                output["loc_map"],
                output["off_map"],
                output["property_score"],
                output["vertical_range"],
                output.get("break_mask", None),
            )
        elif self.range_method == "len_pred" and "loc_map" in output:
            lanes = self.process_v2(
                proposal_indices,
                cls_score,
                output["aux_mask"],
                output["loc_map"],
                output["off_map"],
                output.get("break_mask", None),
            )
        else:
            if "aux_mask" not in output:
                lanes = self.process_v4(
                    proposal_indices,
                    cls_score,
                    output,
                )
            else:
                lanes = self.process_v3(
                    proposal_indices,
                    cls_score,
                    output["aux_mask"],
                )
        if self.nms_thr > 0:  # todo(liuhao1): not use now.
            roadsides = self.line_nms([_ for _ in lanes if _["label_id"] == 1])
            lanes = self.line_nms([_ for _ in lanes if _["label_id"] == 0])
            lanes = lanes + roadsides
        return lanes

    def attr_group(self, attr, with_softmax=True):
        attr = np.split(attr, self.categories.split)
        if with_softmax:
            attr = list(map(softmax, attr))
        return attr

    def fmt_attr(self, attr, flag):
        assert flag in ['lane', 'side']
        if len(attr) <= 0:
            return {}
        ret = {}
        for idx, cate in enumerate(self.categories.group):
            if flag in cate:
                ret[cate] = dict(zip(self.categories.attr[cate], attr[idx].tolist()))
        return ret

    def fmt_pts(self, pts, roi=None):
        if roi is not None:
            l = roi["left"]
            t = roi["top"]
            r = l + roi["width"]
            b = t + roi["height"]
            points = [
                dict(x=x, y=y) for x, y in pts if l <= x <= r and t <= y <= b
            ]
        else:
            points = [dict(x=x, y=y) for x, y in pts]
        return {
            "$kesonType": "array",
            "points": points,
        }

    def fmt_proposals(self, proposal=[]):
        if len(proposal) <= 0:
            return proposal
        return [
            float(proposal[0]), proposal[1], proposal[2].tolist(), proposal[3]
        ],

    def fmt_pred(self, lanes, roi, bk_roi, w, h, label, scene, camera_index,
                 downscale):
        lanes = fix_points(lanes, roi, w, h, downscale)
        if label == "laneline":
            preds = [
                dict(
                    label="LANELINE",
                    roi=roi,
                    bk_roi=bk_roi,
                    confidence=lanes[i]["confidence"],
                    key_points=self.fmt_pts(lanes[i]["key_points"]),
                    break_points=self.fmt_pts(lanes[i].get("break_points", []),
                                              bk_roi),
                    attribute=self.fmt_attr(lanes[i]["attribute"], "lane"),
                    scene=scene,
                    camera_index=camera_index,
                    proposal=self.fmt_proposals(lanes[i].get("proposal", [])),
                ) for i in range(len(lanes))
            ]
        else:
            preds = [
                dict(
                    label="ROADSIDE",
                    roi=roi,
                    confidence=lanes[i]["confidence"],
                    key_points=self.fmt_pts(lanes[i]["key_points"]),
                    attribute=self.fmt_attr(lanes[i]["attribute"], "side"),
                    scene=scene,
                    camera_index=camera_index,
                    proposal=self.fmt_proposals(lanes[i].get("proposal", [])),
                ) for i in range(len(lanes))
            ]
        return preds

    def fmt_gt(self, img_metas, roi, bk_roi, w, h, label, scene, camera_index):
        if len(img_metas) <= 0:
            return []
        gt_masks = img_metas["gt_masks"]
        gt_points = [
            np.array(p).reshape([-1, 2]) for p in img_metas["key_points"]
        ]
        gt_points = fix_points(gt_points, roi, w, h)

        if label == "laneline":
            gts = []
            for i in range(len(gt_points)):
                lane_type = int(gt_masks[i]["type"])
                attribute = dict(
                    lane_type=lane_type,
                    lane_color=int(gt_masks[i]["color"]),
                )
                if self.alone_ms:
                    lane_type, lane_ms = remap_property_ms(lane_type)
                    attribute["lane_type"] = lane_type
                    attribute["lane_ms"] = lane_ms
                gts.append(
                    dict(
                        label="LANELINE",
                        roi=roi,
                        bk_roi=bk_roi,
                        key_points=self.fmt_pts(gt_points[i]),
                        break_points=self.fmt_pts(
                            img_metas.get("break_points", []), bk_roi),
                        attribute=attribute,
                        scene=scene,
                        camera_index=camera_index,
                    )
                )
        else:
            gts = [
                dict(
                    label="ROADSIDE",
                    roi=roi,
                    key_points=self.fmt_pts(gt_points[i]),
                    attribute=dict(roadside=int(gt_masks[i]["type"])),
                    scene=scene,
                    camera_index=camera_index,
                ) for i in range(len(gt_points))
            ]
        return gts


def fix_points(lanes, roi, w, h, scale=1, off=True):
    ratio = [scale * roi["width"] / w, scale * roi["height"] / h]
    off = [roi["left"], roi["top"]] if off else 0

    def fn(points):
        if len(points) <= 0:
            return []
        return points * ratio + off

    for key in range(len(lanes)):
        if isinstance(lanes[key], dict):
            lanes[key]["key_points"] = fn(lanes[key]["key_points"])
            if "break_points" in lanes[key]:
                lanes[key]["break_points"] = fn(lanes[key]["break_points"])
            if "proposal" in lanes[key]:
                lanes[key]["proposal"][2] = fn(lanes[key]["proposal"][2])
        else:
            lanes[key] = fn(lanes[key])
    return lanes
