import torch
from mmcls.models import build_head
from mmcls.models.heads.base_head import BaseHead

from ..builder import MODELS


@MODELS.register_module()
class MultiTaskHeadWrapper(BaseHead):
    """Use MultiTaskHeadWrapper to assign head/loss to each attribute independantly.
    All attributes share one backbone.

    Args:
        attributes (list[dict]): List of attribute dict. Use `loss_weight` in each attribute dict to
            calculate total loss.

    Example:
        attributes = [
            dict(
                name='phone',
                classes=dict(none=0, phone_use=1, phone_call=2),
                type="LinearClsHead",
                head_cfg=dict(loss=dict(type='CrossEntropyLoss', loss_weight=1.0))),
            dict(
                name="umbrella",
                classes=dict(none=0, umbrella_use=1),
                type="LinearClsHead",
                head_cfg=dict(loss=dict(type='CrossEntropyLoss', loss_weight=1.0)))
            ]
    """

    def __init__(self,
                 attributes,
                 common_module=None,
                 init_cfg=None,
                 train_cfg=None,
                 test_cfg=None,
                 **kwargs):
        super(MultiTaskHeadWrapper, self).__init__(init_cfg=init_cfg)

        self.attr_names, self.label_names = [], []

        for attribute_cfg in attributes:
            attr_name = attribute_cfg['name']
            self.attr_names.append(attr_name)

            # if a head need multi labels, define in 'label_name'
            label_name = attribute_cfg.get('label_name', attr_name)
            self.label_names.append(label_name)

            head_name = self.get_head_name(attr_name)
            self.add_module(head_name, self._build_head(attribute_cfg, train_cfg, test_cfg))
        
        self.common_module = self._build_common_module(common_module)

    def _build_head(self, attribute_cfg, train_cfg=None, test_cfg=None):
        # if attribute is also a MultiTaskHeadWrapper, recursive build
        if self.__class__.__name__ in attribute_cfg.type:
            attribute_cfg.pop('type')
            return self.__class__(**attribute_cfg, train_cfg=train_cfg, test_cfg=test_cfg)

        head_cfg = dict(type=attribute_cfg['type'])

        # add leaf node's head_cfg
        if 'head_cfg' in attribute_cfg:
            head_cfg.update(attribute_cfg['head_cfg'])

        # add leaf node's train_cfg and test_cfg
        if train_cfg is not None:
            head_cfg.update(train_cfg=train_cfg.get(attribute_cfg['name'], None))
        if test_cfg is not None:
            head_cfg.update(test_cfg=test_cfg.get(attribute_cfg['name'], None))
        
        return build_head(head_cfg)

    def _build_common_module(self, common_module_cfg):
        if common_module_cfg is None:
            return None
        return MODELS.build(common_module_cfg)

    def init_weights(self):
        for attr_name in self.attr_names:
            head_name = self.get_head_name(attr_name)
            head = getattr(self, head_name)
            head.init_weights()

        if self.common_module is not None:
            self.common_module.init_weights()

    # gt_label: flatten dict
    # return flatten dict losses
    def forward_train(self, x, gt_label, **kwargs):
        losses = {}

        if self.common_module is not None:
            x = self.common_module(x)

        for attr_name, label_name in zip(self.attr_names, self.label_names):
            head_name = self.get_head_name(attr_name)
            head = getattr(self, head_name)
 
            if isinstance(head, MultiTaskHeadWrapper):
                attr_losses = head.forward_train(x, gt_label, **kwargs)
                losses.update(attr_losses)
            else:
                if isinstance(label_name, str):
                    label = gt_label[label_name]
                elif isinstance(label_name, (list, tuple)):
                    label = {name: gt_label[name] for name in label_name}
                attr_losses = head.forward_train(x, label, **kwargs)
                # flatten loss dict
                for loss_name, loss_value in attr_losses.items():
                    if isinstance(loss_value, dict):
                        for key, value in loss_value.items():
                            losses[f"{attr_name}_{loss_name}_{key}"] = value
                    else:
                        losses[f"{attr_name}_{loss_name}"] = loss_value
        # do not need to sum loss part
        # these will be added in mmcls/models/classifier.py _parse_losses
        return losses

    def get_head_name(self, attr_name):
        return attr_name + "_head"

    def forward_dummy(self, img):
        score_list = []

        if self.common_module is not None:
            img = self.common_module(img)

        for attr_name in self.attr_names:
            head_name = self.get_head_name(attr_name)
            head = getattr(self, head_name)
            if isinstance(head, MultiTaskHeadWrapper):
                result = head.forward_dummy(img)
            else:
                result = head.simple_test(img)
            if isinstance(result, list):
                result = torch.Tensor(result)
            score_list.append(result)
        return torch.cat(score_list, axis=1)

    # return flatten dict
    def simple_test(self, data, **kwargs):
        score_dict = {}

        if self.common_module is not None:
            data = self.common_module(data)

        for attr_name in self.attr_names:
            head_name = self.get_head_name(attr_name)
            head = getattr(self, head_name)
            result = head.simple_test(data, **kwargs)
            if isinstance(result, dict):
                for key, value in result.items():
                    # flatten without attr_name prefix
                    score_dict[key] = value
            else:
                score_dict[attr_name] = result
        return score_dict