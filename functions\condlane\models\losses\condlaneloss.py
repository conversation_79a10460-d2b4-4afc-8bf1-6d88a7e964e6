import ipdb
import torch
import torch.nn as nn
import torch.nn.functional as F
from mme.utils import get_root_logger

from pillar.models import MODELS

def Hsigmoid(z):
    return F.relu6(z + 3.0, inplace=True) / 6.0

def _neg_loss(pred, gt, channel_weights=None):
    """ Modified focal loss. Exactly the same as CornerNet.
      Runs faster and costs a little bit more memory
      Arguments:
      pred (batch x c x h x w)
      gt_regr (batch x c x h x w)
    """
    pos_inds = gt.eq(1).float()
    neg_inds = gt.lt(1).float()

    neg_weights = torch.pow(1 - gt, 4)

    loss = 0
#    print("=========================pos_inds.shape================:", pos_inds.shape)
#    print("=========================pred.shape================:", pred.shape)
    pos_loss = torch.log(pred) * torch.pow(1 - pred, 2) * pos_inds
    neg_loss = torch.log(1 - pred) * torch.pow(pred,
                                               2) * neg_weights * neg_inds

    num_pos = pos_inds.float().sum()
    if channel_weights is None:
        pos_loss = pos_loss.sum()
        neg_loss = neg_loss.sum()
    else:
        pos_loss_sum = 0
        neg_loss_sum = 0
        for i in range(len(channel_weights)):
            p = pos_loss[:, i, :, :].sum() * channel_weights[i]
            n = neg_loss[:, i, :, :].sum() * channel_weights[i]
            pos_loss_sum += p
            neg_loss_sum += n
        pos_loss = pos_loss_sum
        neg_loss = neg_loss_sum
    if num_pos > 0:
        loss = -(pos_loss + neg_loss) / num_pos
    else:
        # loss = loss - (pos_loss + neg_loss) / 256
        # loss = torch.tensor(0, dtype=torch.float32).to(pred.device)
        loss = -(neg_loss) / neg_inds.sum()
    return loss


class FocalLoss(nn.Module):
    """nn.Module warpper for focal loss"""

    def __init__(self):
        super(FocalLoss, self).__init__()
        self.neg_loss = _neg_loss

    def forward(self, out, target, weights_list=None):
        return self.neg_loss(out, target, weights_list)


class RegL1KpLoss(nn.Module):

    def __init__(self):
        super(RegL1KpLoss, self).__init__()

    def forward(self, output, target, mask):
        mask = mask.bool()
        loss = F.l1_loss(output[mask], target[mask])
        return loss


def compute_locations(shape, device):
    pos = torch.arange(0,
                       shape[-1],
                       step=1,
                       dtype=torch.float32,
                       device=device)
    pos = pos.reshape((1, 1, 1, -1))
    pos = pos.repeat(shape[0], shape[1], shape[2], 1)
    return pos


@torch.no_grad()
def cal_vertical_weight(gt_range, min=1.0, max=2.0):
    def linemap(mask):
        data = mask.float().cumsum(-1)
        data = torch.where(mask, data, torch.zeros_like(data))
        d_max = data.max(-1, True)[0]
        d_min = data.min(-1, True)[0]
        data = torch.where(d_max > d_min, (data - d_min) / (d_max - d_min),
                           torch.zeros_like(data))
        return data

    mask = gt_range.cumsum(-1) > 0
    weight = linemap(~mask) + linemap(mask.flip(-1)).flip(-1)
    weight = weight * (max - min) + min
    return weight


class FocalCrossLoss(nn.CrossEntropyLoss):
    """ Focal loss for classification tasks on imbalanced datasets """

    def __init__(self,
                 gamma=2,
                 weight=None,
                 ignore_index=-100,
                 reduction="mean"):
        if weight is not None:
            weight = torch.tensor(weight).float().cuda()
        super().__init__(weight=weight,
                         ignore_index=ignore_index,
                         reduction="none")
        self.reduction_ = reduction
        self.gamma = gamma

    def forward(self, input_, target, fg_mask=None, attr_bg_weight=-1):
        mask = target != self.ignore_index
        if not mask.any():
            return torch.tensor(1).float().cuda()
        target[~mask] = 0
        target = target.clone().long()
        cross_entropy = super().forward(input_, target)
        # Temporarily mask out ignore index to "0" for valid gather-indices input.
        # This won"t contribute final loss as the cross_entropy contribution
        # for these would be zero.
        p = F.softmax(input_, 1)
        target = target.unsqueeze(1)
        #print(f"Shape of p[1] before gather: {p.shape[1]}")
        #print(f"Max in target before gather: {torch.max(target)}")
        #print("===========================================")
        input_prob = torch.gather(p, 1, target)
        input_prob = input_prob.squeeze(1)
        loss = torch.pow(1 - input_prob, self.gamma) * cross_entropy

        if attr_bg_weight > 0:
            loss_pos = loss[fg_mask].mean()		#look here 4
            loss_neg = loss[~fg_mask].mean()
            loss_ = loss_pos + loss_neg * attr_bg_weight
            #if fg_mask.any():
            #    loss_pos = loss[fg_mask].mean()
            #else:
            #    loss_pos = 0
            #if ~fg_mask.any():
            #    loss_neg = loss[~fg_mask].mean()
            #else:
            #    loss_neg = 0
            #loss_ = loss_pos + loss_neg * attr_bg_weight
        else:
            if self.reduction_ in ["sum", "mean"]:
                loss_ = loss.sum()
                if self.reduction_ == "mean":
                    loss_ = loss_ / (mask.sum() + 1)
            else:
                raise NotImplementedError
        return loss_


class FocalBCEWithLogitsLoss(nn.BCEWithLogitsLoss):

    def __init__(self,
                 gamma=2,
                 weight=None,
                 size_average=None,
                 reduce=None,
                 reduction: str = "mean",
                 pos_weight=None) -> None:
        super().__init__(weight,
                         size_average,
                         reduce,
                         reduction="none",
                         pos_weight=pos_weight)
        self.reduction_ = reduction
        self.gamma = gamma

    def forward(self, input_, target, bg_weight=None):
        cross_entropy = super().forward(input_, target.float())
        p = input_.sigmoid().clamp(min=1e-4, max=1 - 1e-4)
        input_prob = torch.where(target, p, 1 - p)
        loss = torch.pow(1 - input_prob, self.gamma) * cross_entropy
        loss_ = 0
        if bg_weight is None:
            if self.reduction_ in ["sum", "mean"]:
                loss_ = loss.sum()
                if self.reduction_ == "mean":
                    loss_ = loss_ / (target.sum() + 1)
        else:
            for i, bg_w in enumerate(bg_weight):
                loss_i = loss[:, i, ...]
                target_i = target[:, i, ...]
                loss_i_ = 0
                if bg_w > 0:
                    fg_mask = target_i > 0
                    #if fg_mask.any():
                    #    loss_i_pos = loss_i[fg_mask].mean()
                    #else:
                    #    loss_i_pos = 0
                    #if ~fg_mask.any():
                    #    loss_i_neg = loss_i[~fg_mask].mean()
                    #else:
                    #    loss_i_neg = 0
                    #loss_i_ = loss_i_pos + loss_i_neg * bg_w
                    loss_pos = loss_i[fg_mask].mean()
                    loss_neg = loss_i[~fg_mask].mean()
                    loss_i_ = loss_pos + loss_neg * bg_w
                else:
                    if self.reduction_ in ["sum", "mean"]:
                        loss_i_ = loss_i.sum()
                        if self.reduction_ == "mean":
                            loss_i_ = loss_i_ / (target_i.sum() + 1)
                loss_ += loss_i_
        return loss_


class GroupFocalCrossLoss(nn.CrossEntropyLoss):

    def __init__(self, groups, weights, ignore_index):
        super().__init__()
        self.crit_ce = [
            FocalCrossLoss(weight=w, ignore_index=ignore_index)		#look here 3
            for w in weights
        ]
        self.groups = groups

    def forward(		# look here 2
        self,
        inputs,
        targets,
        masks=[None, None, None],
        attr_bg_weight=-1,
    ):
        if torch.is_tensor(inputs):
            inputs = torch.split(inputs, self.groups, 1)
        if torch.is_tensor(targets):
            targets = torch.chunk(targets, len(self.groups), 1)
            targets = [_.squeeze(1) for _ in targets]
        return [
            self.crit_ce[i](inputs[i], targets[i], masks[i], attr_bg_weight)
            for i in range(len(inputs))
        ]


@MODELS.register_module()
class CondLaneLoss(torch.nn.Module):

    def __init__(self, weights, property_group=[13, 6, 6], ignore_index=255):
        super(CondLaneLoss, self).__init__()
        self.crit = FocalLoss()
        self.crit_kp = RegL1KpLoss()
        # self.crit_bce = CrossEntropyLoss()
        self.crit_ce = nn.CrossEntropyLoss()
        self.crit_ce_attrs = GroupFocalCrossLoss(
            groups=property_group,
            weights=[
                weights.pop("lane_type_weight", None),
                weights.pop("lane_color_weight", None),
                weights.pop("roadside_weight", None),
            ],
            ignore_index=ignore_index,
        )
        self.crit_row_ce = nn.CrossEntropyLoss()
        self.crit_au_mask_bce = FocalBCEWithLogitsLoss()

        self.bk_loss = CondLaneBreakPointLoss(weights)

        self.logger = get_root_logger()
        self.property_group = property_group
        self.ignore_index = ignore_index
        self.hm_weight = weights.get("hm_weight", 1.)
        self.off_weight = weights.get("kps_weight", 0.4)
        self.loc_weight = weights.get("row_weight", 1.0)
        self.loc_ce_weight = weights.get("row_ce_weight", 0)
        self.range_weight = weights.get("range_weight", 1.0)
        self.aux_weight = weights.get("auxiliary_mask_loss_weight", None)
        self.len_weight = weights.get("len_loss_weight", 10.0)
        self.d2_weight = weights.get("d2_loss_weight", 0)
        self.bk_weight = weights.get("bk_weight", 1)
        self.bg_mask_weight = weights.get("bg_mask_weight", -1)
        self.maskAct = weights.get("maskAct", "sigmoid")

        self.loss_names = [
            "laneline_hm_loss", "roadside_hm_loss", "row_loss", "reg_loss",
            "row_ce_loss", "d2_loss", "range_loss", "bk_loss",
            "lane_type_mask_loss", "lane_color_mask_loss",
            "side_type_mask_loss", "laneline_len_loss", "roadside_len_loss",
            "line_mask_loss", "zero_len_loss", "lane_type_loss",
            "lane_color_loss", "roadside_type_loss", "row_att_lanetype_loss",
            "row_att_color_loss", "row_att_sidetype_loss"
        ]

    def loss_headmap(self, hm, gt, mask):
        """
        inputs:
            hm: [N, 1, H, W]
            gt: [N, 1, H, W]
            mask: None | [N, ]
        outputs:
            loss, info
        """
        if self.hm_weight <= 0 or not mask.any():
            return None, None
        gt = gt[mask]
        #print("========SHAPE OF gt in condlaneloss.loss_headmap======: ", gt.shape)
        # gt可以为全0
        hm = hm[mask]
        #print("========SHAPE OF hm in condlaneloss.loss_headmap======: ", hm.shape)
        hm = hm.sigmoid() if self.maskAct == "sigmoid" else Hsigmoid(hm)
        hm = hm.clamp(min=1e-4, max=1 - 1e-4)
        #print("========SHAPE OF hm passed to self.crit in condlaneloss.loss_headmap======: ", hm.shape)
        loss = self.crit(hm, gt) * self.hm_weight
        info = ((hm > 0.3).sum(), (gt > 0.5).sum())

        return loss, info

    def loss_location(self, x, preds, gts):
        ret = {}

        mask_reg = gts["gt_reg_mask"][0, :, :]  # N, H, W
        if mask_reg.any() and self.off_weight > 0:
            off_map = preds["off_map"][:, 0, :, :]  # N, H, W
            gt_reg = gts["gt_reg"][0, :, :]  # N, H, W
            reg_loss = self.crit_kp(off_map, gt_reg, mask_reg)
            ret["reg_loss"] = self.off_weight * reg_loss

        mask_row = gts["gt_row_masks"][0, :, :] > 0  # N, H
        gt_row = gts["gt_rows"][0, :, :]  # N, H
        if mask_row.any():
            row_loss = self.crit_kp(x, gt_row, mask_row)
            ret["row_loss"] = self.loc_weight * row_loss

            if self.loc_ce_weight > 0:
                loc_map = preds["loc_map"][:, 0, :, :]  # N, H, W
                pos_logits = loc_map[mask_row]
                pos_idx_target = gt_row[mask_row]
                pos_idx_target = pos_idx_target.clamp(0, loc_map.shape[-1] - 1)
                pos_idx_target = torch.round(pos_idx_target).long()
                row_ce_loss = self.crit_row_ce(pos_logits, pos_idx_target)
                ret["row_ce_loss"] = self.loc_ce_weight * row_ce_loss

        if self.d2_weight > 0:
            y_maxs = gts["y_maxs"][:, None]  # N, 1
            diff_mask = mask_row
            diff_mask = diff_mask.scatter(1, y_maxs, 0)
            diff_mask = diff_mask.scatter(1, y_maxs - 1, 0)[:, :-2]
            diff_mask_sum = diff_mask.sum(dim=1, keepdim=True) + 1

            # shape = BxH
            d1 = x[:, :-1] - x[:, 1:]
            d2 = (d1[:, :-1] - d1[:, 1:]) * diff_mask
            d2_mean = d2.sum(dim=1, keepdim=True) / diff_mask_sum
            d22 = (d2 - d2_mean) * diff_mask
            d22 = (d22 * d22).sum(dim=1, keepdim=True) / diff_mask_sum
            # shape = BxH
            d1 = gt_row[:, :-1] - gt_row[:, 1:]
            d2 = (d1[:, :-1] - d1[:, 1:]) * diff_mask
            d2_mean = d2.sum(dim=1, keepdim=True) / diff_mask_sum
            gt_d22 = (d2 - d2_mean) * diff_mask
            gt_d22 = (gt_d22 * gt_d22).sum(dim=1, keepdim=True) / diff_mask_sum

            ret["d2_loss"] = self.d2_weight * (torch.abs(d22 - gt_d22)).mean()
        return ret

    def loss_range(self, preds, gts):
        ret = {}
        pred_ranges = preds.get("vertical_range", None)  # N, H, 2
        if self.range_weight <= 0 or pred_ranges is None:
            return ret
        gt_ranges = gts["gt_ranges"]  # N, H
        range_loss = self.crit_ce(pred_ranges.view(-1, 2), gt_ranges.view(-1))
        ret["range_loss"] = self.range_weight * range_loss
        return ret

    def loss_len(self, pred, gt, mask):
        """
        inputs:
            pred: [N, H]
            gt: [N, H]
            mask: [N, H]
        outputs:
            loss
        """
        if mask.any():
            pred = pred[mask]
            gt = gt[mask]
            loss = torch.abs(pred.sigmoid() - gt)
            loss = loss.mean() * self.len_weight
        else:
            loss = torch.tensor(0).float().cuda()
        return loss

    def loss_bk(self, bk_pred, gts):
        ret = {}
        bk_target = gts.get("break_point_targets", None)
        if bk_target is None or not bk_target.any():
            return ret
        bk_weight = gts.get("break_target_weights", 1)
        bk_loss = self.crit_break_point(bk_pred, bk_target, bk_weight)
        ret["bk_loss"] = bk_loss * self.bk_weight
        self.logger.info(f"gt_break_points {(bk_target > 0.5).sum()} "
                         f"pred_break_points {(bk_pred > 0.3).sum()} ")
        return ret

    def loss_attrs(
        self,
        preds,
        gts,
        masks=[None, None, None],
        attr_bg_weight=-1,
    ):

        losses = self.crit_ce_attrs(preds, gts, masks, attr_bg_weight) #GroupFocalCrossLoss look here 1
        ret = dict(
            lane_type_mask_loss=losses[0] * self.aux_weight[1],
            lane_color_mask_loss=losses[1] * self.aux_weight[2],
            side_type_mask_loss=losses[2] * self.aux_weight[3],
        )
        if len(masks) == 4:
            ret["lane_ms_mask_loss"] = losses[3] * self.aux_weight[1]
        return ret

    def forward(self, output, meta, **kwargs):
        aux_mask = output["aux_mask"]
        mask = output.get("loc_map", None)
        property_score = output.get("property_score", None)
        B, C, H, W = aux_mask.shape
        num_lanes = (kwargs["pos_tensor"][:, 1] == 0).sum()
        laneline_valid = ~kwargs["ignore_laneline"]
        roadside_valid = ~kwargs["ignore_roadside"]
        losses = {}

        # heatmap loss
        laneline_hm_loss, laneline_info = self.loss_headmap(
            output["proposal_heatmap"][:, :1, :, :],
            kwargs["laneline_gt_hm"],
            laneline_valid,
        )
        if laneline_hm_loss is not None:
            losses["laneline_hm_loss"] = laneline_hm_loss
        if laneline_info is not None:
            self.logger.info("laneline pred/gt: %d/%d" % laneline_info)
        roadside_hm_loss, roadside_info = self.loss_headmap(
            output["proposal_heatmap"][:, 1:, :, :],
            kwargs["roadside_gt_hm"],
            roadside_valid,
        )
        if roadside_hm_loss is not None:
            losses["roadside_hm_loss"] = roadside_hm_loss
        if roadside_info is not None:
            self.logger.info("roadside pred/gt: %d/%d" % roadside_info)

        mask_softmax = F.softmax(mask, dim=-1)
        line_mask = aux_mask[kwargs["pos_tensor"][:, 0]]
        row_att = (line_mask * mask_softmax).sum(-1)
        mask = mask[:, 0]
        mask_softmax = mask_softmax[:, 0]
        pos = torch.arange(W, dtype=mask.dtype, device=mask.device)
        row_pos = mask_softmax @ pos + 0.5
        losses.update(self.loss_location(row_pos, output, kwargs))

        losses.update(self.loss_range(output, kwargs))

        if C > 27:
            gt_ranges = kwargs["gt_ranges"]  # N, H
            line_length = torch.cumsum(gt_ranges, 1)
            line_length = line_length.float() / gt_ranges.shape[-1]
            mask = gt_ranges == 1
            for _ in range(3):
                mask[:, :-1] = mask[:, :-1] | mask[:, 1:]

            losses["laneline_len_loss"] = self.loss_len(
                row_att[:num_lanes, 27, :],
                line_length[:num_lanes, :],
                mask[:num_lanes],
            )
            losses["roadside_len_loss"] = self.loss_len(
                row_att[num_lanes:, 28, :],
                line_length[num_lanes:, :],
                mask[num_lanes:],
            )

        losses.update(self.bk_loss(output, **kwargs))

        if self.aux_weight is not None and aux_mask is not None:
            line_mask_gt = torch.stack(
                [
                    kwargs["laneline_mask"],
                    kwargs["roadside_mask"],
                ],
                dim=1,
            ) == 1
            # 可以接受全0
            line_mask = aux_mask[:, :2]
            line_mask_loss = self.crit_au_mask_bce(line_mask, line_mask_gt)
            losses["line_mask_loss"] = line_mask_loss * self.aux_weight[0]

            if C > 27:
                ignore_weight = ~line_mask_gt
                ignore_weight[:, :, int(H * 0.7):, :] = False
                length_field = aux_mask[:, 27:29, :, :].sigmoid()
                zero_len_loss = length_field[ignore_weight].mean()
                losses["zero_len_loss"] = zero_len_loss * self.len_weight

            if C > 2:
                stacked_masks = torch.stack(
                    [
                        kwargs["laneline_label"],
                        kwargs["laneline_color"],
                        kwargs["roadside_label"],
                    ],
                    dim=1,
                )

                stacked_masks[stacked_masks == 0] = self.ignore_index
                losses.update(self.loss_attrs(aux_mask[:, 2:27],
                                              stacked_masks))

        if property_score is not None:
            pred_attrs = torch.split(
                property_score,
                self.property_group,
                dim=1,
            )
            pred_attrs = [
                pred_attrs[0][:num_lanes],
                pred_attrs[1][:num_lanes],
                pred_attrs[2][num_lanes:],
            ]
            gt_attrs = [
                kwargs["gt_type"][:num_lanes],
                kwargs["gt_color"][:num_lanes],
                kwargs["gt_type"][num_lanes:],
            ]
            loss_attrs = self.crit_ce_attrs(pred_attrs, gt_attrs)
            losses["lane_type_loss"] = loss_attrs[0]
            losses["lane_color_loss"] = loss_attrs[1]
            losses["roadside_type_loss"] = loss_attrs[2]

            unvalid_mask = kwargs["gt_row_masks"][0] <= 0
            pred_attrs = torch.split(
                row_att[:, 2:27],
                self.property_group,
                dim=1,
            )
            pred_attrs = [
                pred_attrs[0][:num_lanes],
                pred_attrs[1][:num_lanes],
                pred_attrs[2][num_lanes:],
            ]
            gt_attrs = [_[:, None].repeat(1, H) for _ in gt_attrs]
            gt_attrs[0][unvalid_mask[:num_lanes]] = self.ignore_index
            gt_attrs[1][unvalid_mask[:num_lanes]] = self.ignore_index
            gt_attrs[2][unvalid_mask[num_lanes:]] = self.ignore_index

            loss_attrs = self.crit_ce_attrs(pred_attrs, gt_attrs)
            losses["row_att_lanetype_loss"] = loss_attrs[0]
            losses["row_att_color_loss"] = loss_attrs[1]
            losses["row_att_sidetype_loss"] = loss_attrs[2]

        for k in self.loss_names:
            if k not in losses:
                losses[k] = torch.tensor(0).float().cuda()
            else:
                v = losses[k]
                if torch.isinf(v).any() or torch.isnan(v).any():
                    self.logger.error(f"[inf/nan]{k}:{v.item()}")
                    losses[k] = torch.tensor(0).float().cuda()

        # Only non-zero losses are valid, otherwise multi-GPU training will report an error
        return losses


class SoftFocalBCEWithLogitsLoss(nn.BCEWithLogitsLoss):

    def __init__(self,
                 gamma=2,
                 weight=None,
                 size_average=None,
                 reduce=None,
                 reduction: str = "mean",
                 pos_weight=None) -> None:
        super().__init__(weight,
                         size_average,
                         reduce,
                         reduction="none",
                         pos_weight=pos_weight)
        self.reduction_ = reduction
        self.gamma = gamma

    def forward(self, input_, target, weight=None):
        cross_entropy = super().forward(input_, target)
        cross_entropy = cross_entropy * weight
        p = input_.sigmoid() if self.maskAct == "sigmoid" else Hsigmoid(input_)
        p = p.clamp(min=1e-4, max=1 - 1e-4)
        input_prob = torch.where(target > 0.5, p, 1 - p)
        loss = torch.pow(1 - input_prob, self.gamma) * cross_entropy
        if self.reduction_ in ["sum", "mean"]:
            loss = loss.sum()
            if self.reduction_ == "mean":
                loss = loss / target.sum()
        return loss


@MODELS.register_module()
class CondLaneBreakPointLoss(torch.nn.Module):

    def __init__(self, weights, **kwargs):
        super(CondLaneBreakPointLoss, self).__init__()
        self.bk_weight = weights.get("bk_weight", 1)
        self.logger = get_root_logger()
        self.crit_break_point = SoftFocalBCEWithLogitsLoss()

    def forward(self, output, meta={}, **kwargs):
        ret = {}

        bk_target = kwargs.get("break_point_targets", None)
        if bk_target is None or not bk_target.bool().any():
            return ret
        bk_weight = kwargs.get("break_target_weights", 1)

        break_mask = output.get("break_mask", None)
        if "aux_mask" in output and output["aux_mask"].shape[1] > 29:
            mask_softmax = F.softmax(output["loc_map"], dim=-1)
            line_mask = output["aux_mask"][kwargs["pos_tensor"][:, 0]]
            row_att = (line_mask * mask_softmax).sum(-1)
            break_mask = row_att[:, 29]
        if break_mask is None:
            return ret

        bk_loss = self.crit_break_point(break_mask, bk_target, bk_weight)
        ret["bk_loss"] = bk_loss * self.bk_weight
        self.logger.info(f"gt_break_points {(bk_target > 0.5).sum()} "
                         f"pred_break_points {(break_mask > 0.3).sum()} ")

        return ret


@MODELS.register_module()
class CondLaneRNNLoss(CondLaneLoss):
    """for curvelanes rnn"""

    def __init__(self, weights, num_lane_cls):
        super(CondLaneRNNLoss, self).__init__(weights, num_lane_cls)
        state_weight = 1.0
        self.state_weight = weights[
            "state_weight"] if "state_weight" in weights else state_weight

    def forward(self, output, meta, **kwargs):
        hm, kps, mask, lane_range, states = output[:5]
        hm_loss, kps_loss, row_loss, range_loss, state_loss = 0, 0, 0, 0, 0
        hm = hm.sigmoid().clamp(min=1e-4, max=1 - 1e-4)
        # losses for lane, seg_loss, ins_loss, kp_loss
        if self.hm_weight > 0:
            hm_loss += self.crit(hm, kwargs["gt_hm"])
        if self.kps_weight > 0:
            kps_loss += self.crit_kp(kps, kwargs["gt_reg"],
                                     kwargs["gt_reg_mask"])
        if self.state_weight > 0:
            state_loss += self.crit_ce(states, kwargs["gt_states"])
        if self.row_weight > 0:
            mask_softmax = F.softmax(mask, dim=3)
            pos = compute_locations(mask_softmax.size(),
                                    device=mask_softmax.device)
            row_pos = torch.sum(pos * mask_softmax, dim=3)
            row_loss += self.crit_kp(row_pos, kwargs["gt_rows"],
                                     kwargs["gt_row_masks"])
        if self.range_weight > 0:
            range_loss = self.crit_ce(lane_range, kwargs["gt_ranges"])

        losses = {}
        if self.hm_weight:
            losses["hm_loss"] = self.hm_weight * hm_loss
        if self.kps_weight:
            losses["kps_loss"] = self.kps_weight * kps_loss
        if self.row_weight > 0:
            losses["row_loss"] = self.row_weight * row_loss
        if self.state_weight > 0:
            losses["state_loss"] = self.state_weight * state_loss
        if self.range_weight > 0:
            losses["range_loss"] = self.range_weight * range_loss
        return losses
