import copy

from mmcv.utils import Registry, import_modules_from_strings
from mme.datasets import (DATASETS as MME_DATASETS,
                          PIPELINES as MME_PIPELINES,
                          SAMPLERS as MME_SAMPLERS)
from mme.datasets.builder import ROOT_DATASETS, ROOT_PIPELINES
from mme.utils import add_children_to_registry, import_used_modules


DATASETS = Registry('dataset', parent=MME_DATASETS)
PIPELINES = Registry('pipeline', parent=MME_PIPELINES)
SAMPLERS = Registry('sampler', parent=MME_SAMPLERS)


def _concat_dataset(cfg, default_args=None):
    """Build :obj:`ConcatDataset by."""
    from .dataset_wrappers import ConcatDataset
    img_dir = cfg['img_dir']
    ann_dir = cfg.get('ann_dir', None)
    split = cfg.get('split', None)
    # pop 'separate_eval' since it is not a valid key for common datasets.
    separate_eval = cfg.pop('separate_eval', True)
    num_img_dir = len(img_dir) if isinstance(img_dir, (list, tuple)) else 1
    if ann_dir is not None:
        num_ann_dir = len(ann_dir) if isinstance(ann_dir, (list, tuple)) else 1
    else:
        num_ann_dir = 0
    if split is not None:
        num_split = len(split) if isinstance(split, (list, tuple)) else 1
    else:
        num_split = 0
    if num_img_dir > 1:
        assert num_img_dir == num_ann_dir or num_ann_dir == 0
        assert num_img_dir == num_split or num_split == 0
    else:
        assert num_split == num_ann_dir or num_ann_dir <= 1
    num_dset = max(num_split, num_img_dir)

    datasets = []
    for i in range(num_dset):
        data_cfg = copy.deepcopy(cfg)
        if isinstance(img_dir, (list, tuple)):
            data_cfg['img_dir'] = img_dir[i]
        if isinstance(ann_dir, (list, tuple)):
            data_cfg['ann_dir'] = ann_dir[i]
        if isinstance(split, (list, tuple)):
            data_cfg['split'] = split[i]
        datasets.append(build_dataset(data_cfg, default_args))

    return ConcatDataset(datasets, separate_eval)


def build_dataset(cfg, default_args=None):
    """Build datasets."""
    from .dataset_wrappers import (ConcatDataset, MultiImageMixDataset,
                                   RepeatDataset)
    if isinstance(cfg, (list, tuple)):
        dataset = ConcatDataset([build_dataset(c, default_args) for c in cfg])
    elif cfg['type'] == 'pillar.ConcatDataset':
        dataset = ConcatDataset(
            [build_dataset(c, default_args) for c in cfg['datasets']],
            cfg.get('separate_eval', True))
    elif cfg['type'] == 'pillar.RepeatDataset':
        dataset = RepeatDataset(
            build_dataset(cfg['dataset'], default_args), cfg['times'])
    elif cfg['type'] == 'pillar.MultiImageMixDataset':
        cp_cfg = copy.deepcopy(cfg)
        cp_cfg['dataset'] = build_dataset(cp_cfg['dataset'])
        cp_cfg.pop('type')
        dataset = MultiImageMixDataset(**cp_cfg)
    elif isinstance(cfg.get('img_dir'), (list, tuple)) or isinstance(
            cfg.get('split', None), (list, tuple)):
        dataset = _concat_dataset(cfg, default_args)
    else:
        dataset = _build_dataset(cfg, default_args)

    return dataset


def _build_dataset(cfg, default_args=None):
    """
    API wrapper of build_dataset. Call different build func from
    different repo. Need to design a unified dataset API in
    the future.
    Args:
        cfg: dataset config
        default_args: None

    Returns: Dataset

    """
    # TODO: use unified dataset API
    if 'dataset' in cfg:
        dataset_type = cfg['dataset']['type']
        # 'pipeline' may not in cfg when use multi source datasets.
        pipeline_cfg = cfg['dataset'].get('pipeline', [])
    else:
        dataset_type = cfg['type']
        # 'pipeline' may not in cfg when use multi source datasets.
        pipeline_cfg = cfg.get('pipeline', [])

    # TODO: remove after adding root registry in mmcv
    # No root registry of DATASETS in mmcv,
    # need to dfs config to import all used datasets
    modules = import_used_modules(pipeline_cfg, 'datasets')
    add_children_to_registry(ROOT_DATASETS, 'DATASETS', modules)

    # No root registry of PIPELINES in mmcv,
    # need to dfs config to import all used pipelines
    modules = import_used_modules(pipeline_cfg, 'datasets')
    add_children_to_registry(ROOT_PIPELINES, 'PIPELINES', modules)

    if '.' not in dataset_type:
        # use default scope when type is not defined
        from mme.utils import DEFAULT_SCOPE
        if DEFAULT_SCOPE:
            # directly call default scope's build function
            build_func = import_modules_from_strings(DEFAULT_SCOPE +
                                                     '.datasets').build_dataset
            return build_func(cfg=cfg, default_args=default_args)
        else:
            raise AssertionError('When default scope is not defined, '
                                 'scope is necessary in dataset type!')

    scope = dataset_type.split('.')[0]
    if scope == 'pillar':
        return DATASETS.build(cfg=cfg, default_args=default_args)
    else:
        pac_name = scope + '.datasets'
        build_func = import_modules_from_strings(pac_name).build_dataset
        return build_func(cfg=cfg, default_args=default_args)