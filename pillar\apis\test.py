from collections import defaultdict
import os.path as osp
import time

import mmcv
import torch
from mmcv.utils import import_modules_from_strings
from mmcv.runner import get_dist_info


def _convert_list2dict(results):
    """
        Convert the result format from list[dict] to dict[list] format.
        (Private Function)
    """
    assert isinstance(results, list), \
        'invalid input for function _convert_list2dict'
    if not isinstance(results[0], dict):  # plain list
        return results
    _results = defaultdict(list)
    for result in results:
        for res_name, res_val in result.items():
            _results[res_name].append(res_val)
    results = dict(_results)
    return results


def single_gpu_test(model,
                    data_loader,
                    forward_kwargs=dict(),
                    show=False,
                    out_dir=None,
                    return_dict=True,
                    **show_kwargs):
    """Test model with single gpu.

    Args:
        model (nn.Module): Model to be tested.
        data_loader (nn.Dataloader): Pytorch data loader.
        forward_kwargs (dict): Setting for forward function.
        show, out_dir, show_kwargs: compatibility with other repos
        return_dict (bool): decide the format of returned results

    Returns:
        (list[dict] or dict[list]): The prediction results.
    """
    if forward_kwargs.get('return_loss') is None:
        forward_kwargs['return_loss'] = False
    if show:
        raise NotImplementedError

    model.eval()

    sample_num = len(data_loader.dataset)
    prog_bar = mmcv.ProgressBar(sample_num)

    results = list()
    for i, data in enumerate(data_loader):
        with torch.no_grad():
            outputs = model(**forward_kwargs, **data)
        if out_dir:
            for j in range(data_loader.batch_size):
                order = i*data_loader.batch_size+j
                if order < sample_num:
                    filename = osp.join(out_dir, f'pred_{order}.pkl')
                    mmcv.dump({key: value[j] for key, value in outputs.items()}, filename)

        # flip structure
        if isinstance(outputs, dict):
            _outputs = list()
            for out_vals in zip(*outputs.values()):
                _outputs.append(dict(zip(outputs.keys(), out_vals)))
            outputs = _outputs
        results.extend(outputs)

        prog_bar.update(num_tasks=min(data_loader.batch_size,  sample_num-prog_bar.completed))

    if out_dir:
        return [osp.join(out_dir, f'pred_{i}.pkl') for i in range(sample_num)]
    
    assert len(results) == sample_num, \
        'mismatch between source data and output results'
    if return_dict:  # compatibility with earlier versions
        results = _convert_list2dict(results)
    return results


def _collect_results_func(result_part, size, tmpdir=None, gpu_collect=False, scope=None, **kwargs):
    """
        Build collect function and then call it with given arguments.
        (Private Function)
    """
    if gpu_collect:
        func_name = 'collect_results_gpu'
    else:
        func_name = 'collect_results_cpu'
        kwargs['tmpdir'] = tmpdir
    scope = 'mmdet' if scope in ['pillar', None] else scope  # default scope
    # TODO: use unified test API
    pac_name = f'{scope}.apis.test'
    collect_func = getattr(import_modules_from_strings(pac_name), func_name)
    return collect_func(result_part, size, **kwargs)


def multi_gpu_test(model,
                   data_loader,
                   forward_kwargs=dict(),
                   tmpdir=None,
                   gpu_collect=False,
                   scope=None,
                   return_dict=True,
                   out_dir=None):
    """Test model with multiple gpus.

    Args:
        model (nn.Module): Model to be tested.
        data_loader (nn.Dataloader): Pytorch data loader.
        forward_kwargs (dict): Setting for forward function.
        tmpdir (str): Path to save the temporary results under cpu mode.
        gpu_collect (bool): Option to use either gpu or cpu to collect results.
        scope (str): decide the repo imported by collect func.
        return_dict (bool): decide the format of returned results

    Returns:
        (list[dict] or dict[list]): The prediction results.
    """
    if forward_kwargs.get('return_loss') is None:
        forward_kwargs['return_loss'] = False

    model.eval()

    rank, world_size = get_dist_info()
    sample_num = len(data_loader.dataset)
    if rank == 0:
        prog_bar = mmcv.ProgressBar(sample_num)
    time.sleep(2)  # This line can prevent deadlock problem in some cases.

    results = list()
    for i, data in enumerate(data_loader):
        with torch.no_grad():
            outputs = model(**forward_kwargs, **data)
        
        if out_dir:
            for j in range(data_loader.batch_size):
                order = rank+world_size*(i*data_loader.batch_size+j)
                if order < sample_num:
                    filename = osp.join(out_dir, f'pred_{order}.pkl')
                    mmcv.dump({key: value[j] for key, value in outputs.items()}, filename)

        # flip structure
        if isinstance(outputs, dict):
            _outputs = list()
            for out_vals in zip(*outputs.values()):
                _outputs.append(dict(zip(outputs.keys(), out_vals)))
            outputs = _outputs
        results.extend(outputs)
        if rank == 0:
            prog_bar.update(num_tasks=min(
                data_loader.batch_size * world_size, sample_num - prog_bar.completed))

    if out_dir:
        return [osp.join(out_dir, f'pred_{i}.pkl') for i in range(sample_num)]
    
    # collect results from all ranks
    results = _collect_results_func(
        results, sample_num, tmpdir=tmpdir, gpu_collect=gpu_collect, scope=scope)
    if results is None:
        return None

    assert len(results) == sample_num, \
        'mismatch between source data and output results'
    if return_dict:  # compatibility with earlier versions
        results = _convert_list2dict(results)
    return results
