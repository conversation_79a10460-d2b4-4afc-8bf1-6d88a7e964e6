#!/usr/bin/bash
set -x

PILLAR_HOME="$(dirname $0)/.."

export LAUNCHER=${LAUNCHER:-slurm}
export PARTITION=${PARTITION:-ad_rd}
python -m pip show pillar
if [ $( echo $? ) == 1 ]; then
    python -m pip uninstall -y pillar
    if [ $LAUNCHER == 'slurm' ]; then
        srun -p $PARTITION -n1 -N1 --gres=gpu:1 python -m pip install --user -v -e $PILLAR_HOME --global-option="--function=all"
    else
        python -m pip install --user -v -e $PILLAR_HOME --global-option="--function=all"
    fi
fi

export CONFIG=${1}
PHASE=${PHASE:-train}
DEBUG=${DEBUG:-false}

if [ -z $CONFIG ]; then
    echo "config is required"
    exit 1
fi
if [ $DEBUG == true ]; then
    DEBUG="--debug"
else
    unset DEBUG
fi

CONFIG_PATH=${CONFIG#*functions/}
FUNCTION_NAME=${CONFIG_PATH%%/*}
CONFIG_FILE=${CONFIG##*/}
CONFIG_STEM=${CONFIG_FILE%.*}
export WORK_DIR=${WORK_DIR:-$PILLAR_HOME/work_dirs/$FUNCTION_NAME/$CONFIG_STEM}

export ALGORITHM_BACKEND=${ALGORITHM_BACKEND:-OpenMMLab}
if [ $ALGORITHM_BACKEND == 'Spring' ]; then
    POD_PATH=${POD_PATH:-$PILLAR_HOME/../pytorch-object-detection}
    export PYTHONPATH=$POD_PATH:$PYTHONPATH
fi

export GPUS=${GPUS:-8}
export GPUS_PER_NODE=${GPUS_PER_NODE:-$((${GPUS}>8?8:${GPUS}))}
export CPUS_PER_TASK=${CPUS_PER_TASK:-5}
export SRUN_ARGS=${SRUN_ARGS:-""}

case $PHASE in
    train)
        export TORCH_HOME=$HOME/checkpoints

        export TAG=${TIMESTAMP:-`date +%Y%m%d_%H%M%S`}
        if [ !-z $CHECKPOINT ]; then
            export CHECKPOINT
        fi
        OTHER_ARGS=${@:2}

        pillar train ${FUNCTION_NAME} ${DEBUG} ${OTHER_ARGS}
        ;;
    test)
        export CHECKPOINT=${2}
        EVAL_METRIC=${3}
        OTHER_ARGS=${@:4}

        export TAG="test"

        pillar test ${FUNCTION_NAME} ${DEBUG} \
            --eval ${EVAL_METRIC} ${OTHER_ARGS}
        ;;
    infer)
        export CHECKPOINT=${2}
        DATA=${3}
        IMG_ROOT=${4}
        OTHER_ARGS=${@:5}

        export TAG="infer"

        if [ ! -z ${IMG_ROOT} ]; then
            IMG_ROOT="--img-root=${IMG_ROOT}"
        fi

        pillar infer ${FUNCTION_NAME} ${DEBUG} \
            --data ${DATA} \
            --pin-memory --post-process \
            ${IMG_ROOT} ${OTHER_ARGS}
        ;;
    *)
        echo "invalid phase"
        exit 1
        ;;
esac
