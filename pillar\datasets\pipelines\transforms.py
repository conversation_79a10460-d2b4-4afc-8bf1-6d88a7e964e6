import inspect
import math
import random
from numbers import Number
from typing import Sequence

import mmcv
import numpy as np

from ..builder import PIPELINES


@PIPELINES.register_module()
class RandomOmit(object):
    def __init__(self, omitting_ratio):
        omitting_ratio = [
            omitting_ratio.get('left'), omitting_ratio.get('top'),
            omitting_ratio.get('right'), omitting_ratio.get('bottom')]
        for spatial_idx, spatial_ratio in enumerate(omitting_ratio):
            if spatial_ratio is None:
                spatial_ratio = [1., 1.]
            elif isinstance(spatial_ratio, (int, float)):
                spatial_ratio = [spatial_ratio, 1.]
            elif isinstance(spatial_ratio, (tuple, list)):
                assert len(spatial_ratio) == 2 
            else:
                raise Exception('invalid spatial omitting')
            for ratio in spatial_ratio:
                assert isinstance(ratio, (int, float))
                assert ratio <= 1.
            assert spatial_ratio[0] <= spatial_ratio[1]
            omitting_ratio[spatial_idx] = spatial_ratio
        self.omitting_ratio = omitting_ratio

    @staticmethod
    def get_params(img_hw, omitting_ratio):
        height, width = img_hw
        random_omit = list()
        for spatial_ratio, spatial_scale in zip( 
            omitting_ratio, [width, height] * 2):
            spatial_omit = np.rint((1. - random.uniform(*spatial_ratio)) * spatial_scale)
            random_omit.append(int(spatial_omit))
        x1, y1 = random_omit[0], random_omit[1]
        x2 = width - 1 - random_omit[2]
        y2 = height - 1 - random_omit[3]
        bbox = np.array([x1, y1, x2, y2])
        return tuple(random_omit), bbox

    def __call__(self, results):
        for idx, key in enumerate(results.get('img_fields', ['img'])):
            img = results[key]
            if idx == 0:
                field_hw = img.shape[:2]
                omitting, bbox = self.get_params(field_hw, self.omitting_ratio)
            else:
                assert img.shape[:2] == field_hw
            if np.any(omitting):
                img = mmcv.imcrop(img, bbox)
            results[key] = img
        return results


@PIPELINES.register_module()
class RandomPad(object):
    def __init__(self, padding_ratio, padding_mode='constant'):
        padding_ratio = [
            padding_ratio.get('left'), padding_ratio.get('top'),
            padding_ratio.get('right'), padding_ratio.get('bottom')]
        for spatial_idx, spatial_ratio in enumerate(padding_ratio):
            if spatial_ratio is None:
                spatial_ratio = [1., 1.]
            elif isinstance(spatial_ratio, (int, float)):
                spatial_ratio = [1., spatial_ratio]
            elif isinstance(spatial_ratio, (tuple, list)):
                assert len(spatial_ratio) == 2
            else:
                raise Exception('invalid spatial padding')
            for ratio in spatial_ratio:
                assert isinstance(ratio, (int, float))
                assert ratio >= 1.
            assert spatial_ratio[0] <= spatial_ratio[1]
            padding_ratio[spatial_idx] = spatial_ratio
        self.padding_ratio = padding_ratio
        assert padding_mode in ['constant', 'edge', 'reflect', 'symmetric']
        self.padding_mode = padding_mode

    @staticmethod
    def get_params(img_hw, padding_ratio):
        height, width = img_hw
        random_pad = list()
        for spatial_ratio, spatial_scale in zip(
            padding_ratio, [width, height] * 2):
            spatial_pad = np.rint((random.uniform(*spatial_ratio) - 1.) * spatial_scale)
            random_pad.append(int(spatial_pad))
        return tuple(random_pad)

    def __call__(self, results):
        for idx, key in enumerate(results.get('img_fields', ['img'])):
            img = results[key]
            if idx == 0:
                field_hw = img.shape[:2]
                padding = self.get_params(field_hw, self.padding_ratio)
            else:
                assert img.shape[:2] == field_hw
            if np.any(padding):
                img = mmcv.impad(img, padding=padding, padding_mode=self.padding_mode)
            results[key] = img
        return results