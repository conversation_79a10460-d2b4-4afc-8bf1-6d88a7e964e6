load checkpoint from local path: ./checkpoint/condlane_with_mobilenetv3_backbone_ftuned_for_100epochs.pth
data_test:  {'type': 'pillar.LkdCropClsDataset', 'ann_file': '/home/<USER>/data/large_new_data_cropped_combined_test/large_new_data_cropped_combined_test.json', 'img_prefix': '/home/<USER>/data/large_new_data_cropped_combined_test', 'pipeline': [{'type': 'pillar.LoadLanes', 'ceph_cfg': '', 'roi_method': 'crop'}, {'type': 'pillar.Alaug', 'transforms': [{'type': 'Compose', 'params': {'bboxes': False, 'keypoints': True, 'masks': True}}, {'type': 'Resize', 'height': 288, 'width': 864, 'p': 1}]}, {'type': 'mmcls.Normalize', 'mean': [75.3, 76.6, 77.6], 'std': [50.5, 53.8, 54.3], 'to_rgb': False}, {'type': 'pillar.CollectLkdClsLane', 'down_scale': 4, 'hm_down_scale': 16, 'radius': 3}, {'type': 'pillar.DefaultFormatBundle'}], 'test_mode': True, 'camera_roi': {'front_fov30': {'t': 0, 'b': 1, 'l': 0, 'r': 1}, 'front_fov120': {'t': 0, 'b': 1, 'l': 0, 'r': 1}}, 'categories': {'group': ['lane_type', 'lane_color', 'roadside'], 'attr': {'lane_type': ['NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE', 'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE', 'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED', 'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID', 'FISHBONE_DASHED'], 'lane_color': ['NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE', 'BLUE'], 'roadside': ['NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE', 'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE']}}, 'camera_filter': True}
ann_file:  /home/<USER>/data/large_new_data_cropped_combined_test/large_new_data_cropped_combined_test.json
line:  {'filename': 'filename/1698190599.798131170.png', 'camera_index': 'front_fov120', 'data_source': '20240829_JH_199', 'image_width': 1920, 'image_height': 1080, 'date': {'lane': '20240829', 'side': '20240829\r'}, 'tags': {'time': 'day', 'weather': 'cloudy', 'scene': 'normal', 'special_line': 'normal', 'hardcase': 'normal', 'lanetype': [0, 0, 5, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'lanecolor': [0, 0, 7, 0, 0, 0, 0], 'sidetype': [0, 0, 0, 2, 1, 0, 0]}, 'laneline': 'laneline/1698190599.798131170.png', 'laneline_type': 'laneline_type/1698190599.798131170.png', 'laneline_color': 'laneline_color/1698190599.798131170.png', 'roadside': 'roadside/1698190599.798131170.png', 'sidetype': 'sidetype/1698190599.798131170.png', 'lane_cnt': 7, 'side_cnt': 3}
fullpath:  /home/<USER>/data/large_new_data_cropped_combined_test/filename/1698190599.798131170.png
subpath:  filename/1698190599.798131170.png
config.camera_roi.camera:  {'t': 0, 'b': 1, 'l': 0, 'r': 1}
config.img_scale:  (864, 288)
config.img_norm_cg.mean:  [75.3, 76.6, 77.6]
config.img_norm_cfg.std:  [50.5, 53.8, 54.3]
crop:  False
>>>>>>>> infer end to end

======SHAPE OF img before passing to self.backbone(img)=========:  torch.Size([2, 3, 288, 864]) 

outputs.keys():  dict_keys(['net'])
deploy phase begin!
>>>>>>>> infer net
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
list(input_dict.keys()):  ['image', 'image2']
output_names:  ['laneline_field', 'attributes', 'heat_nms_weight_by_relu6_proposal_heatmap', 'heat_nms_weight_by_relu6_line_mask', 'sigmoid_proposal_offset', 'sigmoid_line_mask', 'laneline_vec_field', 'roadside_vec_field', 'field_pass_simp_laneline_inv_vector_field', 'scale_length_roadside_length']

======SHAPE OF img before passing to self.backbone(img)=========:  torch.Size([2, 3, 288, 864]) 

simplify begin!
simplify end!
convert done!
deploy phase done (with opset 12)!
category_param.json
meta.json
parameters.json
net/model.onnx
upload model.tar like this:
>>> python -m adela.cmd -p <pid> -rat ./tokestrel/tokestrel/model.tar
