import os

def rename_json_files(directory):
    """
    Rename JSON files in the specified directory by removing 'post_process_' and '_c++' parts.
    
    Args:
        directory (str): Path to the directory containing JSON files.
    """
    for filename in os.listdir(directory):
        # Check if the file is a JSON file
        if filename.endswith(".json") and "post_process_" in filename and "_c++" in filename:
            # Remove 'post_process_' and '_c++' from the filename
            new_name = filename.replace("post_process_", "").replace("_c++", "")
            
            # Build full paths for renaming
            old_path = os.path.join(directory, filename)
            new_path = os.path.join(directory, new_name)
            
            # Rename the file
            os.rename(old_path, new_path)
            print(f"Renamed: {filename} -> {new_name}")

# Example usage
directory = "cpp_res"  # Replace with the path to your directory
rename_json_files(directory)
