import argparse
import os
import warnings

from mmcv import DictAction

from pillar.algorithm_backend.builder import build_algorithm_backend


def parse_args():
    parser = argparse.ArgumentParser(
        description='Pillar test (and eval) a model')
    parser.add_argument('config', help='test config file path')
    parser.add_argument('checkpoint', help='checkpoint file')
    parser.add_argument(
        '--work-dir',
        help='the directory to save the file containing evaluation metrics')
    parser.add_argument('--out', help='output result file in pickle format')
    parser.add_argument(
        '--fuse-conv-bn',
        action='store_true',
        help='Whether to fuse conv and bn, this will slightly increase'
        'the inference speed')
    parser.add_argument(
        '--format-only',
        action='store_true',
        help='Format the output results without perform evaluation. It is'
        'useful when you want to format the result to a specific format and '
        'submit it to the test server')
    parser.add_argument(
        '--eval',
        type=str,
        nargs='+',
        help='evaluation metrics, which depends on the dataset, e.g., '
        '- Detection task: "bbox", "segm", "proposal" for COCO, '
        'and "mAP", "recall" for PASCAL VOC. '
        '- Segmentation task: "mIoU" for generic datasets, and '
        '"cityscapes" for Cityscapes. '
        '- Classification task: "accuracy", "precision", "recall", '
        '"f1_score", "support" for single label dataset, and "mAP", '
        '"CP", "CR", "CF1", "OP", "OR", "OF1" for multi-label dataset. '
        '- Pose task: "mAP" for MSCOCO')
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.')
    parser.add_argument(
        '--options',
        nargs='+',
        action=DictAction,
        help='custom options for evaluation, the key-value pair in xxx=yyy '
        'format will be kwargs for dataset.evaluate() function (deprecate), '
        'change to --eval-options instead.')
    parser.add_argument(
        '--eval-options',
        nargs='+',
        action=DictAction,
        help='custom options for evaluation, the key-value pair in xxx=yyy '
        'format will be kwargs for dataset.evaluate() function')
    parser.add_argument('--launcher',
                        choices=['none', 'pytorch', 'slurm', 'mpi'],
                        default='none',
                        help='job launcher')
    parser.add_argument('--local_rank', type=int, default=0)
    parser.add_argument('--algorithm-backend',
                        choices=['OpenMMLab', 'Spring'],
                        default='OpenMMLab',
                        help='algorithm library backend')
    parser.add_argument('--function', type=str, help='pillar function name')
    parser.add_argument('--load_file',
                        type=str,
                        default=None,
                        help='load infer result file path')
    parser.add_argument('--petrel-conf-path',
                        type=str,
                        default='~/petreloss.conf',
                        help='config path of petrel client')
    # TODO: POD test argument, need to be merged with OpenMMLab
    parser.add_argument('--no_running_config',
                        help='disable display running config',
                        action='store_true')
    parser.add_argument('--opts',
                        help='options to replace yaml config',
                        default=None,
                        nargs=argparse.REMAINDER)

    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)

    if args.options and args.eval_options:
        raise ValueError(
            '--options and --eval-options cannot be both '
            'specified, --options is deprecated in favor of --eval-options')
    if args.options:
        warnings.warn('--options is deprecated in favor of --eval-options')
        args.eval_options = args.options
    return args


def main():
    #print("=================INSIDE TEST.PY=================", flush=True)
    args = parse_args()

    backend_cfg = dict(type=args.algorithm_backend, args=args)
    #print("=========args.algorithm_backend===============: ", args.algorithm_backend, flush=True)
    #print("BUILDING ALGORITHM BACKEND", flush=True)
    backend = build_algorithm_backend(backend_cfg)
    backend.test()


if __name__ == '__main__':
    main()
