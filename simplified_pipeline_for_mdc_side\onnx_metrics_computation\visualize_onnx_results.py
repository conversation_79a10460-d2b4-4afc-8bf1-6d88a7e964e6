import os
import time
import cv2
import yaml 
import numpy as np
import onnxruntime
import json
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import random

def get_images_paths():
    """Load a sorted list of images' paths"""
    specific_imgs = ['1698307782.598422500.jpg', '1698306250.948376724.jpg', '1698305506.248312645.jpg', '1698305152.402852101.jpg', '1698304652.448385887.jpg', '1698304240.293719442.jpg', '1698303384.343659569.jpg']
    img_path = r"/work/home/<USER>/niloofar/new/data/large_new_data_batch3_test_cropped/filename" #change
    img_list = os.listdir(img_path)
    #img_list = img_list[0:5]
    img_list = [f for f in img_list if f in specific_imgs]
    #print(img_list)
    # Filter out non-image files
    img_list = [f for f in img_list if f.endswith(".png") or f.endswith(".jpg")]
    #print(img_list)
    # sorted_list = sorted(
    #     img_list, key=lambda x: int(x.replace(x.split('.')[1], "").replace(x.split('.')[0], "replace(".jpg", ""))
    # )
    img_list = [os.path.join(img_path, i) for i in img_list]
    return img_list
  
def get_yaml_config():
    with open("./config.yaml", "r", encoding="utf-8") as f:
        cfg = f.read()
        yaml_content = yaml.safe_load(cfg)
    
    return yaml_content
  
def parse_roi(W, H, l=0.0, t=1 / 6, r=1.0, b=2 / 3):
    l = W * l if l <= 1 else l
    t = H * t if t <= 1 else t
    r = W * r if r <= 1 else r
    b = H * b if b <= 1 else b
    l, t, r, b = map(int, [l, t, r, b])
    assert 0 <= l and r <= W
    assert 0 <= t and b <= H
    return l, t, r, b
  
IMG_NORM_CFG = dict(
    mean=[75.3, 76.6, 77.6],
    std=[50.5, 53.8, 54.3],
    to_rgb=False,
)

def preprocess_image(image_path, camera_roi, model_input_shape=[1024, 576]):
    """Do all types of image preprocessing here"""

    #print("image path: ", image_path)
    image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED).astype(np.uint8)
    #cropped_image = image[roi["top"]:roi["height"], roi["left"]:roi["width"], :]
    H, W = image.shape[:2]
    roi = parse_roi(W, H, **camera_roi)
    l, t, r, b = roi 
    cropped_image = image[t:b, l:r, :]
    resized_image = cv2.resize(
        cropped_image, tuple(model_input_shape), interpolation=cv2.INTER_LINEAR,
    )
    """
    normalized_img = denormalize(
        resized_image,
        IMG_NORM_CFG["mean"],
        IMG_NORM_CFG["std"],
        IMG_NORM_CFG["to_rgb"],
    )
    """
    preprocessed_img = np.expand_dims(resized_image, 0)
    #transposed_img = np.transpose(expanded_img, (0, 3, 1, 2))
    #preprocessed_img = np.ascontiguousarray(transposed_img)
    return preprocessed_img
  
  
#"modified"
"""
onnx_keys = [
            "line_mask",
            "laneline_field",
            "roadside_field",
            "roadside_length",
            "attributes",
            "laneline_inv_vector_field",
            "proposal_heatmap",
            "proposal_offset",
            "maxpool",
            "heat_nms_weight_by_relu6_proposal_heatmap",
            "mask_maxpool",
            "heat_nms_weight_by_relu6_line_mask",
            "sigmoid_proposal_offset",
            "sigmoid_line_mask",
        ]
"""

onnx_keys = [
        "laneline_field", 
        "attributes",
        "heat_nms_weight_by_relu6_proposal_heatmap",
        "heat_nms_weight_by_relu6_line_mask", 
        "sigmoid_proposal_offset", 
        "sigmoid_line_mask", 
        "laneline_vec_field", 
        "roadside_vec_field",
        "field_pass_simp_laneline_inv_vector_field", 
        "scale_length_roadside_length"]


def process_images(preprocessed_img, path_onnx="model_remove.onnx"):
    """Load CondLane Model and process images"""
    sess_options = onnxruntime.SessionOptions()
    sess_options.intra_op_num_threads = 8
    print("inside process_image: running onnxruntime.inferencesession")
    onnx_model = onnxruntime.InferenceSession(path_onnx, sess_options=sess_options)
    input_data = {"image": preprocessed_img, "image2": preprocessed_img}
    print("inside process_image: running onnx_model.run")
    model_output = onnx_model.run(None, input_data)

    model_output_dict = {}
    for i, key in enumerate(onnx_keys):
        model_output_dict[key] = model_output[i]
    print("inside process_image: returning model_output_dict")
    return model_output_dict

def cmp_to_key(mycmp):
    """Convert a cmp= function into a key= function"""

    class K(object):
        __slots__ = ["obj"]

        def __init__(self, obj):
            self.obj = obj

        def __lt__(self, other):
            return mycmp(self.obj, other.obj) < 0

        def __gt__(self, other):
            return mycmp(self.obj, other.obj) > 0

        def __eq__(self, other):
            return mycmp(self.obj, other.obj) == 0

        def __le__(self, other):
            return mycmp(self.obj, other.obj) <= 0

        def __ge__(self, other):
            return mycmp(self.obj, other.obj) >= 0

        __hash__ = None

    return K
  
def softmax(scores, axis=-1):
    exp = np.exp(scores - np.max(scores, axis, keepdims=True))
    p = exp / np.sum(exp, axis, keepdims=True)
    return p


def attr_group(attr, with_softmax=True):
    attr = np.split(attr, np.array([13, 19]))
    if with_softmax:
        attr = list(map(softmax, attr))
    return attr

def gather_row(row, x_i, kernel=3):
    W = row.shape[-1]
    values = [row[x_i]] * kernel
    for dx in range(1, 1 + int(kernel // 2)):
        if x_i + dx < W:
            values[+dx] = row[x_i + dx]
        if x_i - dx >= 0:
            values[-dx] = row[x_i - dx]
    return max(values)
  
def check_len(points, th_len, th_kps_min, th_kps_max):
    if len(points) < th_kps_min:
        return False
    else:
        if len(points) >= th_kps_max:
            return True
        else:
            line_len = np.linalg.norm(points[:-1] - points[1:], 2, -1).sum()
            return line_len >= th_len

def update_visit(visit, line, value=+1, th=2, repeat=1):
    H, W = visit.shape[-2:]
    L = len(line)

    def expand(ss, es):
        ss_ = []
        es_ = []
        for i, (start, stop) in enumerate(zip(ss, es)):
            if i > 0:
                start = min(start, ss[i - 1])
                stop = max(stop, es[i - 1])
            if i < L - 1:
                start = min(start, ss[i + 1])
                stop = max(stop, es[i + 1])
            ss_.append(start)
            es_.append(stop)
        return ss_, es_

    starts = [x - th for x, y in line]
    stops = [x + th for x, y in line]
    for _ in range(repeat):
        starts, stops = expand(starts, stops)
    for i, (x, y) in enumerate(line):
        y = int(y)
        if y < 0 or y >= H:
            continue
        start = starts[i]
        stop = stops[i]
        start = max(int(np.floor(start)), 0)
        stop = min(int(np.ceil(stop)), W - 1)
        visit[y, start : stop + 1] += value
        if i == 0 and y > 0:
            visit[y - 1, start : stop + 1] += value
        if i == L - 1 and y < H - 1:
            visit[y + 1, start : stop + 1] += value
    return visit

def field_pass_v4_simp(
        start,           #None
        start_point_pos, #start point
        proposal_type,   #START_PROPOSAL or MASK_PROPOSAL
        len_field,      # there is only roadside_length, no laneline_length, it means the the length of roadside, and scale it
        inv_len_field,  # inv_len_field always None
        vec_field,     #laneline field means the increment along laneline, roadside field means the increment along roadside
        inv_vec_field, #there is only laneline_inv_vector_field, which also means the increment along laneline, and there is no roadside_inv_vector_field, keep in mind that there two direction along the laneline
        attr_field, #attribute for lane type, lane color, and roadside type
        line_mask,  #segmentation results for laneline
        bk=None,    #alway None
        dp=None,     #use dp to decide whether to skip the start point proposal or not
        pc=0,       #pc=0 is laneline pc=1 is roadside
        kernel=3,
        img_name="",
):
    H, W = vec_field.shape[-2:] #vec_field.shape: (2, 2, 144, 256)
    W = int(W/2)
    num_row_point = W * 2
    # for vec_field and inv_vec_field: (2, 2, 144, 256)→(2, 144, 256, 2)→(2, 144, 512)
    #vec_field = vec_field.reshape(-1, 2, H, W).transpose(0, 2, 3, 1).reshape(-1, H, num_row_point)
    
    #if inv_vec_field is not None:
    #    inv_vec_field = inv_vec_field.reshape(-1, 2, H, W).transpose(0, 2, 3, 1).reshape(-1, H, num_row_point)
        

    weights = [1, 0.8, 0.6, 0.4, 0.2]
    idxs = []
    points = []
    line_pos = np.zeros(shape=[H], dtype=np.float32)
    line_vector_weight = np.zeros(shape=[H], dtype=np.float32)

    start_x, start_y = start_point_pos
    start_x_idx = int(np.clip(start_x * 2, 0, num_row_point - 1))
    start_y_idx = int(np.clip(start_y, 0, H - 1))

    points.append([start_x, start_y])  # todo(liuhao1): 为何不是idx，没有用超分？
    if dp is not None:
        # the grid have been visited, skip it
        if dp[start_y_idx, start_x_idx] >= 1:
            return None, None
        idxs.append([start_x_idx, start_y_idx])

    
    mem_length = H
    mem_score = 1
    forward_go = True
    x_idx, y_idx = start_x_idx, start_y_idx
    while forward_go:
        forward_go = False
        ref_x = np.clip((x_idx + 0.5) / 2, 0, W - 1)

        # length (only for roadside)
        if len_field is not None:
            mem_length -= 1
            cur_len = len_field[y_idx, int(ref_x)]
            if cur_len < 2:
                cur_len = gather_row(len_field[y_idx], int(ref_x), kernel)
            if mem_length == H - 4:  # todo(liuhao1): why 4?
                mem_length = cur_len
            elif mem_length < H - 4:
                mem_length = 0.4 * mem_length + (1 - 0.4) * cur_len
        else:
            mem_length = 0
            cur_len = 0

        # next position
        for j in range(vec_field.shape[0]):
            if y_idx - j - 1 >= 0:
                w = weights[j]
                dx = vec_field[j, y_idx, x_idx]
                if None is not None:
                    dx *= (W / None)
                line_pos[y_idx - j - 1] += (dx + ref_x) * w
                line_vector_weight[y_idx - j - 1] += w
        cur_y = y_idx - 1
        if cur_y < 0 or cur_y > H - 1:
            break
        cur_x = line_pos[y_idx - 1] / line_vector_weight[y_idx - 1]
        if cur_x < 0 or cur_x > W - 1:
            break
        x_idx = int(cur_x * 2)
        y_idx = int(cur_y)
        if x_idx < 0 or y_idx < 0 or x_idx > num_row_point - 1 or y_idx > H - 1:
            break
        if dp is not None:
            if dp[y_idx, x_idx] >= 1:
                break
            idxs.append([x_idx, y_idx])

        # next mask score (only for laneline)
        if pc == 0:
            mask_score = line_mask[  # todo(liuhao1): mask map also super resolution
                y_idx,
                int(x_idx / 2)]
            mem_score = 0.4 * mem_score + (1 - 0.4) * mask_score
        else:
            mem_score = 0
        points.append([cur_x, cur_y])
        if 0 < y_idx < H - 1 and 0 < x_idx < num_row_point - 1:
            if (pc == 1 and mem_length >= 2 and cur_len >= 2) or (pc == 0 and mem_score >= 0.5):
                forward_go = True

    if pc == 0 and proposal_type == "MASK_PROPOSAL" and inv_vec_field is not None:
        inv_idxs = idxs[::-1]
        inv_points = points[::-1]
        inv_go = True
        mem_score = 1
        x_idx, y_idx = start_x_idx, start_y_idx
        while inv_go:  # length >= 1.0 and y_idx < H - 1 and 0 <= x_idx < num_row_point:
            inv_go = False
            ref_x = np.clip((x_idx + 0.5) / 2, 0, W - 1)
            # next position
            for j in range(vec_field.shape[0]):  # todo(liuhao1): inv_vec_field
                if y_idx + j + 1 < H:
                    w = weights[j]
                    dx = inv_vec_field[j, y_idx, x_idx]
                    if None is not None:
                        dx *= (W / None)
                    line_pos[y_idx + j + 1] += (dx + ref_x) * w
                    line_vector_weight[y_idx + j + 1] += w
            cur_y = y_idx + 1
            if cur_y < 0 or cur_y > H -1:
                break
            cur_x = line_pos[y_idx + 1] / line_vector_weight[y_idx + 1]
            if cur_x < 0 or cur_x > W - 1:
                break
            x_idx, y_idx = int(cur_x * 2), int(cur_y)
            if x_idx < 0 or y_idx < 0 or x_idx > num_row_point - 1 or y_idx > H - 1:
                break
            if dp is not None:
                if dp[y_idx, x_idx] >= 1:
                    break
                inv_idxs.append([x_idx, y_idx])

            # next mask score
            mask_score = line_mask[
                y_idx,
                int(x_idx / 2)]
            mem_score = 0.4 * mem_score + (1 - 0.4) * mask_score
            inv_points.append([cur_x, cur_y])
            if 0 < y_idx < H - 1 and 0 < x_idx < num_row_point - 1:
                if mem_score > 0.5:
                    inv_go = True

        points = inv_points[::-1]
        idxs = inv_idxs[::-1]
    
    
    points = np.array(points)
    points = points + [0, 0.5]
    kp_thr = 35 if pc == 0 else 35
    if not check_len(points, 10, 6, kp_thr):
        return None, None
    th_dp_radius = 2 if pc == 0 else 9
    # update_overlap(dp, idxs, r=th_dp_radius)
    update_visit(dp, idxs, 3, th_dp_radius, th_dp_radius)
    attr_scores = []
    for x_, y_ in points:
        x_ = int(x_ / 4)
        y_ = int(y_ / 4)
        if x_ < 0 or x_ >= attr_field.shape[2] or y_ >= attr_field.shape[1]:
            continue
        attr_scores.append(attr_field[:, y_, x_])
    return points, attr_scores
  
def find_hot_points(heat_map, threshold=0.6):
    """Finds the location of hot points

    This function finds those points in a heat map which their value is higher than the threshold.
    """
    indexes = list(np.where(heat_map > threshold))
    points = (
        np.stack(indexes, -1),
        heat_map[indexes[0], indexes[1], indexes[2], indexes[3]],
    )
    return points
  
def process_v4_batch_size_2(
    proposal_indices,
    cls_score,
    output,
):
    lines = {}
    H, W = output["laneline_field"].shape[2:]
    # proposals from heatmap
    
    
    nms_proposal_indices = {}
    
    if proposal_indices is not None and True:
    #if proposal_indices is not None and self.with_hm:
        _, _, hm_h, hm_w = output["sigmoid_proposal_offset"].shape
        proposal_hm_ratio = H / hm_h
        for (pn, pc, ph, pw), s in zip(proposal_indices, cls_score): #pn is picture number, pc=0 for laneline, pc=1 for roadside, ph is y coordinate for start point proposal, pw is x coordinate for start point proposal
            off_x, off_y = output["sigmoid_proposal_offset"][pn, pc * 2:pc * 2 + 2, ph, pw]
            off_x, off_y = off_x * hm_w, off_y * hm_h #scale up to get the real offset
            start_x, start_y = np.clip(pw + 0.5 + off_x, 0, hm_w -1), np.clip(ph + 0.5 + off_y, 0, hm_h - 1) #compute start point coordinates for start points
            img_id = (pn, pc)
            if img_id not in nms_proposal_indices:
                nms_proposal_indices[img_id] = {}

            proposal = (int(start_x), int(start_y))
            if proposal not in nms_proposal_indices[img_id]:
                nms_proposal_indices[img_id][proposal] = (s, 1, [[      #s is the classification score whether the start point is laneline/roadside or not
                    start_x * proposal_hm_ratio,
                    start_y * proposal_hm_ratio     #scale up to get the real start point
                ]])
            else:
                score, cnt, start_points = nms_proposal_indices[img_id][
                    proposal]
                start_points.append([
                    start_x * proposal_hm_ratio,
                    start_y * proposal_hm_ratio
                ])
                nms_proposal_indices[img_id][proposal] = (score + s,
                                                            cnt + 1,
                                                            start_points)
    
    # proposals from mask only for lane
    mask_proposal_indices = {}
    for (pn, pc, ph, pw), s in zip(output["mask_pos_tensor"], #ph is y coordinate for start point proposal, pw is x coordinate for start point proposal
                                    output["mask_scores"]):
        if pc == 1: continue
        if ph >= H - 30: 
        #if ph >= H - self.mask_ignores: 
            continue 
        img_id = (pn, pc)       #pn is picture No.,pc=0 for laneline
        if img_id not in mask_proposal_indices:
            mask_proposal_indices[(pn, pc)] = [
                (s, 1, np.array([pw + 0.5, ph]), 'MASK_PROPOSAL')   #s is the classification score whether the start point is laneline or not
            ]
        else:
            mask_proposal_indices[(pn, pc)].append(
                (s, 1, np.array([pw + 0.5, ph]), 'MASK_PROPOSAL'))




    ordered_proposals_dict = {}
    for img_id in nms_proposal_indices:
        # img_id contains (img_idx, lane_type), so dp will be cleared when handling roadside and lane in single image
        ordered_proposals = [
            nms_proposal_indices[img_id][k]
            for k in nms_proposal_indices[img_id]
        ]
        ordered_proposals = sorted(
            [(v[0], v[1], np.array(v[2]).mean(axis=0), 'START_PROPOSAL')
                for v in ordered_proposals],
            key=cmp_to_key(lambda a, b: -a[0] * a[2][1] + b[0] * b[2][1]))   #sort lane and roadside start points proposal by specific rule
        ordered_proposals_dict[img_id] = ordered_proposals
        
    for img_id in mask_proposal_indices:
        mask_proposals = mask_proposal_indices[img_id]
        mask_ordered_proposals = sorted(
            mask_proposals,
            key=cmp_to_key(lambda a, b: -a[0] * a[2][1] + b[0] * b[2][1]))  #sort lane start points proposal by specific rule
        ordered_proposals_dict[img_id] = ordered_proposals_dict.get( img_id, []) + mask_ordered_proposals   #add and merge lane start points proposal 


    #print("ordered_proposals_dict: ", ordered_proposals_dict.keys())    
    for img_id, ordered_proposals in ordered_proposals_dict.items():    #walk through the total lane and roadside start points
        pn, pc = img_id                 
        #print("pn: ", pn)#pn is picture number, pc=0 for laneline, pc=1 for roadside,
        dp = np.zeros(
            shape=(H, W * 2), dtype=np.float32) 
        for k, proposal in enumerate(ordered_proposals):
            if proposal[-1] == "MASK_PROPOSAL":
                #fastly filter mask proposal
                x, y = proposal[2]
                #x, y = int(x * self.num_row_ref_point), int(y)
                x, y = int(x * 2), int(y)

                if dp[y, x] > 0: continue       #dp is used to skip some laneline start point proposal 
            s, cnt, start_point_pos, proposal_type = proposal
            
            if pc == 0:
                start = None
                bk = None
                len_field =  None #for output, there is only roadside_length, not laneline_length, it means the the length of roadside
                inv_len_field =  None #for output, there is not roadside_length, not laneline_length, so it's always None
                #vec_field = output[5][pn]  #for output, laneline field means the increment along laneline, roadside field means the increment along roadside
                vec_field = output["laneline_vec_field"][pn]
                #inv_vec_field = output[9][pn]   #for output, there is only laneline_inv_vector_field, which also means the increment along laneline, and there is no roadside_inv_vector_field
                inv_vec_field = output["field_pass_simp_laneline_inv_vector_field"][pn]
                points, attr_scores = field_pass_v4_simp(
                    start, #None
                    start_point_pos, #start point
                    proposal_type, #START_PROPOSAL or MASK_PROPOSAL
                    len_field=None, # there is only roadside_length, no laneline_length, it means the the length of roadside, and scale it
                    inv_len_field=None,  # inv_len_field always None
                    vec_field=vec_field,   #laneline field means the increment along laneline, roadside field means the increment along roadside
                    inv_vec_field=inv_vec_field, #there is only laneline_inv_vector_field, which also means the increment along laneline, and there is no roadside_inv_vector_field, keep in mind that there two direction along the laneline
                    attr_field=output["attributes"][pn], #for lane type, lane color, and roadside type
                    line_mask=output["sigmoid_line_mask"][pn][pc], #segmentation results for laneline
                    bk=bk, #always None
                    dp=dp if dp is not None else None,
                    pc=pc, #pc=0 is laneline pc=1 is roadside
                )
            else: 
                start = None
                bk = None
                #len_field = output[7][pn][0] #for output, there is only roadside_length, not laneline_length, it means the the length of roadside
                len_field = output["scale_length_roadside_length"][pn][0]
                inv_len_field =  None #for output, there is not roadside_length, not laneline_length, so it's always None
                #vec_field = output[6][pn]  #for output, laneline field means the increment along laneline, roadside field means the increment along roadside
                vec_field = output["roadside_vec_field"][pn]
                inv_vec_field = None  #for output, there is only laneline_inv_vector_field, which also means the increment along laneline, and there is no roadside_inv_vector_field
                points, attr_scores = field_pass_v4_simp(
                    start, #None
                    start_point_pos, #start point
                    proposal_type, #START_PROPOSAL or MASK_PROPOSAL
                    #len_field=scale_length(len_field), # there is only roadside_length, no laneline_length, it means the the length of roadside, and scale it
                    len_field = len_field,
                    inv_len_field=None,  # inv_len_field always None
                    vec_field=vec_field,   #laneline field means the increment along laneline, roadside field means the increment along roadside
                    inv_vec_field=inv_vec_field, #there is only laneline_inv_vector_field, which also means the increment along laneline, and there is no roadside_inv_vector_field, keep in mind that there two direction along the laneline
                    attr_field=output["attributes"][pn], #for lane type, lane color, and roadside type
                    line_mask=output["sigmoid_line_mask"][pn][pc], #segmentation results for laneline
                    bk=bk, #always None
                    dp=dp if dp is not None else None,
                    pc=pc, #pc=0 is laneline pc=1 is roadside
                )
            
        

            lane = dict(
                image_id=pn,
                label_id=pc,
                confidence=float(s),
                proposal=list(proposal),
                key_points=[],
                attribute=[],
            )
            if points is not None:
                attr_scores = np.stack(attr_scores).mean(0)
                lane.update({
                    "key_points": points,
                    "attribute": attr_group(attr_scores),
                })
            if (pn, pc) not in lines:
                lines[(pn, pc)] = []
            lines[(pn, pc)].append(lane)
    nms_lines = []
    for k in lines:
        nms_lines.extend(lines[k])
    return nms_lines

def parse_result(result_files, single_picture):
    if isinstance(result_files, str):
        result_files = [result_files]
    data = {}
    image_path = single_picture
    for res_file in result_files:
        data[image_path] = []
        for line in open(res_file):
            line = json.loads(line)
            #image_path = single_picture
            data.setdefault(image_path, [])
            data[image_path].append(line)

    return data

def relpath(file, img_prefix=""):
    if len(file) <= 0: return "", ""
    if "s3://" in file: # xxx:s3://bucket/relpath -> relpath
        prefix, file = file.split("s3://", 1)
        bucket, file = file.split("/", 1)
        prefix = f"{prefix}s3://{bucket}"
    else:
        prefix = img_prefix if len(img_prefix) > 0 else "/"
        # /prefix/relpath -> prefix/relpath  # /prefix/relpath -> relpath
        file = os.path.relpath(file, prefix)
    return prefix, file


def load_img(image_path, prefix=""):
    image_path = os.path.join(prefix, image_path)
    if "s3://" in image_path:
        img_bytes = client.Get(image_path, update_cache=False)
        assert img_bytes is not None, image_path
        img_mem_view = memoryview(img_bytes)
        img_array = np.frombuffer(img_mem_view, np.uint8)
        assert len(img_array) > 0, image_path
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
    else:
        img = cv2.imread(image_path)
    return img


def draw_roi_boxs(img, roi):
    t, l, h, w = roi[0],roi[1],roi[2],roi[3]  #top 540, left 0, h 1620, w 3840
    x_min,y_min,x_max,y_max= l,t,l+w,t+h
    cv2.rectangle(img,(x_min,y_min),(x_max,y_max),(255,0,255),4) #color B G R // thinkness
    return img


Lane_type = [
    "NO_LANE", "OTHER_LANE_TYPE", "SOLID_LANE",
    "DASHED_LANE", "SHORT_THICK_DASHED_LANE",
    "SPLIT_LANE", "MERGE_LANE",
    "LEFT_DASHED_RIGHT_SOLID", "LEFT_SOLID_RIGHT_DASHED",
    "DOUBLE_SOLID", "DOUBLE_DASHED",
    "FISHBONE_SOLID", "FISHBONE_DASHED", "SPLIT_MERGE_LANE",
    "INVALID_VALUE"
]


def argmax(data, keys=None):
    if isinstance(data, dict):
        data = [data[k] for k in keys]
    return int(np.argmax(data))



def draw_points(img, points, point_scores=[], color=(0, 0, 255)):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    # for pt in points:
    if point_scores != []: 
        point_scores = point_scores["scores"]
    for i, pt in enumerate(points):
        if point_scores != []:
            assert len(point_scores)==len(points), "bk_points is not equal to bk_scores"
            font = cv2.FONT_HERSHEY_DUPLEX
            text = format(point_scores[i],".2f") + " "+ str((int(pt[0]), int(pt[1])))
            if pt[1] > 1080 and point_scores[i] >= 0.75: # and bk_flag
                img = cv2.circle(img, (int(pt[0]), int(pt[1])), 16, color, 2)
                cv2.putText(img, text, (int(pt[0])+10, int(pt[1])+10), font, 1.0, color, 2)

    return img


def smooth_points(points):
    new_points = []
    x_1, y_1 = points[0][0], points[0][1]
    new_points.append([x_1, y_1])
    for i in range(1, len(points)-1):
        x_1, y_1 = points[i-1][0], points[i-1][1]
        x0, y0   = points[i][0], points[i][1]
        x1, y1   = points[i+1][0], points[i+1][1]
        x_m, y_m = round((x_1+x0+x1)/3.0), round((y_1+y0+y1)/3.0)
        new_points.append([x_m, y_m])
    x1, y1   = points[-1][0], points[-1][1]
    new_points.append([x1, y1])
    return new_points


Lane_type_cn = [
    u'无车道线', u'其他线型', u'实线', u'虚线', u'短粗线',
    u'分离线', u'合并线', u'左虚右实', u'左实右虚',
    u'双实线', u'双虚线', u'鱼骨实线', u'鱼骨虚线', u'分离合并线',
    u'无效属性'
]

def cv2AddChineseText(img, text, position, textColor=(0,255,0), textSize=30):
    if(isinstance(img, np.ndarray)):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw      = ImageDraw.Draw(img)   # 字体格式
    fontStyle = ImageFont.truetype("./simsun.ttc", textSize, encoding="utf-8")
    draw.text(position, text, textColor, font=fontStyle)     # 绘制文本
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)


def draw_lines(img, points, score=0.0, color=(0, 255, 0), lane_type=0, off_w=80, off_h=30):
    #print("points: ", points)
    im_h, im_w = img.shape[:2]
    if isinstance(points, dict): 
        points = [[_["x"], _["y"]] for _ in points["points"]]
    points = [[p[0], p[1]] for p in points if 0 <=p[0]< im_w and 0 <=p[1]< im_h]
    if len(points) > 2: 
        points = smooth_points(points)
    for i in range(0, len(points)-1):
        x0, y0 = round(points[i][0]), round(points[i][1])
        x1, y1 = round(points[i+1][0]), round(points[i+1][1])
        img = cv2.line(img, (x0, y0), (x1, y1), color, 2)
        #
        cv2.circle(img, (x0, y0), 5, (0,0,255), -1)

    # visual first point
    #print("points: ", points)
    if points ==[]:
        return img
    f_x0, f_y0 = round(points[-1][0]), round(points[-1][1])
    cv2.circle(img,(f_x0, f_y0), 5, (0,0,255), -1)
    # f_x0, f_y0 = f_x0 + 10, f_y0 + 10
    f_x0, f_y0 = f_x0 -250, f_y0 +300
    f_x0 = f_x0 if (im_w - f_x0 > off_w) else (im_w - off_w)
    f_y0 = f_y0 if (im_h - f_y0 > off_h) else (im_h - off_h)
    # # 图片对象、文本、像素、字体、字体大小、颜色、字体粗细*****cv2默认为BGR顺序
    # text = format(score,".2f") + " " + Lane_type_cn[lane_type]
    text = format(score,".5f") + "\n" + Lane_type_cn[lane_type]
    #print("f_x0: ", f_x0, "f_y0: ", f_y0)
    #print("text: ", text)
    img  = cv2AddChineseText(img, text, (f_x0, f_y0), (255, 0, 0), 15)

    return img


def draw_lines_roadside(img, points, color=(0, 255, 0)):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    for i in range(len(points) - 1):
        img = cv2.line(img, (int(points[i][0]), int(points[i][1])),
                (int(points[i + 1][0]), int(points[i + 1][1])), color, 4)
    return img

COLORS = [
    (255, 255, 0), (255, 0, 255), (255, 0, 0), (0, 255, 0), (0, 255, 255), (128, 255, 0),
    (255, 128, 0), (128, 0, 255), (255, 0, 128), (0, 128, 255), (0, 255, 128), (128, 255, 255),
    (255, 128, 255), (255, 255, 128), (60, 180, 0), (180, 60, 0), (0, 60, 180), (0, 180, 60),
    (60, 0, 180), (180, 0, 60), (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),(255, 0, 255),
    (0, 255, 255), (128, 255, 0), (255, 128, 0), (128, 0, 255),
]

def vis_result_pool(data):
    image_path, lines = data[0], data[1]
    prefix, image_path = relpath(image_path, img_prefix="")
    img = load_img(image_path, prefix)
    if len(lines) > 0:
        for idx, line in enumerate(lines):
            points = line["key_points"]
            if isinstance(points, dict): 
                points = [[_["x"], _["y"]] for _ in points["points"]]
            if len(points) < 2: 
                continue
            ####----draw efficient ROI----####
            roi = [line["roi"]["top"], line["roi"]["left"], line["roi"]["height"], line["roi"]["width"]]
            #img = draw_roi_boxs(img, roi)
            if line["label"] in [0, "LANELINE", "laneline"]:
                lane_type_data = line.get("attribute",{}).get("lane_type",[])
                if type(lane_type_data) is not int: 
                    type_len       = len(lane_type_data)
                    lane_type      = argmax(lane_type_data, Lane_type[:type_len])
                else: 
                    lane_type = lane_type_data  # for ground_truth.txt visual
                if "camera_0_" in image_path or "fov30" in image_path:
                    break_points = line.get("break_points", [])
                    img = draw_points(img, break_points, line.get("bk_scores", []))
                    img = draw_lines(img, points, line["confidence"], COLORS[lane_type], lane_type,\
                        off_w=100, off_h=60)
                else:
                    img = draw_lines(img, points[3:], line["confidence"], COLORS[lane_type], lane_type)
            else:
                #if "camera_2_" in image_path or "fov120" in image_path:
                    #img = draw_lines_roadside(img, line["key_points"], (0, 0, 255))
                img = draw_lines_roadside(img, line["key_points"], (0, 0, 255))
    
    subfolder = image_path.split('/')[-2]
    #print(image_path)
    #subfolder = image_path.split('\\')[-2]
    if subfolder in ["center_camera_fov120","center_camera_fov30","center_camera_fov30#s3"]:
        subfolder=image_path.split('/')[-3] + "_" + image_path.split('/')[-2]
    #out_path = os.path.join("./output_results", subfolder, os.path.basename(image_path))
    #os.makedirs(os.path.dirname(out_path), exist_ok=True)
    im_h, im_w = img.shape[:2]
    #print("before preprocessing image shape: ", img.shape)
    if im_h >= 1080: 
        #img = cv2.resize(img, (int(im_w/2), int(im_h/2)))
        img = cv2.resize(img, tuple(yaml_content["img_scale"]))
    quality = 100
    #cv2.imwrite(out_path, img, [cv2.IMWRITE_JPEG_QUALITY, quality])
    #print("displayed image shape: ", img.shape)
    #cv2.imshow("1", img)
    out_dir_name = f"frames_output_{yaml_content['img_scale'][0]}_{yaml_content['img_scale'][1]}"
    os.makedirs(out_dir_name, exist_ok=True)
    cv2.imwrite(f"{out_dir_name}/frame_output_{random.randint(0,20000)}.jpg", img)
    return 1

def softmax(scores, axis=-1):
    exp = np.exp(scores - np.max(scores, axis, keepdims=True))
    p = exp / np.sum(exp, axis, keepdims=True)
    return p


def attr_group(attr, with_softmax=True):
    attr = np.split(attr, np.array([13, 19]))
    if with_softmax:
        attr = list(map(softmax, attr))
    return attr



def fmt_pts(pts, roi=None):
    if roi is not None:
        l = roi["left"]
        t = roi["top"]
        r = l + roi["width"]
        b = t + roi["height"]
        points = [
            dict(x=x, y=y) for x, y in pts if l <= x <= r and t <= y <= b
        ]
    else:
        points = [dict(x=x, y=y) for x, y in pts]
    return {
        "$kesonType": "array",
        "points": points,
    }


def fmt_attr(attr, flag):
    assert flag in ['lane', 'side']
    if len(attr) <= 0:
        return {}
    ret = {}
    for idx, cate in enumerate(['lane_type', 'lane_color', 'roadside']):
        if flag in cate:
            ret[cate] = dict(zip({'lane_type': ['NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE', 'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE', 'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED', 'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID', 'FISHBONE_DASHED'], 
                                'lane_color': ['NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE', 'BLUE'], 
                                'roadside': ['NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE', 'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE']}[cate], attr[idx].tolist()))
    return ret


def fmt_proposals(proposal=[]):
    if len(proposal) <= 0:
        return proposal
    return [
        float(proposal[0]), proposal[1], proposal[2].tolist(), proposal[3]
    ],


def fix_points(lanes, roi, w, h, scale=1, off=True):
    ratio = [scale * roi["width"] / w, scale * roi["height"] / h]
    off = [roi["left"], roi["top"]] if off else 0

    def fn(points):
        if len(points) <= 0:
            return []
        return points * ratio + off

    for key in range(len(lanes)):
        if isinstance(lanes[key], dict):
            lanes[key]["key_points"] = fn(lanes[key]["key_points"])
            if "break_points" in lanes[key]:
                lanes[key]["break_points"] = fn(lanes[key]["break_points"])
            if "proposal" in lanes[key]:
                lanes[key]["proposal"][2] = fn(lanes[key]["proposal"][2])
        else:
            lanes[key] = fn(lanes[key])
    return lanes


def fmt_pred(lanes, roi, w, h, label, downscale):
    lanes = fix_points(lanes, roi, w, h, downscale)
    if label == "laneline":
        preds = [
            dict(
                label="LANELINE",
                roi=roi,
                confidence=lanes[i]["confidence"],
                key_points=fmt_pts(lanes[i]["key_points"]),
                attribute=fmt_attr(lanes[i]["attribute"], "lane"),
                proposal=fmt_proposals(lanes[i].get("proposal", [])),
            ) for i in range(len(lanes))
        ]
    else:
        preds = [
            dict(
                label="ROADSIDE",
                roi=roi,
                confidence=lanes[i]["confidence"],
                key_points=fmt_pts(lanes[i]["key_points"]),
                attribute=fmt_attr(lanes[i]["attribute"], "side"),
                proposal=fmt_proposals(lanes[i].get("proposal", [])),
            ) for i in range(len(lanes))
        ]
    return preds



def format_results(results, out_dir, image_path, image_prefix):
    image_path = image_path.split("\\")[-1]
    if out_dir is None: 
        out_dir = "result"
    os.makedirs(out_dir, exist_ok=True)
    
    results["pred"] = [results["pred"]]
    
    with open(os.path.join(out_dir, "eval_files/onnx_runner_result.txt"), "w") as f:
        for res in results["pred"]:
            for _ in res:
                _["image_path"] = image_path
                _["img_prefix"] = image_prefix
                f.write(json.dumps(_) + "\n")

if __name__ == "__main__":

    # 0. Prepare a list for images in "./frames" directory
    images_paths = get_images_paths()
    print(images_paths)
    average_time = 0
    ret_all = []
    for image_path in images_paths:
        #print("image_path: ", image_path)
        yaml_content = get_yaml_config()
        
        # 1. Load images and preprocess it:

        print("running preprocessing")
        preprocessed_img = preprocess_image(image_path, yaml_content["camera_roi"], model_input_shape=yaml_content["img_scale"])
        # 2. Process images with CondLane network:
        #print("preprocessed image shape: ", preprocessed_img.shape)
        print("running process_image")
        model_output_dict = process_images(preprocessed_img, path_onnx=yaml_content["path_onnx"])
        #print(model_output_dict)

        # 3. Post-process model's output
        #start = time.perf_counter_ns()
        #print("model_output_dict.keys(): ", model_output_dict.keys(), model_output_dict.__len__())
        print("running find_hot_points")
        proposal_indices, cls_score = find_hot_points(
            model_output_dict["heat_nms_weight_by_relu6_proposal_heatmap"]
        )
        mask_pos_tensor, mask_scores = find_hot_points(
            model_output_dict["heat_nms_weight_by_relu6_line_mask"]
        )
        model_output_dict["mask_pos_tensor"] = mask_pos_tensor
        model_output_dict["mask_scores"] = mask_scores
        
        #predictions = postprocess.process_v4_batch_size_1(proposal_indices, cls_score, model_output_dict)
        print("running process_v4_batch_size_2")
        predictions = process_v4_batch_size_2(proposal_indices, cls_score, model_output_dict)
        print("finished running process_v4_batch_size_2")
        #end = time.perf_counter_ns()
        #print(predictions)

        ret = []
        shape = (yaml_content["img_scale"][1], yaml_content["img_scale"][0], 3)
        scale = 4
        roi = {"top": 540*yaml_content["camera_roi"]["t"], 
               "left": 1680*yaml_content["camera_roi"]["l"],
               "width": 1680*(yaml_content["camera_roi"]["r"]-yaml_content["camera_roi"]["l"]),
               "height": 540*(yaml_content["camera_roi"]["b"]-yaml_content["camera_roi"]["t"]),
               "#keson_code": "RCT"}
        ret.append(dict(gt=[], pred=[]))
        for j, label in enumerate(["laneline", "roadside"]):
            dts = [_ for _ in predictions if _["image_id"] == 0 and _["label_id"] == j]
            dts = fmt_pred(dts, roi, shape[1], shape[0], label, scale)
            ret[-1]["pred"].extend(dts)
        #print("ret is: ", ret)
        #print("ret[0] is: ", ret[0])
        ret_all.append(ret)
        print("running format_results")
        format_results(ret[0], "./", image_path, r"./frames_ori")
        data = parse_result("eval_files/onnx_runner_result.txt", image_path)
        if len(data)!=0:
            new_list = [(key, val) for key, val in data.items()]
            vis_result_pool(new_list[0])

        #average_time = average_time + (end - start)
    #print(f"Average post_process time: {(average_time)/1000000/len(images_paths)} ms")
    #cv2.destroyAllWindows()
    #print('ret_all is: ', ret_all)
