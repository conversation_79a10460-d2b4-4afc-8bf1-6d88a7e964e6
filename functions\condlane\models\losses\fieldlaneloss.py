import ipdb
import torch
from torch.nn import functional as F
import numpy as np
import matplotlib.pyplot as plt
import os
import random

from pillar.models import MODELS
from .condlaneloss import CondLaneLoss, Hsigmoid, GroupFocalCrossLoss


def smooth_l1_loss(diff, beta=1. / 9):
    cond = diff < beta
    loss = torch.where(
        cond, 0.5 * (diff ** 2) / beta, diff - 0.5 * beta
    )
    return loss


@MODELS.register_module()
class FieldLaneLoss(CondLaneLoss):

    def __init__(
        self,
        weights,
        laneline_indices={},
        roadside_indices={},
        gt_indices={},
        property_group=[13, 6, 6],
        ignore_index=255,
        alone_ms=False,
        split_lane_side=False,
    ):
        self.view_distance = weights.pop("view_distance", 1)
        self.inv_view_distance = weights.pop("inv_view_distance", 0)
        self.smooth_weight = weights.pop("smooth_weight", 0.0)
        self.len_weight = weights.get("len_loss_weight", 1.0)
        self.field_weight = weights.get("field_loss_weight", 1.0)
        self.view_weight = weights.get("view_loss_weight", 1.0)
        self.st_off_weight = weights.get("st_off_weight", 64)
        self.num_ref_point = weights.get("num_ref_point", 1)
        self.length_scale = weights.get("length_scale", 10)     # None: ReLU6 / 6;  constant;
        self.field_scale = weights.get("field_scale", None)
        self.reg_smooth_l1 = weights.get("reg_smooth_l1", False)
        self.mask_bce_weight = weights.get("mask_bce_weight", None)
        if self.mask_bce_weight is not None:
            assert isinstance(self.mask_bce_weight, list)
            self.mask_bce_weight = torch.tensor(self.mask_bce_weight)
            self.crit_au_mask_bce = FocalBCEWithLogitsLoss(
                pos_weight=self.mask_bce_weight)

        # balance weight
        self.mask_bg_weight = weights.get("mask_bg_weight", None)  # for lane & roadside
        if self.mask_bg_weight is not None and not isinstance(
                self.mask_bg_weight, list):
            self.mask_bg_weight = [self.mask_bg_weight] * 2
        self.length_bg_weight = weights.get("length_bg_weight", [-1, -1])
        if not isinstance(self.length_bg_weight, list):
            self.length_bg_weight = [self.length_bg_weight] * 2
        self.field_bg_weight = weights.get("field_bg_weight", [-1, -1])
        if not isinstance(self.field_bg_weight, list):
            self.field_bg_weight = [self.field_bg_weight] * 2
        self.attr_bg_weight = weights.get("attr_bg_weight", -1)

        super().__init__(weights, property_group, ignore_index)
        if len(laneline_indices) <= 0:
            laneline_indices = dict(
                length=(27, 28),
                field=(29, 30),
                offset=(30, 31),
            )
        self.laneline_indices = laneline_indices
        if len(roadside_indices) <= 0:
            roadside_indices = dict(
                length=(28, 29),
                field=(31, 32),
                offset=(32, 33),
            )
        self.roadside_indices = roadside_indices
        if len(gt_indices) <= 0:
            gt_indices = dict(
                offset=(0, 1),
                field=(1, 2),
                length=(2, 3),
                weight=(3, 4),
            )
        self.gt_indices = gt_indices
        self.split_lane_side = split_lane_side
        self.alone_ms = alone_ms
        attr_weights = [
            weights.pop("lane_type_weight", None),
            weights.pop("lane_color_weight", None),
            weights.pop("roadside_weight", None),
        ]
        if self.alone_ms:
            assert len(property_group) == 4, "merge&split should be an alone dimension."
            attr_weights.append(weights.pop("lane_ms_weight", attr_weights[0]))
        self.crit_ce_attrs = GroupFocalCrossLoss(
            groups=property_group,
            weights=attr_weights,
            ignore_index=ignore_index,
        )

    def parse_indices(self, tensor, indices, dim=1):
        ret = {}
        for k, start in indices.items():
            if isinstance(start, (list, tuple)):
                start, stop = start
            else:
                stop = start + 1
            if start >= tensor.shape[dim]:
                continue
            index = torch.arange(start, stop).to(tensor.device)
            ret[k] = torch.index_select(tensor, dim, index)
        return ret

    def remap_property_ms(self, line_type_map):
        n, c, h, w = line_type_map.shape
        if c == 13:      # pred
            line_ms_map = torch.zeros((n, 3, h, w),
                                      dtype=line_type_map.dtype,
                                      device=line_type_map.device)
            line_ms_map[:, 1:, ...] = line_type_map[:, 5:7, ...]
            line_type_map = torch.cat([line_type_map[:, :5, ...], line_type_map[:, 7:, ...]], dim=1)
        else:   # gt
            line_ms_map = torch.zeros_like(line_type_map)
            split_index = line_type_map == 5
            merge_index = line_type_map == 6
            line_ms_map[split_index] = 1
            line_ms_map[merge_index] = 2
            line_type_map[split_index | merge_index] = 2
            line_type_map[(line_type_map > 6) & (line_type_map < 13)] -= 2
            ignore_index = line_type_map >= 13
            line_ms_map[ignore_index] = line_type_map[ignore_index]
        return line_type_map, line_ms_map

    def loss_field_reg(self, preds, gts, idx):
        ret = {
            "len_loss": torch.tensor(0).float().cuda(),
            "field_loss": torch.tensor(0).float().cuda()
        }
        if self.view_distance > 1:
            ret["view_loss"] = torch.tensor(0).float().cuda()
        if self.smooth_weight > 0:
            ret["smooth_loss"] = torch.tensor(0).float().cuda()
        if self.inv_view_distance > 0:
            ret["inv_len_loss"] = torch.tensor(0).float().cuda()
            ret["inv_field_loss"] = torch.tensor(0).float().cuda()

        gt_mask = gts["gt_mask"]
        pos_index = gt_mask > 0
        if not pos_index.any():
            return ret
        pos_index = pos_index.squeeze(1)
        H, W = pos_index.shape[-2:]

        # len loss: l1 loss todo: enlarge len loss
        if preds["length"] is not None:
            len_weight = gts["length_weight"]
            if self.length_scale is None:
                len_pred = preds["length"].sigmoid()
                len_gt = gts["length"] / H
            elif self.length_scale > 0:
               len_pred = preds["length"]
               len_gt = gts["length"] / (H / self.length_scale)
            else:
                len_pred = Hsigmoid(preds["length"])
                len_gt = gts["length"] / H
            len_loss = torch.abs(len_pred - len_gt)
            if self.reg_smooth_l1:
                beta = np.clip(0.01 * self.length_scale, 0.05, 0.1) if self.length_scale is not None else 0.05
                len_loss = smooth_l1_loss(len_loss, beta=beta)
            if self.length_bg_weight[idx] > 0:
                len_loss = len_loss.permute(0, 2, 3, 1)
                len_weight = len_weight.permute(0, 2, 3, 1)
                len_loss_pos = (len_loss * len_weight)[pos_index].mean()
                len_loss_neg = (len_loss * len_weight)[~pos_index].mean()
                len_loss = len_loss_pos + len_loss_neg * self.length_bg_weight[idx]
            else:
                len_loss = (len_loss * len_weight).sum() / ((gt_mask * len_weight).sum() + 1)   # todo: mean?
            ret["len_loss"] = len_loss * self.len_weight

        # field loss: l1 loss
        field_weight = gts["field_weight"]
        field_preds = preds["field"]
        field_gts = gts["field"]
        if self.field_scale is not None:
            field_gts = field_gts / (W / self.field_scale)
        if preds["offset"] is not None:
            field_weight = torch.cat([gts["offset_weight"], field_weight], 1)
            field_preds = torch.cat([preds["offset"], field_preds], 1)
            offset_gt = gts["offset"]
            if self.field_scale is not None:
                offset_gt = offset_gt / (W / self.field_scale)
            field_gts = torch.cat([offset_gt, field_gts], 1)
        field_loss = torch.abs(field_preds - field_gts)
        if self.reg_smooth_l1:
            beta = 1.0 / (W / self.field_scale) if self.field_scale is not None else 0.1
            field_loss = smooth_l1_loss(field_loss, beta=beta)
        if self.field_bg_weight[idx] > 0:
            field_loss = field_loss.permute(0, 2, 3, 1)
            field_weight = field_weight.permute(0, 2, 3, 1)
            field_loss_pos = (field_loss * field_weight)[pos_index].mean()
            field_loss_neg = (field_loss * field_weight)[~pos_index].mean()
            field_loss = field_loss_pos + field_loss_neg * self.field_bg_weight[idx]
        else:
            field_loss = (field_loss * field_weight).sum() / (field_weight.sum() + 1)   # todo: mean?
        ret["field_loss"] = field_loss * self.field_weight

        # view loss: l1 loss todo: merge into field loss
        if self.view_distance > 1:
            view_pred = preds.get("view", None)
            view_gt = gts.get("view", None)
            view_weight = gts.get("view_weight", None)
            if view_pred is not None and view_gt is not None and view_weight is not None:
                if self.field_scale is not None:
                    view_gt = view_gt / (W / self.field_scale)
                if view_weight.any():
                    view_loss = torch.abs(view_pred - view_gt)
                    if self.reg_smooth_l1:
                        beta = 1.0 / (W / self.field_scale) if self.field_scale is not None else 0.1
                        view_loss = smooth_l1_loss(view_loss, beta=beta)
                    if self.field_bg_weight[idx] > 0:
                        view_loss = view_loss.permute(0, 2, 3, 1)
                        view_weight = view_weight.permute(0, 2, 3, 1)
                        view_loss_pos = (view_loss * view_weight)[pos_index].mean()
                        view_loss_neg = (view_loss * view_weight)[~pos_index].mean()
                        view_loss = view_loss_pos + view_loss_neg * self.field_bg_weight[idx]
                    else:
                        view_loss = (view_loss * view_weight).sum() / (view_weight.sum() + 1)  # todo: mean?
                    ret["view_loss"] = view_loss * self.view_weight

        if self.smooth_weight > 0:
            smooth_loss = (field_preds * field_weight)**2
            smooth_loss = smooth_loss.sum() / field_weight.sum()
            ret["smooth_loss"] = smooth_loss * self.smooth_weight

        if self.inv_view_distance > 0:
            inv_field_weight = gts.get("inv_field_weight", None)
            if inv_field_weight is not None and inv_field_weight[:, :1].any():
                inv_length_gt = gts.get("inv_length", None)
                inv_length = preds.get("inv_length", None)

                # inv length loss: l1 loss
                if inv_length_gt is not None and inv_length is not None:
                    inv_len_weight = gts.get("inv_length_weight")
                    if self.length_scale is None:
                        inv_len_pred = inv_length.sigmoid()
                        inv_len_gt = inv_length_gt / H
                    elif self.length_scale > 0:
                        inv_len_pred = inv_length
                        inv_len_gt = inv_length_gt / (H / self.length_scale)
                    else:
                        inv_len_pred = Hsigmoid(inv_length)
                        inv_len_gt = inv_length_gt / H
                    inv_len_loss = torch.abs(inv_len_pred - inv_len_gt)
                    if self.reg_smooth_l1:
                        beta = np.clip(0.01 * self.length_scale, 0.05, 0.1) if self.length_scale is not None else 0.05
                        inv_len_loss = smooth_l1_loss(inv_len_loss, beta=beta)
                    if self.length_bg_weight[idx] > 0:
                        inv_len_loss = inv_len_loss.permute(0, 2, 3, 1)
                        inv_len_weight = inv_len_weight.permute(0, 2, 3, 1)
                        inv_len_loss_pos = (inv_len_loss * inv_len_weight)[pos_index].mean()
                        inv_len_loss_neg = (inv_len_loss * inv_len_weight)[~pos_index].mean()
                        inv_len_loss = inv_len_loss_pos + inv_len_loss_neg * self.length_bg_weight[idx]
                    else:
                        inv_len_loss = (inv_len_loss * inv_len_weight).sum() / ((gt_mask * inv_len_weight).sum() + 1)   # todo: mean?
                    ret["inv_len_loss"] = inv_len_loss * self.len_weight

                # inv vector field loss: l1 loss
                inv_vector_field_gt = gts.get("inv_vector_field", None)
                inv_vector_field = preds.get("inv_vector_field", None)
                if self.field_scale is not None:
                    inv_vector_field_gt = inv_vector_field_gt / (
                        W / self.field_scale)
                    inv_vector_field = inv_vector_field / (W /
                                                           self.field_scale)
                if inv_vector_field_gt is not None and inv_vector_field is not None:
                    inv_field_loss = torch.abs(inv_vector_field - inv_vector_field_gt)
                    if self.reg_smooth_l1:
                        beta = 1.0 / (W / self.field_scale) if self.field_scale is not None else 0.1
                        inv_field_loss = smooth_l1_loss(inv_field_loss, beta=beta)
                    if self.field_bg_weight[idx] > 0:
                        inv_field_loss = inv_field_loss.permute(0, 2, 3, 1)
                        inv_field_weight = inv_field_weight.permute(0, 2, 3, 1)
                        inv_field_loss_pos = (inv_field_loss * inv_field_weight)[pos_index].mean()
                        inv_field_loss_neg = (inv_field_loss * inv_field_weight)[~pos_index].mean()
                        inv_field_loss = inv_field_loss_pos + inv_field_loss_neg * self.field_bg_weight[idx]
                    else:
                        inv_field_loss = (inv_field_loss * inv_field_weight).sum() / (inv_field_weight.sum() + 1)   # todo: mean?
                    ret["inv_field_loss"] = inv_field_loss * self.field_weight
        return ret

    def loss_mask_cls(self, aux_mask, lane_gt, side_gt, lane_valid, side_valid):
        if isinstance(aux_mask, dict):
            line_mask = aux_mask['line_mask'][:, :2]
        else:
            line_mask = aux_mask[:, :2]
        if self.split_lane_side:
            lane_mask = line_mask[:, :1]
            side_mask = line_mask[:, 1:]
            if lane_valid.any():
                laneline_mask_loss = self.crit_au_mask_bce(lane_mask, lane_gt["gt_mask"].bool(),
                                                           self.mask_bg_weight[:1])
                laneline_mask_loss = laneline_mask_loss * self.aux_weight[0]
            else:
                laneline_mask_loss = torch.tensor(0).float().cuda()
            if side_valid.any():
                roadside_mask_loss = self.crit_au_mask_bce(side_mask, side_gt["gt_mask"].bool(),
                                                           self.mask_bg_weight[1:])
                roadside_mask_loss = roadside_mask_loss * self.aux_weight[0]
            else:
                roadside_mask_loss = torch.tensor(0).float().cuda()
            mask_loss = {
                "laneline_mask_loss": laneline_mask_loss,
                "roadside_mask_loss": roadside_mask_loss
            }
        else:
            line_mask_target = torch.cat(
                [lane_gt["gt_mask"], side_gt["gt_mask"]], dim=1).bool()
            line_mask_loss = self.crit_au_mask_bce(line_mask, line_mask_target,
                                                   self.mask_bg_weight)
            mask_loss = {
                "mask_loss": line_mask_loss * self.aux_weight[0]
            }
        return mask_loss

    def init_field_loss(self):
        ret = {
            "len_loss": torch.tensor(0).float().cuda(),
            "field_loss": torch.tensor(0).float().cuda()
        }
        if self.view_distance > 1:
            ret["view_loss"] = torch.tensor(0).float().cuda()
        if self.smooth_weight > 0:
            ret["smooth_loss"] = torch.tensor(0).float().cuda()
        if self.inv_view_distance > 0:
            ret["inv_len_loss"] = torch.tensor(0).float().cuda()
            ret["inv_field_loss"] = torch.tensor(0).float().cuda()
        return ret

    def forward(self, output, spatial_output, meta, **kwargs):
        aux_mask = output["aux_mask"]
        laneline_valid = ~kwargs["ignore_laneline"]
        roadside_valid = ~kwargs["ignore_roadside"]
        losses = {}

        if "proposal_heatmap" in output.keys():
            # heatmap loss：focal loss
            laneline_gt_hm = kwargs["laneline_gt_hm"]
            roadside_gt_hm = kwargs["roadside_gt_hm"]

            laneline_hm_pred = output["proposal_heatmap"]
            laneline_hm_loss, laneline_info = self.loss_headmap(
                laneline_hm_pred[:, :1, :, :],
                laneline_gt_hm,
                laneline_valid,
            )
            if laneline_hm_loss is not None:
                losses["laneline_hm_loss"] = laneline_hm_loss
            else:
                losses["laneline_hm_loss"] = torch.tensor(0).float().cuda()
            if laneline_info is not None:
                self.logger.info("laneline pred/gt: %d/%d" % laneline_info)

            roadside_hm_loss, roadside_info = self.loss_headmap(
                laneline_hm_pred[:, 1:, :, :],
                roadside_gt_hm,
                roadside_valid,
            )
            if roadside_hm_loss is not None:
                losses["roadside_hm_loss"] = roadside_hm_loss
            else:
                losses["roadside_hm_loss"] = torch.tensor(0).float().cuda()
            if roadside_info is not None:
                self.logger.info("roadside pred/gt: %d/%d" % roadside_info)

            # start offset loss: l1 loss todo: not norm?
            if "proposal_offset" in output.keys():
                laneline_offset_gt = kwargs["laneline_start_point_off"]
                roadside_offset_gt = kwargs["roadside_start_point_off"]

                proposal_offset_pred = output['proposal_offset']
                laneline_st_off_loss = proposal_offset_pred[
                    laneline_valid, :2].tanh(
                    ) - laneline_offset_gt[laneline_valid]
                laneline_gt_hm = laneline_gt_hm[laneline_valid]
                laneline_st_off_loss = torch.abs(laneline_st_off_loss *
                                                 laneline_gt_hm).sum() / (laneline_gt_hm.sum() + 1)
                if self.reg_smooth_l1:
                    laneline_st_off_loss = smooth_l1_loss(laneline_st_off_loss, beta=0.25)
                losses["laneline_st_off_loss"] = laneline_st_off_loss * self.st_off_weight

                roadside_st_off_loss = proposal_offset_pred[
                    roadside_valid,
                    2:4].tanh() - roadside_offset_gt[roadside_valid]
                roadside_gt_hm = roadside_gt_hm[roadside_valid]
                roadside_st_off_loss = torch.abs(roadside_st_off_loss *
                                                 roadside_gt_hm).sum() / (roadside_gt_hm.sum() + 1)
                if self.reg_smooth_l1:
                    roadside_st_off_loss = smooth_l1_loss(roadside_st_off_loss, beta=0.25)
                losses["roadside_st_off_loss"] = roadside_st_off_loss * self.st_off_weight

        gts_lane = kwargs["laneline_field"]
        gts_rs = kwargs["roadside_field"]

        # mask loss
        mask_loss = self.loss_mask_cls(aux_mask, gts_lane, gts_rs, laneline_valid, roadside_valid)
        losses.update(mask_loss)

        # attr loss: focal loss
        line_type_mask = kwargs["laneline_attr"][:, 0:1, ...]
        line_color_mask = kwargs["laneline_attr"][:, 1:, ...]
        side_attr_mask = kwargs["roadside_attr"]
        line_ms_mask = torch.tensor([], device=line_type_mask.device)
        if self.alone_ms:
            line_type_mask, line_ms_mask = self.remap_property_ms(line_type_mask)
        stacked_masks = torch.cat([line_type_mask, line_color_mask, side_attr_mask, line_ms_mask], dim=1)
        if not isinstance(aux_mask, dict):
            line_type_pred = aux_mask[:, 2:15]
            line_color_pred = aux_mask[:, 15:21]
            side_type_pred = aux_mask[:, 21:27]
            line_ms_pred = torch.tensor([], device=line_type_pred.device)
            if self.alone_ms:
                line_type_pred, line_ms_pred = self.remap_property_ms(line_type_pred)
        else:
            line_type_pred = aux_mask["laneline_type"]
            line_color_pred = aux_mask["laneline_color"]
            side_type_pred = aux_mask["roadside_type"]
            line_ms_pred = aux_mask.get("laneline_ms", torch.tensor([], device=line_type_pred.device))
        attr_pred = torch.cat([line_type_pred, line_color_pred, side_type_pred, line_ms_pred], dim=1)

        attr_masks = [None] * len(self.property_group)
        if self.attr_bg_weight >= 0:
            H, W = attr_pred.shape[-2:]
            laneline_mask = kwargs["laneline_field"]["gt_mask"]
            laneline_mask = F.adaptive_max_pool2d(laneline_mask, (H, W))
            laneline_mask = laneline_mask.squeeze(1) > 0
            roadside_mask = kwargs["roadside_field"]["gt_mask"]
            roadside_mask = F.adaptive_max_pool2d(roadside_mask, (H, W))
            roadside_mask = roadside_mask.squeeze(1) > 0
            attr_masks = [laneline_mask, laneline_mask, roadside_mask]
            if self.alone_ms:
                attr_masks += [laneline_mask]
        loss_attrs = self.loss_attrs(attr_pred, stacked_masks, attr_masks, self.attr_bg_weight) #look here 1
        losses.update(loss_attrs)

        # length + vector + offset + view + smooth loss: l1 loss
        laneline_field_losses = self.init_field_loss()
        if laneline_valid.any():
            if isinstance(aux_mask, dict):
                field_preds = dict(
                    length=aux_mask["laneline_length"][laneline_valid] if "laneline_length" in aux_mask else None,
                    field=aux_mask["laneline_field"][laneline_valid] if "laneline_field" in aux_mask else None,
                    offset=aux_mask["laneline_offset"][laneline_valid] if "laneline_offset" in aux_mask else None,
                )
                if "laneline_view" in aux_mask:
                    field_preds["view"] = aux_mask["laneline_view"][laneline_valid]
                if 'laneline_inv_vector_field' in aux_mask:
                    field_preds['inv_vector_field'] = aux_mask[
                        'laneline_inv_vector_field'][laneline_valid]
                if 'laneline_inv_length' in aux_mask:
                    field_preds['inv_length'] = aux_mask[
                        'laneline_inv_length'][laneline_valid]
            else:
                field_preds = self.parse_indices(
                    aux_mask[laneline_valid],
                    self.laneline_indices,
                )
            valid_gts = {}
            for k in gts_lane:
                valid_gts[k] = gts_lane[k][laneline_valid]
            laneline_field_losses = self.loss_field_reg(field_preds, valid_gts, idx=0)
        for k, v in laneline_field_losses.items():
            losses[f"laneline_{k}"] = v

        roadside_field_losses = self.init_field_loss()
        if roadside_valid.any():
            if isinstance(aux_mask, dict):
                field_preds = dict(
                    length=aux_mask["roadside_length"][roadside_valid] if "roadside_length" in aux_mask else None,
                    field=aux_mask["roadside_field"][roadside_valid] if "roadside_field" in aux_mask else None,
                    offset=aux_mask["roadside_offset"][roadside_valid] if "roadside_offset" in aux_mask else None,
                )
                if "roadside_view" in aux_mask:
                    field_preds["view"] = aux_mask["roadside_view"][roadside_valid]
                if 'roadside_inv_vector_field' in aux_mask:
                    field_preds['inv_vector_field'] = aux_mask[
                        'roadside_inv_vector_field'][roadside_valid]
                if 'roadside_inv_length' in aux_mask:
                    field_preds['inv_length'] = aux_mask[
                        'roadside_inv_length'][roadside_valid]
            else:
                field_preds = self.parse_indices(
                    aux_mask[roadside_valid],
                    self.roadside_indices,
                )
            valid_gts = {}
            for k in gts_rs:
                valid_gts[k] = gts_rs[k][roadside_valid]
            roadside_field_losses = self.loss_field_reg(field_preds, valid_gts, idx=1)
        for k, v in roadside_field_losses.items():
            losses[f"roadside_{k}"] = v
        for k, v in losses.items():
            if torch.isinf(v).any() or torch.isnan(v).any():
                self.logger.error(f"[inf/nan]{k}:{v.item()}")
                losses[k] = torch.tensor(0).float().cuda()
                
        gt_depth = kwargs.get("depth_map", None)
        #print(f"shape of gt_depth is {gt_depth.shape}")
       # print(f"type of gt_depth is {type(gt_depth)}")
        if gt_depth is not None:
            depth_pred = spatial_output
            #target_H, target_W = gt_depth.shape[2], gt_depth.shape[3]
            target_H, target_W = 288, 864
            depth_upsampled = F.interpolate(depth_pred, size=(target_H, target_W),
                                          mode='bilinear', align_corners=False)
            depth_loss = F.smooth_l1_loss(depth_upsampled, gt_depth)
            losses["depth_loss"] = depth_loss
            
            #saving pred image                              
            #pred = depth_upsampled[0, 0].detach().cpu().numpy()  # shape: (H, W)
            #pred = (pred - pred.min()) / (pred.max() - pred.min() + 1e-8) #normalize
            #save_path = os.path.join("./preds", f"{random.randint(0,50000)}.png")
            #os.makedirs("./preds", exist_ok=True)
            #plt.imsave(save_path, pred, cmap="viridis")

        return losses
