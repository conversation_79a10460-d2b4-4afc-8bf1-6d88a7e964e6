import ipdb
import torch
from torch import nn

from mmdet.models.losses import Accuracy
from mmdet.models.losses.focal_loss import py_sigmoid_focal_loss
from mme.utils import get_root_logger
from pillar.models import MODELS


@MODELS.register_module()
class FocalLoss(nn.Module):

    def __init__(
        self,
        gamma=2.0,
        alpha=0.5,
        reduction='mean',
        loss_weight=1.0,
    ):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction
        self.loss_weight = loss_weight

    def forward(
        self,
        pred,
        target,
        weight=None,
        avg_factor=None,
        reduction_override=None,
    ):
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = (reduction_override
                     if reduction_override else self.reduction)
        loss_cls = self.loss_weight * py_sigmoid_focal_loss(
            pred,
            target,
            weight,
            gamma=self.gamma,
            alpha=self.alpha,
            reduction=reduction,
            avg_factor=avg_factor,
        )
        return loss_cls


@MODELS.register_module()
class MaskLoss(nn.Module):

    def __init__(self, criterions, line_type, alpha=0.1, mix_mask=True):
        super().__init__()
        self.alpha = alpha
        self.line_type = line_type
        self.mix_mask = mix_mask
        self.criterions = {
            f"{self.line_type}_{k}": MODELS.build(v)
            for k, v in criterions.items()
        }
        self.logger = get_root_logger()

    def forward(self, output, metas={}, **kwargs):
        losses = {
            k + "_loss": torch.tensor(0).float().cuda()
            for k in self.criterions
        }
        valid = ~kwargs[f"ignore_{self.line_type}"]
        if not valid.any():
            return losses
        # weight = kwargs[f"{self.line_type}_x_weight"][valid].flatten()
        mask_pos = kwargs[f"{self.line_type}_mask"]
        if self.mix_mask:
            mask_pos = kwargs["mix_mask"]
        mask_pos = mask_pos[valid].flatten()
        mask_neg = ~mask_pos
        for k, crit in self.criterions.items():
            gt = kwargs[k][valid].flatten()
            hm = output[k][valid].flatten()
            self.logger.info(
                f"{self.line_type} {(hm > 0).sum()} {(gt > 0.5).sum()}")
            loss = crit(
                hm,
                gt,
                # weight,
                reduction_override="none",
            )
            loss_pos = loss[mask_pos].mean()
            loss_neg = loss[mask_neg].mean()
            losses[k + "_loss"] = loss_pos + loss_neg * self.alpha
        return losses


@MODELS.register_module()
class LocLoss(nn.Module):

    def __init__(self, criterions, line_type, alpha=0.1):
        super().__init__()
        self.alpha = alpha
        self.line_type = line_type
        self.criterions = {
            f"{self.line_type}_{k}": MODELS.build(v)
            for k, v in criterions.items()
        }

    def forward(self, output, metas={}, **kwargs):
        losses = {
            k + "_loss": torch.tensor(0).float().cuda()
            for k in self.criterions
        }
        valid = ~kwargs[f"ignore_{self.line_type}"]
        if not valid.any():
            return losses
        weight = kwargs[f"{self.line_type}_weight"][valid]
        mask_pos = kwargs[f"{self.line_type}_mask"][valid].squeeze(1)
        mask_neg = ~mask_pos
        for k, crit in self.criterions.items():
            gt = kwargs[k][valid]
            pred = output[k][valid]
            loss = crit(pred, gt, weight, reduction_override="none")
            loss = loss.permute(0, 2, 3, 1)
            loss_pos = loss[mask_pos].mean()
            loss_neg = loss[mask_neg].mean()
            losses[k + "_loss"] = loss_pos + loss_neg * self.alpha
        return losses


@MODELS.register_module()
class AttrLoss(nn.Module):

    def __init__(self, criterions, line_type, alpha=0.1):
        super().__init__()
        self.alpha = alpha
        self.line_type = line_type
        self.criterions = {
            f"{self.line_type}_{k}": MODELS.build(v)
            for k, v in criterions.items()
        }
        self.acc = Accuracy()

    def forward(self, output, metas={}, **kwargs):
        losses = {
            k + "_loss": torch.tensor(0).float().cuda()
            for k in self.criterions
        }
        losses.update({
            k + "_acc": torch.tensor(0).float().cuda()
            for k in self.criterions
        })
        valid = ~kwargs[f"ignore_{self.line_type}"]
        if not valid.any():
            return losses
        mask_pos = kwargs[f"{self.line_type}_mask_attr"][valid].flatten()
        mask_neg = ~mask_pos
        for k, crit in self.criterions.items():
            gt = kwargs[k][valid].long().flatten()
            pred = output[k][valid]
            pred = pred.permute(0, 2, 3, 1)
            pred = pred.reshape(gt.shape[0], -1)
            loss = crit(pred, gt, reduction_override="none")
            loss_pos = loss[mask_pos].mean()
            loss_neg = loss[mask_neg].mean()
            losses[k + "_loss"] = loss_pos + loss_neg * self.alpha
            with torch.no_grad():
                losses[k + "_acc"] = self.acc(pred[mask_pos], gt[mask_pos])[0]
        return losses


@MODELS.register_module()
class FieldLaneLossBeta(nn.Module):

    def __init__(
        self,
        laneline=[],
        roadside=[],
    ):
        super().__init__()
        criterions = []
        criterions.extend(
            [MODELS.build(dict(**v, line_type="laneline")) for v in laneline])
        criterions.extend(
            [MODELS.build(dict(**v, line_type="roadside")) for v in roadside])
        self.criterions = nn.ModuleList(criterions)
        self.logger = get_root_logger()

    def forward(self, output, metas={}, **kwargs):
        losses = {}
        for v in self.criterions:
            losses.update(v(output, **kwargs))

        for k, v in losses.items():
            if torch.isinf(v).any() or torch.isnan(v).any():
                self.logger.error(f"[inf/nan]{k}:{v.item()}")
                losses[k] = torch.tensor(0).float().cuda()

        return losses
