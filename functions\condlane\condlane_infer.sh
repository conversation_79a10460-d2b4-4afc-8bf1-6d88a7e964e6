# sh condlane_infer.sh ad_rd 8
#!/usr/bin/env bash

set -x

sh_file_dir=$(cd "$(dirname "$0")"; pwd)
echo ${sh_file_dir}
now=$(date +"%Y%m%d_%H%M%S")

## 123
WORK_DIR="./visual_res/condlane_one_stage_$now"
CONFIG="./configs/config_r331_one_stage_11.6MB_finetuning.py"
#CHECKPOINT="./checkpoint/condlane_with_mobilenetv3_backbone_ftuned_for_100epochs.pth"
CHECKPOINT="/home/<USER>/condlane/condlane_checkpoints/ftuned_model_all_batches_exp1.pth"

ROOT="/home/<USER>/condlane/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage"
export PYTHONPATH=$ROOT:$ROOT/functions:$PYTHONPATH

python -u $ROOT/tools/test.py                       \
	${CONFIG} ${CHECKPOINT}                        \
	--work-dir=${WORK_DIR}/                         \
	--eval ""                                       \
	--launcher="none"                               \
	2>&1 | tee "${WORK_DIR}.log"

 ##condlane_vis_result.sh
 prefix=""
 WORKERS='12'
 echo ${data}
 python -u $sh_file_dir/lane_evalutation/condlane_vis_result_bk.py     \
	 ${WORK_DIR}/runner_result.txt                    \
	 --outdir ${WORK_DIR}/                            \
	 --workers ${WORKERS}
