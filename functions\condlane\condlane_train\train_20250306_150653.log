connect clearml server error: clearml server is unreachable: 1536, turn off clearml logger hook
Namespace(algorithm_backend='OpenMMLab', allow_dead_parameter=False, asynchronize=False, cfg_options=None, config='./configs/config_r331_one_stage_11.6MB_finetuning.py', ddp=False, deterministic=False, display=20, evaluate=False, fork_method='fork', function=None, gpu_ids=None, gpus=None, launcher='none', local_rank=0, no_running_config=False, no_validate=False, nocudnn=False, options=None, opts=None, petrel_conf_path='~/petreloss.conf', phase='train', resume_from=None, seed=None, sync_bn=False, test_gpu=False, work_dir='./condlane_train/')
fatal: not a git repository (or any of the parent directories): .git
2025-03-06 15:07:07,577 - mme - INFO - Environment info:
------------------------------------------------------------
sys.platform: linux
Python: 3.6.9 |Anaconda, Inc.| (default, Jul 30 2019, 19:07:31) [GCC 7.3.0]
CUDA available: True
GPU 0,1: NVIDIA GeForce RTX 3090
CUDA_HOME: /usr/local/cuda
NVCC: Build cuda_12.1.r12.1/compiler.32415258_0
GCC: gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
PyTorch: 1.8.1+cu111
PyTorch compiling details: PyTorch built with:
  - GCC 7.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v1.7.0 (Git Hash 7aed236906b1f7a05c0917e5257a1af05e9ff683)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.1
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.0.5
  - Magma 2.5.2
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.1, CUDNN_VERSION=8.0.5, CXX_COMPILER=/opt/rh/devtoolset-7/root/usr/bin/c++, CXX_FLAGS= -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-sign-compare -Wno-unused-parameter -Wno-unused-variable -Wno-unused-function -Wno-unused-result -Wno-unused-local-typedefs -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.8.1, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, 

TorchVision: 0.9.1+cu111
OpenCV: 4.5.1
MMCV: 1.4.8
MMCV Compiler: GCC 7.3
MMCV CUDA Compiler: 11.1
MMEnterprise: 0.2.1+
------------------------------------------------------------

2025-03-06 15:07:09,176 - mme - INFO - Distributed training: False
2025-03-06 15:07:10,829 - mme - INFO - Config:
custom_imports = dict(imports=[
    'condlane.datasets', 'condlane.datasets.pipelines',
    'condlane.models.backbones', 'condlane.models.necks',
    'condlane.models.dense_heads', 'condlane.models.losses',
    'condlane.models.predictors'
])
test_setting = dict(
    repo='pillar',
    single_gpu_test=dict(show=False),
    multi_gpu_test=dict(gpu_collect=True))
find_unused_parameters = True
dist_params = dict(port='29507')
batch_size = 8
num_workers = 4
tokestrel = dict(
    model_name='ep20_r331_data277w_single_stage_relu_sm1_mirror_soft0.5',
    version='3.3.1',
    batch_size=dict(image=1, image2=1))
dataset_type = 'pillar.LkdCropClsDataset'
ceph_cfg = ''
train_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'
train_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'
val_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'
val_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'
num_lane_classes = 2
categories = dict(
    group=['lane_type', 'lane_color', 'roadside'],
    attr=dict(
        lane_type=[
            'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
            'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
            'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
            'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
            'FISHBONE_DASHED'
        ],
        lane_color=[
            'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE', 'BLUE'
        ],
        roadside=[
            'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
            'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
        ]))
view_distance = 5
inv_view_distance = 5
num_ref_point = 2
length_scale = 0
field_scale = None
maskAct = 'Hsigmoid'
laneline_indices = dict(length=27, offset=29, field=30, view=(33, 37))
roadside_indices = dict(length=28, offset=31, field=32, view=(37, 41))
gt_indices = dict(
    offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23))
img_scale = (864, 288)
h_crop = (0, 288)
camera_roi = dict(
    front_fov30=dict(t=0, b=1, l=0, r=1),
    front_fov120=dict(t=0, b=1, l=0, r=1))
img_norm_cfg = dict(
    mean=[75.3, 76.6, 77.6], std=[50.5, 53.8, 54.3], to_rgb=False)
proposal_heatmap_thr = 0.6
range_method = 'field'
post_processor = dict(
    key_points_size_thr=35,
    side_key_points_size_thr=35,
    range_method='field',
    cls_method='row_att',
    with_breakpoints=True,
    breakpoints_thr=0.6,
    range_thr=0,
    range_continue_tole=0,
    use_offset=True,
    nms_thr=0.0,
    categories=dict(
        group=['lane_type', 'lane_color', 'roadside'],
        attr=dict(
            lane_type=[
                'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                'FISHBONE_DASHED'
            ],
            lane_color=[
                'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                'BLUE'
            ],
            roadside=[
                'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
            ])),
    laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),
    roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),
    mask_upsample_rate=4,
    num_row_ref_point=2,
    inv_view_distance=5,
    length_scale=0,
    field_scale=None,
    with_hm=True,
    mask_sigma=0.5,
    mask_max_thr=0.0,
    th_len=10,
    v4_simplify=True,
    mask_ignores=30)
mask_down_scale = 4
hm_down_scale = 16
line_width = 4
radius = 3
max_mask_sample = 4
train_cfg = dict(out_scale=4)
test_cfg = dict(out_scale=4)
loss_weights = dict(
    hm_weight=2.5,
    kps_weight=0.4,
    row_weight=1.5,
    range_weight=2.0,
    lane_type_weight=[
        1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
    ],
    row_ce_weight=1.0,
    len_loss_weight=1.0,
    auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],
    d2_loss_weight=0.5,
    smooth_weight=0.0,
    view_distance=5,
    inv_view_distance=5,
    num_ref_point=2,
    length_scale=0,
    field_scale=None,
    field_bg_weight=[-1, 0.1],
    attr_bg_weight=0.1,
    mask_bg_weight=[-1, 0.1],
    maskAct='Hsigmoid',
    reg_smooth_l1=True)
norm_cfg = dict(type='BN2d', requires_grad=True)
model = dict(
    type='pillar.FieldLaneNet',
    train_cfg=dict(out_scale=4),
    test_cfg=dict(out_scale=4),
    num_classes=2,
    loss_condlane=dict(
        type='pillar.FieldLaneLoss',
        weights=dict(
            hm_weight=2.5,
            kps_weight=0.4,
            row_weight=1.5,
            range_weight=2.0,
            lane_type_weight=[
                1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
            ],
            row_ce_weight=1.0,
            len_loss_weight=1.0,
            auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],
            d2_loss_weight=0.5,
            smooth_weight=0.0,
            view_distance=5,
            inv_view_distance=5,
            num_ref_point=2,
            length_scale=0,
            field_scale=None,
            field_bg_weight=[-1, 0.1],
            attr_bg_weight=0.1,
            mask_bg_weight=[-1, 0.1],
            maskAct='Hsigmoid',
            reg_smooth_l1=True),
        property_group=[13, 6, 6]),
    backbone=dict(
        type='pillar.ConvNeXtTI',
        depths=[2, 3, 9, 3],
        dims=[64, 64, 128, 128],
        drop_path_rate=0.2,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        ActType='ReLU',
        layer_scale_init_value=0),
    neck=dict(
        type='pillar.TransConvFPN',
        in_channels=[64, 64, 128, 128],
        out_channels=64,
        num_outs=4,
        trans_idx=-1,
        trans_cfg=None,
        next_conv_cfg=dict(
            in_channel=128,
            channels=[128, 128],
            kernels=[[7, 7], [7, 7]],
            expand_ratios=[2, 2],
            is_plus=True,
            ActType='ReLU'),
        attention=False,
        kernel_size=7,
        deconv=False,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        lateral_norm_cfg=None,
        fpn_norm_cfg=None),
    decoder=dict(
        type='pillar.SegFormerHead',
        in_channels=[64, 64, 128, 128],
        in_index=[0, 1, 2, 3],
        feature_strides=[4, 8, 16, 32],
        channels=128,
        dropout_ratio=0.1,
        num_classes=150,
        align_corners=False,
        decoder_params=dict(embed_dim=768),
        loss_decode=dict(
            type='CrossEntropyLoss', use_sigmoid=False, loss_weight=1.0)),
    spatial_head=dict(
        type='pillar.DepthRegressionModel', in_channels=768, out_channels=1),
    head=dict(
        type='pillar.FieldLaneHead',
        in_channel=64,
        ActType='ReLU',
        maskAct='Hsigmoid',
        mask_property_group=dict(
            line_mask=(2, 0, 1),
            laneline_type=(13, 2, 1),
            laneline_color=(6, 2, 1),
            roadside_type=(6, 2, 1),
            roadside_length=(1, 0, 2),
            laneline_length=(1, 0, 2),
            roadside_offset=(2, 0, 1),
            laneline_offset=(2, 0, 1),
            roadside_field=(2, 0, 2),
            laneline_field=(2, 0, 2),
            roadside_view=(8, 0, 2),
            laneline_view=(8, 0, 2),
            roadside_inv_vector_field=(10, 0, 2),
            laneline_inv_vector_field=(10, 0, 2),
            roadside_inv_length=(1, 0, 1),
            laneline_inv_length=(1, 0, 1)),
        num_classes=2,
        with_break_point=False,
        hm_idx=2,
        mask_idx=0,
        proposal_heatmap_thr=0.6,
        range_method='field',
        expand=1,
        with_start_point_off=True,
        with_start_point_feat_group=True,
        with_nms_weight=True,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        v4_simplify=True),
    post_processor=dict(
        key_points_size_thr=35,
        side_key_points_size_thr=35,
        range_method='field',
        cls_method='row_att',
        with_breakpoints=True,
        breakpoints_thr=0.6,
        range_thr=0,
        range_continue_tole=0,
        use_offset=True,
        nms_thr=0.0,
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),
        roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),
        mask_upsample_rate=4,
        num_row_ref_point=2,
        inv_view_distance=5,
        length_scale=0,
        field_scale=None,
        with_hm=True,
        mask_sigma=0.5,
        mask_max_thr=0.0,
        th_len=10,
        v4_simplify=True,
        mask_ignores=30),
    gt_indices=dict(
        offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23)))
compose_params = dict(bboxes=False, keypoints=True, masks=True)
DIAMOND_KERNEL_5 = [[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],
                    [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]]
train_al_pipeline = [
    dict(
        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),
    dict(type='Resize', height=288, width=864, p=1)
]
val_al_pipeline = [
    dict(
        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),
    dict(type='Resize', height=288, width=864, p=1)
]
train_pipeline = [
    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
    dict(
        type='pillar.Alaug',
        transforms=[
            dict(
                type='Compose',
                params=dict(bboxes=False, keypoints=True, masks=True)),
            dict(type='Resize', height=288, width=864, p=1)
        ]),
    dict(
        type='mmcls.Normalize',
        mean=[75.3, 76.6, 77.6],
        std=[50.5, 53.8, 54.3],
        to_rgb=False),
    dict(
        type='pillar.DepthCompletion',
        max_depth=100.0,
        custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],
                       [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],
        extrapolate=False,
        blur_type='bilateral'),
    dict(
        type='pillar.CollectBilateralFiledLane',
        down_scale=4,
        hm_down_scale=16,
        property_down_rate=4,
        num_drop_remote_point=0,
        num_start_rows=3,
        bg_weight=0.01,
        soft_weight_len_alpha=0.5,
        soft_weight_off_alpha=0.5,
        soft_weight_x=True,
        soft_weight_y=True,
        draw_r=4,
        num_ref_point=2,
        inv_view_distance=5,
        max_mask_sample=4,
        line_width=4,
        radius=3,
        interval=5,
        filter_pts=1,
        endpoint='down',
        keys=[
            'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',
            'roadside_gt_hm', 'lane_masks', 'lane_label_mask',
            'lane_color_mask', 'roadside_masks', 'roadside_label_mask',
            'roadside_field', 'roadside_attribute_target', 'depth_map'
        ],
        meta_keys=[
            'filename', 'sub_img_name', 'mask_shape', 'hm_shape', 'ori_shape',
            'img_shape', 'down_scale', 'hm_down_scale', 'img_norm_cfg'
        ]),
    dict(type='pillar.DefaultFormatBundle')
]
val_pipeline = [
    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
    dict(
        type='pillar.Alaug',
        transforms=[
            dict(
                type='Compose',
                params=dict(bboxes=False, keypoints=True, masks=True)),
            dict(type='Resize', height=288, width=864, p=1)
        ]),
    dict(
        type='mmcls.Normalize',
        mean=[75.3, 76.6, 77.6],
        std=[50.5, 53.8, 54.3],
        to_rgb=False),
    dict(
        type='pillar.CollectLkdClsLane',
        down_scale=4,
        hm_down_scale=16,
        radius=3),
    dict(type='pillar.DefaultFormatBundle')
]
data = dict(
    samples_per_gpu=8,
    workers_per_gpu=4,
    train=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.DepthCompletion',
                max_depth=100.0,
                custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1,
                                                 0], [1, 1, 1, 1, 1],
                               [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],
                extrapolate=False,
                blur_type='bilateral'),
            dict(
                type='pillar.CollectBilateralFiledLane',
                down_scale=4,
                hm_down_scale=16,
                property_down_rate=4,
                num_drop_remote_point=0,
                num_start_rows=3,
                bg_weight=0.01,
                soft_weight_len_alpha=0.5,
                soft_weight_off_alpha=0.5,
                soft_weight_x=True,
                soft_weight_y=True,
                draw_r=4,
                num_ref_point=2,
                inv_view_distance=5,
                max_mask_sample=4,
                line_width=4,
                radius=3,
                interval=5,
                filter_pts=1,
                endpoint='down',
                keys=[
                    'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',
                    'roadside_gt_hm', 'lane_masks', 'lane_label_mask',
                    'lane_color_mask', 'roadside_masks', 'roadside_label_mask',
                    'roadside_field', 'roadside_attribute_target', 'depth_map'
                ],
                meta_keys=[
                    'filename', 'sub_img_name', 'mask_shape', 'hm_shape',
                    'ori_shape', 'img_shape', 'down_scale', 'hm_down_scale',
                    'img_norm_cfg'
                ]),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=False,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    val=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    test=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True))
evaluation = dict(
    type='mme.EvalHook',
    dataset=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    dataloader=dict(samples_per_gpu=16, workers_per_gpu=2),
    test_setting=dict(
        repo='pillar',
        single_gpu_test=dict(show=False),
        multi_gpu_test=dict(gpu_collect=True)),
    metric='accuracy',
    interval=2,
    by_epoch=True)
optimizer = dict(
    type='AdamW',
    lr=0.0001,
    weight_decay=0.0001,
    paramwise_cfg=dict(
        custom_keys=dict(
            backbone=dict(lr_mult=0.1),
            neck=dict(lr_mult=1.0),
            head=dict(lr_mult=1.0)),
        bias_decay_mult=0.0,
        norm_decay_mult=0.0))
optimizer_config = dict(grad_clip=dict(max_norm=3))
lr_config = dict(
    policy='CosineAnnealing',
    min_lr=1e-06,
    by_epoch=False,
    warmup='linear',
    warmup_iters=500,
    warmup_ratio=0.01,
    warmup_by_epoch=False)
runner = dict(type='mmcv.EpochBasedRunner', max_epochs=30)
checkpoint_config = dict(interval=1, by_epoch=True, max_keep_ckpts=1)
log_config = dict(interval=1, hooks=[dict(type='mmcv.TextLoggerHook')])
log_level = 'INFO'
auto_resume = False
resume_from = ''
load_from = ''
workflow = [('train', 200)]
work_dir = './condlane_train/'
gpu_ids = range(0, 1)

fatal: not a git repository (or any of the parent directories): .git
2025-03-06 15:07:13,923 - mme - INFO - Start running, host: owais.tahir@ubuntu, work_dir: /home/<USER>/condlane_new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/functions/condlane/condlane_train
2025-03-06 15:07:13,923 - mme - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(NORMAL      ) CheckpointHook                     
(LOW         ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(LOW         ) IterTimerHook                      
(LOW         ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
before_train_iter:
(VERY_HIGH   ) CosineAnnealingLrUpdaterHook       
(LOW         ) IterTimerHook                      
(LOW         ) EvalHook                           
 -------------------- 
after_train_iter:
(ABOVE_NORMAL) OptimizerHook                      
(NORMAL      ) CheckpointHook                     
(LOW         ) IterTimerHook                      
(LOW         ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) CheckpointHook                     
(LOW         ) EvalHook                           
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
before_val_epoch:
(LOW         ) IterTimerHook                      
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
before_val_iter:
(LOW         ) IterTimerHook                      
 -------------------- 
after_val_iter:
(LOW         ) IterTimerHook                      
 -------------------- 
after_val_epoch:
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
after_run:
(VERY_LOW    ) TextLoggerHook                     
 -------------------- 
2025-03-06 15:07:13,924 - mme - INFO - workflow: [('train', 200)], max: 30 epochs
2025-03-06 15:07:13,924 - mme - INFO - Checkpoints will be saved to /home/<USER>/condlane_new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/functions/condlane/condlane_train by HardDiskBackend.
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:17,462 - mme - INFO - laneline pred/gt: 7758/693
2025-03-06 15:07:17,463 - mme - INFO - roadside pred/gt: 7776/693
2025-03-06 15:07:17,809 - mme - INFO - Epoch [1][1/624]	lr: 1.000e-07, eta: 20:11:37, time: 3.884, data_time: 2.992, memory: 6816, laneline_hm_loss: 4.1027, roadside_hm_loss: 6.3386, laneline_st_off_loss: 13.9523, roadside_st_off_loss: 20.4256, mask_loss: 1.2199, lane_type_mask_loss: 1.3223, lane_color_mask_loss: 0.5937, side_type_mask_loss: 0.9224, laneline_len_loss: 11.6617, laneline_field_loss: 2.5692, laneline_view_loss: 8.5106, laneline_inv_len_loss: 8.5380, laneline_inv_field_loss: 6.2260, roadside_len_loss: 11.5895, roadside_field_loss: 1.7033, roadside_view_loss: 5.0981, roadside_inv_len_loss: 7.7582, roadside_inv_field_loss: 2.9560, depth_loss: 0.1568, loss: 115.6450, grad_norm: 480.5607
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:17,920 - mme - INFO - laneline pred/gt: 7717/705
2025-03-06 15:07:17,921 - mme - INFO - roadside pred/gt: 7776/705
2025-03-06 15:07:18,220 - mme - INFO - Epoch [1][2/624]	lr: 1.198e-07, eta: 11:09:55, time: 0.411, data_time: 0.007, memory: 6893, laneline_hm_loss: 4.0769, roadside_hm_loss: 6.3108, laneline_st_off_loss: 17.4971, roadside_st_off_loss: 21.0750, mask_loss: 1.1627, lane_type_mask_loss: 1.3329, lane_color_mask_loss: 0.6078, side_type_mask_loss: 0.9328, laneline_len_loss: 11.6389, laneline_field_loss: 2.6385, laneline_view_loss: 8.7978, laneline_inv_len_loss: 8.5062, laneline_inv_field_loss: 6.4783, roadside_len_loss: 11.4265, roadside_field_loss: 1.7358, roadside_view_loss: 5.0695, roadside_inv_len_loss: 7.7953, roadside_inv_field_loss: 3.0021, depth_loss: 0.1485, loss: 120.2334, grad_norm: 550.6954
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:18,330 - mme - INFO - laneline pred/gt: 7731/932
2025-03-06 15:07:18,331 - mme - INFO - roadside pred/gt: 7776/932
2025-03-06 15:07:18,615 - mme - INFO - Epoch [1][3/624]	lr: 1.396e-07, eta: 8:07:36, time: 0.394, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.0865, roadside_hm_loss: 4.6964, laneline_st_off_loss: 15.2047, roadside_st_off_loss: 18.2546, mask_loss: 0.8825, lane_type_mask_loss: 1.3057, lane_color_mask_loss: 0.5852, side_type_mask_loss: 0.9328, laneline_len_loss: 8.4472, laneline_field_loss: 2.9346, laneline_view_loss: 10.1078, laneline_inv_len_loss: 6.0369, laneline_inv_field_loss: 7.4825, roadside_len_loss: 8.2912, roadside_field_loss: 1.7743, roadside_view_loss: 5.3834, roadside_inv_len_loss: 5.5054, roadside_inv_field_loss: 3.1120, depth_loss: 0.2304, loss: 104.2542, grad_norm: 473.8448
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:18,722 - mme - INFO - laneline pred/gt: 7740/822
2025-03-06 15:07:18,722 - mme - INFO - roadside pred/gt: 7776/822
2025-03-06 15:07:19,009 - mme - INFO - Epoch [1][4/624]	lr: 1.594e-07, eta: 6:36:27, time: 0.395, data_time: 0.004, memory: 6893, laneline_hm_loss: 3.4797, roadside_hm_loss: 5.3515, laneline_st_off_loss: 16.5569, roadside_st_off_loss: 18.2327, mask_loss: 0.9916, lane_type_mask_loss: 1.3093, lane_color_mask_loss: 0.5977, side_type_mask_loss: 0.9230, laneline_len_loss: 9.6766, laneline_field_loss: 2.6980, laneline_view_loss: 8.8949, laneline_inv_len_loss: 6.9542, laneline_inv_field_loss: 6.5531, roadside_len_loss: 9.5508, roadside_field_loss: 1.7324, roadside_view_loss: 4.9159, roadside_inv_len_loss: 6.3613, roadside_inv_field_loss: 2.8887, depth_loss: 0.1858, loss: 107.8539, grad_norm: 518.6198
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:19,126 - mme - INFO - laneline pred/gt: 7737/614
2025-03-06 15:07:19,127 - mme - INFO - roadside pred/gt: 7776/614
2025-03-06 15:07:19,417 - mme - INFO - Epoch [1][5/624]	lr: 1.792e-07, eta: 5:42:35, time: 0.408, data_time: 0.012, memory: 6893, laneline_hm_loss: 4.5846, roadside_hm_loss: 7.2288, laneline_st_off_loss: 18.4788, roadside_st_off_loss: 21.1262, mask_loss: 1.4338, lane_type_mask_loss: 1.3065, lane_color_mask_loss: 0.5917, side_type_mask_loss: 0.9302, laneline_len_loss: 14.8364, laneline_field_loss: 2.4582, laneline_view_loss: 7.9771, laneline_inv_len_loss: 11.0821, laneline_inv_field_loss: 5.8637, roadside_len_loss: 14.6637, roadside_field_loss: 1.6581, roadside_view_loss: 4.7801, roadside_inv_len_loss: 10.0206, roadside_inv_field_loss: 2.8651, depth_loss: 0.1461, loss: 132.0318, grad_norm: 600.4487
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:19,525 - mme - INFO - laneline pred/gt: 7737/620
2025-03-06 15:07:19,525 - mme - INFO - roadside pred/gt: 7776/620
2025-03-06 15:07:19,817 - mme - INFO - Epoch [1][6/624]	lr: 1.990e-07, eta: 5:06:13, time: 0.399, data_time: 0.005, memory: 6893, laneline_hm_loss: 4.4979, roadside_hm_loss: 7.1506, laneline_st_off_loss: 13.8734, roadside_st_off_loss: 16.9461, mask_loss: 1.2516, lane_type_mask_loss: 1.3035, lane_color_mask_loss: 0.6050, side_type_mask_loss: 0.9081, laneline_len_loss: 11.9333, laneline_field_loss: 2.2867, laneline_view_loss: 6.8467, laneline_inv_len_loss: 8.7256, laneline_inv_field_loss: 5.1970, roadside_len_loss: 11.6996, roadside_field_loss: 1.5163, roadside_view_loss: 4.1183, roadside_inv_len_loss: 7.9912, roadside_inv_field_loss: 2.5696, depth_loss: 0.1289, loss: 109.5494, grad_norm: 470.8126
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:19,930 - mme - INFO - laneline pred/gt: 7740/475
2025-03-06 15:07:19,931 - mme - INFO - roadside pred/gt: 7776/475
2025-03-06 15:07:20,217 - mme - INFO - Epoch [1][7/624]	lr: 2.188e-07, eta: 4:40:19, time: 0.401, data_time: 0.008, memory: 6893, laneline_hm_loss: 5.8855, roadside_hm_loss: 9.4740, laneline_st_off_loss: 13.7004, roadside_st_off_loss: 16.0700, mask_loss: 1.5738, lane_type_mask_loss: 1.3047, lane_color_mask_loss: 0.5988, side_type_mask_loss: 0.9227, laneline_len_loss: 16.1831, laneline_field_loss: 2.2782, laneline_view_loss: 7.4174, laneline_inv_len_loss: 12.0156, laneline_inv_field_loss: 5.5382, roadside_len_loss: 15.9338, roadside_field_loss: 1.6653, roadside_view_loss: 4.9080, roadside_inv_len_loss: 11.0606, roadside_inv_field_loss: 2.9902, depth_loss: 0.1396, loss: 129.6599, grad_norm: 610.6418
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:20,336 - mme - INFO - laneline pred/gt: 7730/667
2025-03-06 15:07:20,337 - mme - INFO - roadside pred/gt: 7776/667
2025-03-06 15:07:20,637 - mme - INFO - Epoch [1][8/624]	lr: 2.386e-07, eta: 4:21:38, time: 0.420, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.2005, roadside_hm_loss: 6.6131, laneline_st_off_loss: 14.9325, roadside_st_off_loss: 16.1687, mask_loss: 1.1844, lane_type_mask_loss: 1.3140, lane_color_mask_loss: 0.5907, side_type_mask_loss: 0.9259, laneline_len_loss: 11.7997, laneline_field_loss: 2.4888, laneline_view_loss: 8.3462, laneline_inv_len_loss: 8.6501, laneline_inv_field_loss: 6.3171, roadside_len_loss: 11.6740, roadside_field_loss: 1.6421, roadside_view_loss: 4.9110, roadside_inv_len_loss: 7.9078, roadside_inv_field_loss: 2.9926, depth_loss: 0.1798, loss: 112.8390, grad_norm: 518.1855
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:20,748 - mme - INFO - laneline pred/gt: 7744/674
2025-03-06 15:07:20,748 - mme - INFO - roadside pred/gt: 7776/674
2025-03-06 15:07:21,035 - mme - INFO - Epoch [1][9/624]	lr: 2.584e-07, eta: 4:06:19, time: 0.397, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.1511, roadside_hm_loss: 6.6335, laneline_st_off_loss: 13.9654, roadside_st_off_loss: 17.8120, mask_loss: 1.1876, lane_type_mask_loss: 1.2846, lane_color_mask_loss: 0.5895, side_type_mask_loss: 0.9168, laneline_len_loss: 11.8970, laneline_field_loss: 2.6230, laneline_view_loss: 7.7994, laneline_inv_len_loss: 8.7628, laneline_inv_field_loss: 5.7353, roadside_len_loss: 11.6835, roadside_field_loss: 1.8036, roadside_view_loss: 4.5677, roadside_inv_len_loss: 7.9593, roadside_inv_field_loss: 2.6886, depth_loss: 0.1886, loss: 112.2493, grad_norm: 419.3556
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:21,146 - mme - INFO - laneline pred/gt: 7725/524
2025-03-06 15:07:21,147 - mme - INFO - roadside pred/gt: 7776/524
2025-03-06 15:07:21,428 - mme - INFO - Epoch [1][10/624]	lr: 2.782e-07, eta: 3:53:58, time: 0.394, data_time: 0.006, memory: 6893, laneline_hm_loss: 5.2912, roadside_hm_loss: 8.4564, laneline_st_off_loss: 13.3893, roadside_st_off_loss: 16.8214, mask_loss: 1.4101, lane_type_mask_loss: 1.2849, lane_color_mask_loss: 0.6021, side_type_mask_loss: 0.9046, laneline_len_loss: 14.7786, laneline_field_loss: 2.3609, laneline_view_loss: 7.3157, laneline_inv_len_loss: 10.9382, laneline_inv_field_loss: 5.4651, roadside_len_loss: 14.6215, roadside_field_loss: 1.6104, roadside_view_loss: 4.4891, roadside_inv_len_loss: 10.0203, roadside_inv_field_loss: 2.8078, depth_loss: 0.1043, loss: 122.6720, grad_norm: 479.4828
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:21,538 - mme - INFO - laneline pred/gt: 7730/798
2025-03-06 15:07:21,539 - mme - INFO - roadside pred/gt: 7776/798
2025-03-06 15:07:21,821 - mme - INFO - Epoch [1][11/624]	lr: 2.980e-07, eta: 3:43:47, time: 0.392, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.5613, roadside_hm_loss: 5.4547, laneline_st_off_loss: 15.9295, roadside_st_off_loss: 17.7970, mask_loss: 1.0312, lane_type_mask_loss: 1.3207, lane_color_mask_loss: 0.5866, side_type_mask_loss: 0.8898, laneline_len_loss: 10.0786, laneline_field_loss: 2.8854, laneline_view_loss: 9.8000, laneline_inv_len_loss: 7.4046, laneline_inv_field_loss: 7.2320, roadside_len_loss: 9.9825, roadside_field_loss: 1.8147, roadside_view_loss: 5.2772, roadside_inv_len_loss: 6.7302, roadside_inv_field_loss: 3.1184, depth_loss: 0.2161, loss: 111.1105, grad_norm: 426.8657
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:21,934 - mme - INFO - laneline pred/gt: 7732/887
2025-03-06 15:07:21,935 - mme - INFO - roadside pred/gt: 7776/887
2025-03-06 15:07:22,216 - mme - INFO - Epoch [1][12/624]	lr: 3.178e-07, eta: 3:35:24, time: 0.395, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.2373, roadside_hm_loss: 4.8573, laneline_st_off_loss: 16.5650, roadside_st_off_loss: 17.7149, mask_loss: 0.8927, lane_type_mask_loss: 1.3127, lane_color_mask_loss: 0.5870, side_type_mask_loss: 0.9070, laneline_len_loss: 8.2219, laneline_field_loss: 2.9177, laneline_view_loss: 10.3559, laneline_inv_len_loss: 5.9582, laneline_inv_field_loss: 7.7185, roadside_len_loss: 8.1652, roadside_field_loss: 1.8452, roadside_view_loss: 5.6795, roadside_inv_len_loss: 5.4299, roadside_inv_field_loss: 3.2665, depth_loss: 0.2429, loss: 105.8753, grad_norm: 499.3030
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:22,325 - mme - INFO - laneline pred/gt: 7716/866
2025-03-06 15:07:22,325 - mme - INFO - roadside pred/gt: 7776/866
2025-03-06 15:07:22,615 - mme - INFO - Epoch [1][13/624]	lr: 3.376e-07, eta: 3:28:13, time: 0.392, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.2949, roadside_hm_loss: 4.9899, laneline_st_off_loss: 16.0643, roadside_st_off_loss: 16.2841, mask_loss: 1.0453, lane_type_mask_loss: 1.3207, lane_color_mask_loss: 0.5950, side_type_mask_loss: 0.9042, laneline_len_loss: 10.2153, laneline_field_loss: 2.6229, laneline_view_loss: 8.7446, laneline_inv_len_loss: 7.3975, laneline_inv_field_loss: 6.5663, roadside_len_loss: 9.9843, roadside_field_loss: 1.6694, roadside_view_loss: 4.8607, roadside_inv_len_loss: 6.7994, roadside_inv_field_loss: 2.8960, depth_loss: 0.1923, loss: 106.4470, grad_norm: 437.2790
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:22,728 - mme - INFO - laneline pred/gt: 7744/769
2025-03-06 15:07:22,728 - mme - INFO - roadside pred/gt: 7776/769
2025-03-06 15:07:23,018 - mme - INFO - Epoch [1][14/624]	lr: 3.574e-07, eta: 3:22:28, time: 0.411, data_time: 0.015, memory: 6893, laneline_hm_loss: 3.6498, roadside_hm_loss: 5.5860, laneline_st_off_loss: 16.6700, roadside_st_off_loss: 18.6460, mask_loss: 1.0125, lane_type_mask_loss: 1.3309, lane_color_mask_loss: 0.5867, side_type_mask_loss: 0.9014, laneline_len_loss: 10.0530, laneline_field_loss: 2.7595, laneline_view_loss: 9.4604, laneline_inv_len_loss: 7.3812, laneline_inv_field_loss: 6.9718, roadside_len_loss: 9.9633, roadside_field_loss: 1.7548, roadside_view_loss: 5.3461, roadside_inv_len_loss: 6.7005, roadside_inv_field_loss: 3.1537, depth_loss: 0.1769, loss: 112.1046, grad_norm: 432.5598
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:23,127 - mme - INFO - laneline pred/gt: 7706/718
2025-03-06 15:07:23,128 - mme - INFO - roadside pred/gt: 7776/718
2025-03-06 15:07:23,416 - mme - INFO - Epoch [1][15/624]	lr: 3.772e-07, eta: 3:17:14, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.9416, roadside_hm_loss: 6.0212, laneline_st_off_loss: 17.2454, roadside_st_off_loss: 17.6218, mask_loss: 1.1011, lane_type_mask_loss: 1.2800, lane_color_mask_loss: 0.5883, side_type_mask_loss: 0.9151, laneline_len_loss: 11.1102, laneline_field_loss: 2.6255, laneline_view_loss: 7.7752, laneline_inv_len_loss: 8.1291, laneline_inv_field_loss: 5.6341, roadside_len_loss: 11.0296, roadside_field_loss: 1.6905, roadside_view_loss: 4.2873, roadside_inv_len_loss: 7.4497, roadside_inv_field_loss: 2.5454, depth_loss: 0.1702, loss: 111.1614, grad_norm: 443.2524
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:23,525 - mme - INFO - laneline pred/gt: 7738/703
2025-03-06 15:07:23,525 - mme - INFO - roadside pred/gt: 7776/703
2025-03-06 15:07:23,818 - mme - INFO - Epoch [1][16/624]	lr: 3.970e-07, eta: 3:12:41, time: 0.400, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.9352, roadside_hm_loss: 6.0907, laneline_st_off_loss: 14.2443, roadside_st_off_loss: 18.0900, mask_loss: 1.0654, lane_type_mask_loss: 1.3034, lane_color_mask_loss: 0.5796, side_type_mask_loss: 0.9200, laneline_len_loss: 10.1678, laneline_field_loss: 2.5545, laneline_view_loss: 8.1905, laneline_inv_len_loss: 7.4834, laneline_inv_field_loss: 6.2612, roadside_len_loss: 10.1683, roadside_field_loss: 1.6456, roadside_view_loss: 4.7374, roadside_inv_len_loss: 6.8296, roadside_inv_field_loss: 2.9428, depth_loss: 0.1716, loss: 107.3811, grad_norm: 449.7953
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:23,930 - mme - INFO - laneline pred/gt: 7728/594
2025-03-06 15:07:23,930 - mme - INFO - roadside pred/gt: 7776/594
2025-03-06 15:07:24,234 - mme - INFO - Epoch [1][17/624]	lr: 4.168e-07, eta: 3:09:01, time: 0.418, data_time: 0.007, memory: 6893, laneline_hm_loss: 4.6639, roadside_hm_loss: 7.3099, laneline_st_off_loss: 15.1246, roadside_st_off_loss: 17.7014, mask_loss: 1.2328, lane_type_mask_loss: 1.2938, lane_color_mask_loss: 0.5760, side_type_mask_loss: 0.8822, laneline_len_loss: 12.6770, laneline_field_loss: 2.7095, laneline_view_loss: 8.8698, laneline_inv_len_loss: 9.3656, laneline_inv_field_loss: 6.4837, roadside_len_loss: 12.4934, roadside_field_loss: 1.8358, roadside_view_loss: 5.1620, roadside_inv_len_loss: 8.6337, roadside_inv_field_loss: 3.0528, depth_loss: 0.1477, loss: 120.2158, grad_norm: 494.8731
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:24,343 - mme - INFO - laneline pred/gt: 7735/726
2025-03-06 15:07:24,344 - mme - INFO - roadside pred/gt: 7776/726
2025-03-06 15:07:24,631 - mme - INFO - Epoch [1][18/624]	lr: 4.366e-07, eta: 3:05:22, time: 0.396, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.8236, roadside_hm_loss: 5.9227, laneline_st_off_loss: 15.4295, roadside_st_off_loss: 15.7200, mask_loss: 1.1450, lane_type_mask_loss: 1.2970, lane_color_mask_loss: 0.5787, side_type_mask_loss: 0.8876, laneline_len_loss: 11.6502, laneline_field_loss: 2.7775, laneline_view_loss: 9.0623, laneline_inv_len_loss: 8.5526, laneline_inv_field_loss: 6.4088, roadside_len_loss: 11.5384, roadside_field_loss: 1.7814, roadside_view_loss: 4.9359, roadside_inv_len_loss: 7.8241, roadside_inv_field_loss: 2.8027, depth_loss: 0.1302, loss: 112.2682, grad_norm: 414.2353
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:24,740 - mme - INFO - laneline pred/gt: 7730/898
2025-03-06 15:07:24,741 - mme - INFO - roadside pred/gt: 7776/898
2025-03-06 15:07:25,030 - mme - INFO - Epoch [1][19/624]	lr: 4.564e-07, eta: 3:02:08, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.1423, roadside_hm_loss: 4.7132, laneline_st_off_loss: 16.3920, roadside_st_off_loss: 17.4664, mask_loss: 0.9336, lane_type_mask_loss: 1.3276, lane_color_mask_loss: 0.5806, side_type_mask_loss: 0.8655, laneline_len_loss: 8.7381, laneline_field_loss: 2.8183, laneline_view_loss: 9.4957, laneline_inv_len_loss: 6.3177, laneline_inv_field_loss: 7.2403, roadside_len_loss: 8.6216, roadside_field_loss: 1.7677, roadside_view_loss: 5.2459, roadside_inv_len_loss: 5.7624, roadside_inv_field_loss: 3.1280, depth_loss: 0.2274, loss: 104.7844, grad_norm: 429.5780
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:25,149 - mme - INFO - laneline pred/gt: 7742/991
2025-03-06 15:07:25,150 - mme - INFO - roadside pred/gt: 7776/991
2025-03-06 15:07:25,439 - mme - INFO - Epoch [1][20/624]	lr: 4.762e-07, eta: 2:59:24, time: 0.409, data_time: 0.015, memory: 6893, laneline_hm_loss: 2.8571, roadside_hm_loss: 4.2023, laneline_st_off_loss: 17.3444, roadside_st_off_loss: 18.0675, mask_loss: 0.8642, lane_type_mask_loss: 1.3050, lane_color_mask_loss: 0.5775, side_type_mask_loss: 0.8878, laneline_len_loss: 7.7488, laneline_field_loss: 3.0501, laneline_view_loss: 10.4466, laneline_inv_len_loss: 5.6148, laneline_inv_field_loss: 7.8417, roadside_len_loss: 7.6990, roadside_field_loss: 1.9177, roadside_view_loss: 5.5871, roadside_inv_len_loss: 5.0871, roadside_inv_field_loss: 3.2859, depth_loss: 0.2420, loss: 104.6265, grad_norm: 397.7947
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:25,547 - mme - INFO - laneline pred/gt: 7738/922
2025-03-06 15:07:25,548 - mme - INFO - roadside pred/gt: 7776/922
2025-03-06 15:07:25,841 - mme - INFO - Epoch [1][21/624]	lr: 4.960e-07, eta: 2:56:49, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.0353, roadside_hm_loss: 4.5960, laneline_st_off_loss: 15.8986, roadside_st_off_loss: 17.5398, mask_loss: 0.8489, lane_type_mask_loss: 1.2913, lane_color_mask_loss: 0.5889, side_type_mask_loss: 0.8736, laneline_len_loss: 7.5420, laneline_field_loss: 2.7656, laneline_view_loss: 9.4961, laneline_inv_len_loss: 5.4652, laneline_inv_field_loss: 7.2252, roadside_len_loss: 7.5274, roadside_field_loss: 1.7464, roadside_view_loss: 5.3247, roadside_inv_len_loss: 4.9499, roadside_inv_field_loss: 3.1906, depth_loss: 0.2364, loss: 100.1417, grad_norm: 436.8163
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:25,969 - mme - INFO - laneline pred/gt: 7740/825
2025-03-06 15:07:25,970 - mme - INFO - roadside pred/gt: 7776/825
2025-03-06 15:07:26,270 - mme - INFO - Epoch [1][22/624]	lr: 5.158e-07, eta: 2:54:51, time: 0.430, data_time: 0.020, memory: 6893, laneline_hm_loss: 3.4046, roadside_hm_loss: 5.0478, laneline_st_off_loss: 15.2856, roadside_st_off_loss: 16.3430, mask_loss: 0.9520, lane_type_mask_loss: 1.2852, lane_color_mask_loss: 0.5708, side_type_mask_loss: 0.8824, laneline_len_loss: 9.0745, laneline_field_loss: 2.6726, laneline_view_loss: 8.8966, laneline_inv_len_loss: 6.6239, laneline_inv_field_loss: 6.6204, roadside_len_loss: 8.9503, roadside_field_loss: 1.7193, roadside_view_loss: 4.9775, roadside_inv_len_loss: 6.0677, roadside_inv_field_loss: 2.9401, depth_loss: 0.2007, loss: 102.5150, grad_norm: 404.4479
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:26,380 - mme - INFO - laneline pred/gt: 7737/699
2025-03-06 15:07:26,381 - mme - INFO - roadside pred/gt: 7776/699
2025-03-06 15:07:26,680 - mme - INFO - Epoch [1][23/624]	lr: 5.356e-07, eta: 2:52:48, time: 0.410, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.9247, roadside_hm_loss: 6.1206, laneline_st_off_loss: 15.2961, roadside_st_off_loss: 17.5279, mask_loss: 1.0245, lane_type_mask_loss: 1.2862, lane_color_mask_loss: 0.5686, side_type_mask_loss: 0.8680, laneline_len_loss: 10.0588, laneline_field_loss: 2.6446, laneline_view_loss: 8.1359, laneline_inv_len_loss: 7.3490, laneline_inv_field_loss: 5.9614, roadside_len_loss: 10.0297, roadside_field_loss: 1.7339, roadside_view_loss: 4.7068, roadside_inv_len_loss: 6.6863, roadside_inv_field_loss: 2.7656, depth_loss: 0.1998, loss: 106.8885, grad_norm: 428.2231
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:26,792 - mme - INFO - laneline pred/gt: 7702/600
2025-03-06 15:07:26,793 - mme - INFO - roadside pred/gt: 7776/600
2025-03-06 15:07:27,089 - mme - INFO - Epoch [1][24/624]	lr: 5.554e-07, eta: 2:50:53, time: 0.408, data_time: 0.007, memory: 6893, laneline_hm_loss: 4.5106, roadside_hm_loss: 7.1492, laneline_st_off_loss: 14.1980, roadside_st_off_loss: 14.3811, mask_loss: 1.1707, lane_type_mask_loss: 1.2511, lane_color_mask_loss: 0.5777, side_type_mask_loss: 0.8672, laneline_len_loss: 11.8860, laneline_field_loss: 2.6174, laneline_view_loss: 8.0213, laneline_inv_len_loss: 8.8456, laneline_inv_field_loss: 5.9453, roadside_len_loss: 11.7499, roadside_field_loss: 1.7873, roadside_view_loss: 4.7717, roadside_inv_len_loss: 8.0840, roadside_inv_field_loss: 2.8968, depth_loss: 0.1597, loss: 110.8706, grad_norm: 447.8559
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:27,201 - mme - INFO - laneline pred/gt: 7721/823
2025-03-06 15:07:27,201 - mme - INFO - roadside pred/gt: 7776/823
2025-03-06 15:07:27,494 - mme - INFO - Epoch [1][25/624]	lr: 5.752e-07, eta: 2:49:06, time: 0.405, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.3808, roadside_hm_loss: 5.0965, laneline_st_off_loss: 15.2822, roadside_st_off_loss: 15.6118, mask_loss: 0.9334, lane_type_mask_loss: 1.2565, lane_color_mask_loss: 0.5565, side_type_mask_loss: 0.8739, laneline_len_loss: 9.2528, laneline_field_loss: 2.8609, laneline_view_loss: 9.9064, laneline_inv_len_loss: 6.8049, laneline_inv_field_loss: 7.2923, roadside_len_loss: 9.1975, roadside_field_loss: 1.8345, roadside_view_loss: 5.4357, roadside_inv_len_loss: 6.1745, roadside_inv_field_loss: 3.1585, depth_loss: 0.2771, loss: 105.1866, grad_norm: 386.3305
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:27,605 - mme - INFO - laneline pred/gt: 7699/764
2025-03-06 15:07:27,606 - mme - INFO - roadside pred/gt: 7776/764
2025-03-06 15:07:27,920 - mme - INFO - Epoch [1][26/624]	lr: 5.950e-07, eta: 2:47:42, time: 0.427, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.6710, roadside_hm_loss: 5.4486, laneline_st_off_loss: 14.0224, roadside_st_off_loss: 16.6943, mask_loss: 0.9756, lane_type_mask_loss: 1.2480, lane_color_mask_loss: 0.5610, side_type_mask_loss: 0.8668, laneline_len_loss: 9.6410, laneline_field_loss: 2.5437, laneline_view_loss: 8.0789, laneline_inv_len_loss: 7.1414, laneline_inv_field_loss: 6.0756, roadside_len_loss: 9.5667, roadside_field_loss: 1.6740, roadside_view_loss: 4.5850, roadside_inv_len_loss: 6.4858, roadside_inv_field_loss: 2.7888, depth_loss: 0.1751, loss: 102.2434, grad_norm: 462.7207
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:28,042 - mme - INFO - laneline pred/gt: 7715/762
2025-03-06 15:07:28,042 - mme - INFO - roadside pred/gt: 7776/762
2025-03-06 15:07:28,328 - mme - INFO - Epoch [1][27/624]	lr: 6.148e-07, eta: 2:46:11, time: 0.407, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.6261, roadside_hm_loss: 5.4984, laneline_st_off_loss: 18.4365, roadside_st_off_loss: 18.8168, mask_loss: 0.9685, lane_type_mask_loss: 1.2916, lane_color_mask_loss: 0.5600, side_type_mask_loss: 0.8480, laneline_len_loss: 9.3123, laneline_field_loss: 2.7148, laneline_view_loss: 9.6597, laneline_inv_len_loss: 6.8914, laneline_inv_field_loss: 7.2919, roadside_len_loss: 9.2518, roadside_field_loss: 1.7767, roadside_view_loss: 5.6440, roadside_inv_len_loss: 6.2394, roadside_inv_field_loss: 3.3652, depth_loss: 0.2331, loss: 112.4262, grad_norm: 480.2284
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:28,440 - mme - INFO - laneline pred/gt: 7724/885
2025-03-06 15:07:28,441 - mme - INFO - roadside pred/gt: 7776/887
2025-03-06 15:07:28,729 - mme - INFO - Epoch [1][28/624]	lr: 6.346e-07, eta: 2:44:42, time: 0.401, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.1219, roadside_hm_loss: 4.5995, laneline_st_off_loss: 12.9844, roadside_st_off_loss: 18.6415, mask_loss: 0.8767, lane_type_mask_loss: 1.2555, lane_color_mask_loss: 0.5445, side_type_mask_loss: 0.8472, laneline_len_loss: 8.4352, laneline_field_loss: 3.1796, laneline_view_loss: 10.5394, laneline_inv_len_loss: 6.2327, laneline_inv_field_loss: 7.6547, roadside_len_loss: 8.4453, roadside_field_loss: 2.0174, roadside_view_loss: 5.8220, roadside_inv_len_loss: 5.7039, roadside_inv_field_loss: 3.3004, depth_loss: 0.1822, loss: 104.3840, grad_norm: 494.3893
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:28,838 - mme - INFO - laneline pred/gt: 7702/796
2025-03-06 15:07:28,839 - mme - INFO - roadside pred/gt: 7776/796
2025-03-06 15:07:29,126 - mme - INFO - Epoch [1][29/624]	lr: 6.544e-07, eta: 2:43:16, time: 0.397, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.4006, roadside_hm_loss: 5.2282, laneline_st_off_loss: 11.8177, roadside_st_off_loss: 14.3352, mask_loss: 0.8717, lane_type_mask_loss: 1.2565, lane_color_mask_loss: 0.5600, side_type_mask_loss: 0.8471, laneline_len_loss: 8.6235, laneline_field_loss: 2.6130, laneline_view_loss: 8.4719, laneline_inv_len_loss: 6.2866, laneline_inv_field_loss: 6.3981, roadside_len_loss: 8.5769, roadside_field_loss: 1.6165, roadside_view_loss: 4.5843, roadside_inv_len_loss: 5.6970, roadside_inv_field_loss: 2.8300, depth_loss: 0.1725, loss: 94.1874, grad_norm: 358.1711
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:29,236 - mme - INFO - laneline pred/gt: 7707/761
2025-03-06 15:07:29,236 - mme - INFO - roadside pred/gt: 7776/761
2025-03-06 15:07:29,522 - mme - INFO - Epoch [1][30/624]	lr: 6.742e-07, eta: 2:41:56, time: 0.396, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.5118, roadside_hm_loss: 5.4143, laneline_st_off_loss: 13.3888, roadside_st_off_loss: 14.6950, mask_loss: 0.9638, lane_type_mask_loss: 1.2506, lane_color_mask_loss: 0.5648, side_type_mask_loss: 0.8348, laneline_len_loss: 9.5339, laneline_field_loss: 2.6312, laneline_view_loss: 8.6730, laneline_inv_len_loss: 7.1195, laneline_inv_field_loss: 6.4861, roadside_len_loss: 9.4766, roadside_field_loss: 1.7355, roadside_view_loss: 4.9259, roadside_inv_len_loss: 6.4604, roadside_inv_field_loss: 2.9404, depth_loss: 0.1563, loss: 100.7626, grad_norm: 374.9187
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:29,631 - mme - INFO - laneline pred/gt: 7717/660
2025-03-06 15:07:29,632 - mme - INFO - roadside pred/gt: 7776/660
2025-03-06 15:07:29,914 - mme - INFO - Epoch [1][31/624]	lr: 6.940e-07, eta: 2:40:38, time: 0.392, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.0334, roadside_hm_loss: 6.2865, laneline_st_off_loss: 13.1976, roadside_st_off_loss: 16.5328, mask_loss: 0.9578, lane_type_mask_loss: 1.2408, lane_color_mask_loss: 0.5448, side_type_mask_loss: 0.8375, laneline_len_loss: 10.2381, laneline_field_loss: 2.6597, laneline_view_loss: 8.9066, laneline_inv_len_loss: 7.6555, laneline_inv_field_loss: 6.6209, roadside_len_loss: 10.2233, roadside_field_loss: 1.7263, roadside_view_loss: 5.1619, roadside_inv_len_loss: 6.8915, roadside_inv_field_loss: 3.1028, depth_loss: 0.1590, loss: 106.9769, grad_norm: 426.7629
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:30,032 - mme - INFO - laneline pred/gt: 7714/758
2025-03-06 15:07:30,032 - mme - INFO - roadside pred/gt: 7776/758
2025-03-06 15:07:30,313 - mme - INFO - Epoch [1][32/624]	lr: 7.138e-07, eta: 2:39:30, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.5668, roadside_hm_loss: 5.3718, laneline_st_off_loss: 13.5405, roadside_st_off_loss: 17.3076, mask_loss: 0.9666, lane_type_mask_loss: 1.2564, lane_color_mask_loss: 0.5534, side_type_mask_loss: 0.8230, laneline_len_loss: 9.9587, laneline_field_loss: 2.7992, laneline_view_loss: 9.5434, laneline_inv_len_loss: 7.4161, laneline_inv_field_loss: 6.9439, roadside_len_loss: 9.8550, roadside_field_loss: 1.7351, roadside_view_loss: 5.2408, roadside_inv_len_loss: 6.7850, roadside_inv_field_loss: 3.0508, depth_loss: 0.1774, loss: 106.8912, grad_norm: 416.9561
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:30,422 - mme - INFO - laneline pred/gt: 7684/657
2025-03-06 15:07:30,422 - mme - INFO - roadside pred/gt: 7776/657
2025-03-06 15:07:30,710 - mme - INFO - Epoch [1][33/624]	lr: 7.336e-07, eta: 2:38:24, time: 0.397, data_time: 0.005, memory: 6893, laneline_hm_loss: 4.0762, roadside_hm_loss: 6.2471, laneline_st_off_loss: 13.0298, roadside_st_off_loss: 14.2434, mask_loss: 1.0094, lane_type_mask_loss: 1.2184, lane_color_mask_loss: 0.5468, side_type_mask_loss: 0.8500, laneline_len_loss: 10.4084, laneline_field_loss: 2.4912, laneline_view_loss: 7.9305, laneline_inv_len_loss: 7.7739, laneline_inv_field_loss: 6.0068, roadside_len_loss: 10.4144, roadside_field_loss: 1.6384, roadside_view_loss: 4.5830, roadside_inv_len_loss: 7.0565, roadside_inv_field_loss: 2.8452, depth_loss: 0.1822, loss: 102.5517, grad_norm: 362.6834
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:30,820 - mme - INFO - laneline pred/gt: 7694/996
2025-03-06 15:07:30,820 - mme - INFO - roadside pred/gt: 7776/996
2025-03-06 15:07:31,101 - mme - INFO - Epoch [1][34/624]	lr: 7.534e-07, eta: 2:37:19, time: 0.391, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7210, roadside_hm_loss: 4.0390, laneline_st_off_loss: 13.8952, roadside_st_off_loss: 15.7721, mask_loss: 0.7670, lane_type_mask_loss: 1.2275, lane_color_mask_loss: 0.5561, side_type_mask_loss: 0.8251, laneline_len_loss: 7.6419, laneline_field_loss: 3.3684, laneline_view_loss: 11.0460, laneline_inv_len_loss: 5.6823, laneline_inv_field_loss: 8.1664, roadside_len_loss: 7.6681, roadside_field_loss: 2.0338, roadside_view_loss: 5.6031, roadside_inv_len_loss: 5.1642, roadside_inv_field_loss: 3.2558, depth_loss: 0.2617, loss: 99.6945, grad_norm: 367.6122
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:31,215 - mme - INFO - laneline pred/gt: 7687/881
2025-03-06 15:07:31,216 - mme - INFO - roadside pred/gt: 7776/881
2025-03-06 15:07:31,496 - mme - INFO - Epoch [1][35/624]	lr: 7.732e-07, eta: 2:36:20, time: 0.396, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.0778, roadside_hm_loss: 4.5704, laneline_st_off_loss: 13.8340, roadside_st_off_loss: 15.4741, mask_loss: 0.8448, lane_type_mask_loss: 1.2358, lane_color_mask_loss: 0.5349, side_type_mask_loss: 0.8328, laneline_len_loss: 8.6432, laneline_field_loss: 2.7587, laneline_view_loss: 8.9707, laneline_inv_len_loss: 6.3815, laneline_inv_field_loss: 6.5841, roadside_len_loss: 8.5703, roadside_field_loss: 1.7052, roadside_view_loss: 4.7448, roadside_inv_len_loss: 5.7786, roadside_inv_field_loss: 2.7560, depth_loss: 0.1259, loss: 97.4236, grad_norm: 385.9178
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:31,605 - mme - INFO - laneline pred/gt: 7705/705
2025-03-06 15:07:31,606 - mme - INFO - roadside pred/gt: 7776/705
2025-03-06 15:07:31,899 - mme - INFO - Epoch [1][36/624]	lr: 7.930e-07, eta: 2:35:28, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.7545, roadside_hm_loss: 5.7710, laneline_st_off_loss: 13.8981, roadside_st_off_loss: 13.1769, mask_loss: 0.9417, lane_type_mask_loss: 1.2193, lane_color_mask_loss: 0.5480, side_type_mask_loss: 0.8132, laneline_len_loss: 9.5629, laneline_field_loss: 2.6752, laneline_view_loss: 8.3836, laneline_inv_len_loss: 7.2466, laneline_inv_field_loss: 6.1603, roadside_len_loss: 9.6086, roadside_field_loss: 1.7461, roadside_view_loss: 4.6451, roadside_inv_len_loss: 6.4381, roadside_inv_field_loss: 2.7764, depth_loss: 0.1441, loss: 99.5098, grad_norm: 366.5810
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:32,008 - mme - INFO - laneline pred/gt: 7698/910
2025-03-06 15:07:32,009 - mme - INFO - roadside pred/gt: 7776/910
2025-03-06 15:07:32,301 - mme - INFO - Epoch [1][37/624]	lr: 8.128e-07, eta: 2:34:38, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.9657, roadside_hm_loss: 4.3624, laneline_st_off_loss: 13.5178, roadside_st_off_loss: 14.8722, mask_loss: 0.7847, lane_type_mask_loss: 1.2221, lane_color_mask_loss: 0.5267, side_type_mask_loss: 0.8313, laneline_len_loss: 7.7389, laneline_field_loss: 2.7888, laneline_view_loss: 9.5754, laneline_inv_len_loss: 5.7634, laneline_inv_field_loss: 7.3047, roadside_len_loss: 7.7594, roadside_field_loss: 1.7066, roadside_view_loss: 5.1519, roadside_inv_len_loss: 5.1753, roadside_inv_field_loss: 3.1269, depth_loss: 0.2795, loss: 95.4535, grad_norm: 342.3594
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:32,410 - mme - INFO - laneline pred/gt: 7671/650
2025-03-06 15:07:32,411 - mme - INFO - roadside pred/gt: 7776/650
2025-03-06 15:07:32,698 - mme - INFO - Epoch [1][38/624]	lr: 8.326e-07, eta: 2:33:48, time: 0.397, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.0090, roadside_hm_loss: 6.1259, laneline_st_off_loss: 11.8322, roadside_st_off_loss: 14.1110, mask_loss: 1.0239, lane_type_mask_loss: 1.1880, lane_color_mask_loss: 0.5420, side_type_mask_loss: 0.8146, laneline_len_loss: 10.9989, laneline_field_loss: 2.3441, laneline_view_loss: 7.1626, laneline_inv_len_loss: 8.4007, laneline_inv_field_loss: 5.4193, roadside_len_loss: 11.0490, roadside_field_loss: 1.5635, roadside_view_loss: 4.1742, roadside_inv_len_loss: 7.5673, roadside_inv_field_loss: 2.6060, depth_loss: 0.1075, loss: 101.0397, grad_norm: 355.8279
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:32,810 - mme - INFO - laneline pred/gt: 7711/580
2025-03-06 15:07:32,811 - mme - INFO - roadside pred/gt: 7776/580
2025-03-06 15:07:33,100 - mme - INFO - Epoch [1][39/624]	lr: 8.524e-07, eta: 2:33:04, time: 0.402, data_time: 0.008, memory: 6893, laneline_hm_loss: 4.4219, roadside_hm_loss: 6.8667, laneline_st_off_loss: 11.4702, roadside_st_off_loss: 13.3585, mask_loss: 1.0454, lane_type_mask_loss: 1.1967, lane_color_mask_loss: 0.5395, side_type_mask_loss: 0.7945, laneline_len_loss: 11.2943, laneline_field_loss: 2.3006, laneline_view_loss: 7.2728, laneline_inv_len_loss: 8.6928, laneline_inv_field_loss: 5.3435, roadside_len_loss: 11.3983, roadside_field_loss: 1.6416, roadside_view_loss: 4.5743, roadside_inv_len_loss: 7.7481, roadside_inv_field_loss: 2.7447, depth_loss: 0.1417, loss: 102.8463, grad_norm: 368.2501
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:33,209 - mme - INFO - laneline pred/gt: 7665/577
2025-03-06 15:07:33,210 - mme - INFO - roadside pred/gt: 7776/577
2025-03-06 15:07:33,499 - mme - INFO - Epoch [1][40/624]	lr: 8.722e-07, eta: 2:32:20, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.4445, roadside_hm_loss: 6.9311, laneline_st_off_loss: 12.1019, roadside_st_off_loss: 13.8648, mask_loss: 1.1992, lane_type_mask_loss: 1.1758, lane_color_mask_loss: 0.5160, side_type_mask_loss: 0.8165, laneline_len_loss: 13.3888, laneline_field_loss: 2.2934, laneline_view_loss: 6.3894, laneline_inv_len_loss: 10.3187, laneline_inv_field_loss: 4.6488, roadside_len_loss: 13.4395, roadside_field_loss: 1.6279, roadside_view_loss: 3.8583, roadside_inv_len_loss: 9.2790, roadside_inv_field_loss: 2.3186, depth_loss: 0.1031, loss: 108.7153, grad_norm: 403.6891
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:33,609 - mme - INFO - laneline pred/gt: 7697/716
2025-03-06 15:07:33,610 - mme - INFO - roadside pred/gt: 7776/716
2025-03-06 15:07:33,899 - mme - INFO - Epoch [1][41/624]	lr: 8.920e-07, eta: 2:31:39, time: 0.401, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.7122, roadside_hm_loss: 5.4441, laneline_st_off_loss: 14.0459, roadside_st_off_loss: 15.2658, mask_loss: 0.9651, lane_type_mask_loss: 1.2146, lane_color_mask_loss: 0.5041, side_type_mask_loss: 0.8102, laneline_len_loss: 9.7100, laneline_field_loss: 2.6832, laneline_view_loss: 9.0174, laneline_inv_len_loss: 7.4621, laneline_inv_field_loss: 6.6665, roadside_len_loss: 9.7857, roadside_field_loss: 1.7327, roadside_view_loss: 5.1462, roadside_inv_len_loss: 6.6762, roadside_inv_field_loss: 3.0620, depth_loss: 0.1628, loss: 104.0667, grad_norm: 370.2533
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:34,008 - mme - INFO - laneline pred/gt: 7673/700
2025-03-06 15:07:34,009 - mme - INFO - roadside pred/gt: 7776/700
2025-03-06 15:07:34,292 - mme - INFO - Epoch [1][42/624]	lr: 9.118e-07, eta: 2:30:57, time: 0.393, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.6474, roadside_hm_loss: 5.5235, laneline_st_off_loss: 13.4756, roadside_st_off_loss: 14.6744, mask_loss: 0.9000, lane_type_mask_loss: 1.2073, lane_color_mask_loss: 0.5100, side_type_mask_loss: 0.7917, laneline_len_loss: 9.3201, laneline_field_loss: 2.7859, laneline_view_loss: 8.7840, laneline_inv_len_loss: 7.0099, laneline_inv_field_loss: 6.2675, roadside_len_loss: 9.2497, roadside_field_loss: 1.8258, roadside_view_loss: 4.9592, roadside_inv_len_loss: 6.2640, roadside_inv_field_loss: 2.8509, depth_loss: 0.1784, loss: 100.2253, grad_norm: 405.8660
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:34,401 - mme - INFO - laneline pred/gt: 7669/670
2025-03-06 15:07:34,401 - mme - INFO - roadside pred/gt: 7776/670
2025-03-06 15:07:34,693 - mme - INFO - Epoch [1][43/624]	lr: 9.316e-07, eta: 2:30:20, time: 0.401, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.8250, roadside_hm_loss: 5.8340, laneline_st_off_loss: 13.3518, roadside_st_off_loss: 15.0428, mask_loss: 0.9723, lane_type_mask_loss: 1.1663, lane_color_mask_loss: 0.5108, side_type_mask_loss: 0.8071, laneline_len_loss: 11.4814, laneline_field_loss: 3.2840, laneline_view_loss: 10.7748, laneline_inv_len_loss: 8.8635, laneline_inv_field_loss: 7.1316, roadside_len_loss: 11.5026, roadside_field_loss: 2.0813, roadside_view_loss: 5.7514, roadside_inv_len_loss: 7.8931, roadside_inv_field_loss: 3.0206, depth_loss: 0.1522, loss: 113.4465, grad_norm: 404.5387
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:34,802 - mme - INFO - laneline pred/gt: 7618/838
2025-03-06 15:07:34,803 - mme - INFO - roadside pred/gt: 7776/838
2025-03-06 15:07:35,091 - mme - INFO - Epoch [1][44/624]	lr: 9.514e-07, eta: 2:29:43, time: 0.397, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.1817, roadside_hm_loss: 4.5471, laneline_st_off_loss: 15.3029, roadside_st_off_loss: 15.8503, mask_loss: 0.8528, lane_type_mask_loss: 1.1896, lane_color_mask_loss: 0.5033, side_type_mask_loss: 0.7730, laneline_len_loss: 8.2677, laneline_field_loss: 2.7167, laneline_view_loss: 9.0614, laneline_inv_len_loss: 6.3019, laneline_inv_field_loss: 6.6681, roadside_len_loss: 8.3796, roadside_field_loss: 1.7185, roadside_view_loss: 4.9718, roadside_inv_len_loss: 5.6023, roadside_inv_field_loss: 2.9352, depth_loss: 0.1748, loss: 98.9988, grad_norm: 333.5511
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:35,201 - mme - INFO - laneline pred/gt: 7670/775
2025-03-06 15:07:35,201 - mme - INFO - roadside pred/gt: 7776/775
2025-03-06 15:07:35,480 - mme - INFO - Epoch [1][45/624]	lr: 9.712e-07, eta: 2:29:05, time: 0.390, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.3348, roadside_hm_loss: 4.9175, laneline_st_off_loss: 13.3698, roadside_st_off_loss: 15.3042, mask_loss: 0.8367, lane_type_mask_loss: 1.1971, lane_color_mask_loss: 0.5037, side_type_mask_loss: 0.7696, laneline_len_loss: 8.4671, laneline_field_loss: 2.6596, laneline_view_loss: 8.7671, laneline_inv_len_loss: 6.5209, laneline_inv_field_loss: 6.5057, roadside_len_loss: 8.6299, roadside_field_loss: 1.7153, roadside_view_loss: 4.8911, roadside_inv_len_loss: 5.7990, roadside_inv_field_loss: 2.9004, depth_loss: 0.2153, loss: 97.3046, grad_norm: 340.3604
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:35,596 - mme - INFO - laneline pred/gt: 7652/582
2025-03-06 15:07:35,596 - mme - INFO - roadside pred/gt: 7776/582
2025-03-06 15:07:35,883 - mme - INFO - Epoch [1][46/624]	lr: 9.910e-07, eta: 2:28:33, time: 0.402, data_time: 0.011, memory: 6893, laneline_hm_loss: 4.3101, roadside_hm_loss: 6.5614, laneline_st_off_loss: 11.7209, roadside_st_off_loss: 13.3320, mask_loss: 1.0809, lane_type_mask_loss: 1.1778, lane_color_mask_loss: 0.5227, side_type_mask_loss: 0.7613, laneline_len_loss: 11.4054, laneline_field_loss: 2.2846, laneline_view_loss: 6.7477, laneline_inv_len_loss: 8.9255, laneline_inv_field_loss: 5.0428, roadside_len_loss: 11.5302, roadside_field_loss: 1.5740, roadside_view_loss: 4.0548, roadside_inv_len_loss: 7.9294, roadside_inv_field_loss: 2.5317, depth_loss: 0.1210, loss: 101.6142, grad_norm: 382.9155
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:35,992 - mme - INFO - laneline pred/gt: 7646/679
2025-03-06 15:07:35,993 - mme - INFO - roadside pred/gt: 7776/679
2025-03-06 15:07:36,287 - mme - INFO - Epoch [1][47/624]	lr: 1.011e-06, eta: 2:28:03, time: 0.403, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.7743, roadside_hm_loss: 5.5818, laneline_st_off_loss: 13.5885, roadside_st_off_loss: 13.4122, mask_loss: 0.9116, lane_type_mask_loss: 1.1677, lane_color_mask_loss: 0.5038, side_type_mask_loss: 0.7543, laneline_len_loss: 9.2164, laneline_field_loss: 2.3016, laneline_view_loss: 7.3886, laneline_inv_len_loss: 7.2309, laneline_inv_field_loss: 5.5460, roadside_len_loss: 9.4005, roadside_field_loss: 1.5848, roadside_view_loss: 4.5092, roadside_inv_len_loss: 6.3642, roadside_inv_field_loss: 2.6981, depth_loss: 0.1740, loss: 96.1084, grad_norm: 371.5811
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:36,397 - mme - INFO - laneline pred/gt: 7654/818
2025-03-06 15:07:36,397 - mme - INFO - roadside pred/gt: 7776/818
2025-03-06 15:07:36,679 - mme - INFO - Epoch [1][48/624]	lr: 1.031e-06, eta: 2:27:30, time: 0.393, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.1886, roadside_hm_loss: 4.5763, laneline_st_off_loss: 12.4885, roadside_st_off_loss: 13.5358, mask_loss: 0.7893, lane_type_mask_loss: 1.1377, lane_color_mask_loss: 0.4929, side_type_mask_loss: 0.7691, laneline_len_loss: 7.8627, laneline_field_loss: 2.6458, laneline_view_loss: 8.9436, laneline_inv_len_loss: 6.1102, laneline_inv_field_loss: 6.6058, roadside_len_loss: 7.9844, roadside_field_loss: 1.7333, roadside_view_loss: 5.1778, roadside_inv_len_loss: 5.3932, roadside_inv_field_loss: 2.9946, depth_loss: 0.1993, loss: 92.6287, grad_norm: 344.2459
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:36,797 - mme - INFO - laneline pred/gt: 7652/582
2025-03-06 15:07:36,797 - mme - INFO - roadside pred/gt: 7776/582
2025-03-06 15:07:37,087 - mme - INFO - Epoch [1][49/624]	lr: 1.050e-06, eta: 2:27:05, time: 0.408, data_time: 0.014, memory: 6893, laneline_hm_loss: 4.3174, roadside_hm_loss: 6.3796, laneline_st_off_loss: 12.5478, roadside_st_off_loss: 14.6072, mask_loss: 1.0738, lane_type_mask_loss: 1.1258, lane_color_mask_loss: 0.4720, side_type_mask_loss: 0.7699, laneline_len_loss: 12.8429, laneline_field_loss: 2.4777, laneline_view_loss: 8.4309, laneline_inv_len_loss: 10.1530, laneline_inv_field_loss: 6.1184, roadside_len_loss: 12.8908, roadside_field_loss: 1.7542, roadside_view_loss: 5.2565, roadside_inv_len_loss: 9.0035, roadside_inv_field_loss: 3.1034, depth_loss: 0.1360, loss: 113.4607, grad_norm: 466.0237
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:37,196 - mme - INFO - laneline pred/gt: 7646/964
2025-03-06 15:07:37,197 - mme - INFO - roadside pred/gt: 7776/964
2025-03-06 15:07:37,477 - mme - INFO - Epoch [1][50/624]	lr: 1.070e-06, eta: 2:26:34, time: 0.391, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7012, roadside_hm_loss: 3.8082, laneline_st_off_loss: 13.8917, roadside_st_off_loss: 14.0332, mask_loss: 0.7181, lane_type_mask_loss: 1.1452, lane_color_mask_loss: 0.4844, side_type_mask_loss: 0.7473, laneline_len_loss: 6.9216, laneline_field_loss: 3.0310, laneline_view_loss: 10.3772, laneline_inv_len_loss: 5.3822, laneline_inv_field_loss: 7.5513, roadside_len_loss: 7.0456, roadside_field_loss: 1.9028, roadside_view_loss: 5.5622, roadside_inv_len_loss: 4.7752, roadside_inv_field_loss: 3.1164, depth_loss: 0.2802, loss: 93.4749, grad_norm: 298.0951
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:37,594 - mme - INFO - laneline pred/gt: 7609/838
2025-03-06 15:07:37,595 - mme - INFO - roadside pred/gt: 7776/838
2025-03-06 15:07:37,885 - mme - INFO - Epoch [1][51/624]	lr: 1.090e-06, eta: 2:26:10, time: 0.408, data_time: 0.013, memory: 6893, laneline_hm_loss: 3.0763, roadside_hm_loss: 4.3690, laneline_st_off_loss: 12.6120, roadside_st_off_loss: 14.9203, mask_loss: 0.7792, lane_type_mask_loss: 1.1535, lane_color_mask_loss: 0.4867, side_type_mask_loss: 0.7325, laneline_len_loss: 7.5025, laneline_field_loss: 2.7297, laneline_view_loss: 9.0312, laneline_inv_len_loss: 5.8424, laneline_inv_field_loss: 6.6305, roadside_len_loss: 7.6673, roadside_field_loss: 1.7175, roadside_view_loss: 5.0301, roadside_inv_len_loss: 5.1701, roadside_inv_field_loss: 2.9283, depth_loss: 0.2591, loss: 92.6380, grad_norm: 357.8520
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:37,995 - mme - INFO - laneline pred/gt: 7571/972
2025-03-06 15:07:37,995 - mme - INFO - roadside pred/gt: 7776/972
2025-03-06 15:07:38,276 - mme - INFO - Epoch [1][52/624]	lr: 1.110e-06, eta: 2:25:41, time: 0.391, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.7185, roadside_hm_loss: 3.7588, laneline_st_off_loss: 12.4085, roadside_st_off_loss: 14.7020, mask_loss: 0.6973, lane_type_mask_loss: 1.1362, lane_color_mask_loss: 0.4682, side_type_mask_loss: 0.7428, laneline_len_loss: 6.6094, laneline_field_loss: 2.8704, laneline_view_loss: 10.4089, laneline_inv_len_loss: 5.1622, laneline_inv_field_loss: 7.9249, roadside_len_loss: 6.7133, roadside_field_loss: 1.7810, roadside_view_loss: 5.7411, roadside_inv_len_loss: 4.5321, roadside_inv_field_loss: 3.4031, depth_loss: 0.2187, loss: 91.9972, grad_norm: 329.0435
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:38,388 - mme - INFO - laneline pred/gt: 7566/764
2025-03-06 15:07:38,388 - mme - INFO - roadside pred/gt: 7776/764
2025-03-06 15:07:38,669 - mme - INFO - Epoch [1][53/624]	lr: 1.130e-06, eta: 2:25:14, time: 0.393, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.3981, roadside_hm_loss: 4.7681, laneline_st_off_loss: 13.4465, roadside_st_off_loss: 15.7156, mask_loss: 0.8666, lane_type_mask_loss: 1.1025, lane_color_mask_loss: 0.4722, side_type_mask_loss: 0.7417, laneline_len_loss: 9.2304, laneline_field_loss: 2.7920, laneline_view_loss: 9.6339, laneline_inv_len_loss: 7.3731, laneline_inv_field_loss: 7.0251, roadside_len_loss: 9.4368, roadside_field_loss: 1.8305, roadside_view_loss: 5.2443, roadside_inv_len_loss: 6.3728, roadside_inv_field_loss: 3.0589, depth_loss: 0.1579, loss: 102.6671, grad_norm: 352.0781
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:38,780 - mme - INFO - laneline pred/gt: 7560/787
2025-03-06 15:07:38,780 - mme - INFO - roadside pred/gt: 7776/787
2025-03-06 15:07:39,067 - mme - INFO - Epoch [1][54/624]	lr: 1.149e-06, eta: 2:24:50, time: 0.398, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.1214, roadside_hm_loss: 4.6001, laneline_st_off_loss: 11.5109, roadside_st_off_loss: 12.7946, mask_loss: 0.7245, lane_type_mask_loss: 1.1123, lane_color_mask_loss: 0.4878, side_type_mask_loss: 0.7211, laneline_len_loss: 7.2034, laneline_field_loss: 2.7165, laneline_view_loss: 9.3944, laneline_inv_len_loss: 5.6817, laneline_inv_field_loss: 6.8920, roadside_len_loss: 7.3192, roadside_field_loss: 1.7641, roadside_view_loss: 5.3696, roadside_inv_len_loss: 5.0293, roadside_inv_field_loss: 3.1215, depth_loss: 0.2672, loss: 89.8315, grad_norm: 317.1807
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:39,177 - mme - INFO - laneline pred/gt: 7572/772
2025-03-06 15:07:39,178 - mme - INFO - roadside pred/gt: 7774/772
2025-03-06 15:07:39,458 - mme - INFO - Epoch [1][55/624]	lr: 1.169e-06, eta: 2:24:24, time: 0.391, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.2552, roadside_hm_loss: 4.6242, laneline_st_off_loss: 13.0449, roadside_st_off_loss: 15.0516, mask_loss: 0.7722, lane_type_mask_loss: 1.1004, lane_color_mask_loss: 0.4688, side_type_mask_loss: 0.7208, laneline_len_loss: 8.2997, laneline_field_loss: 2.7009, laneline_view_loss: 8.6385, laneline_inv_len_loss: 6.5415, laneline_inv_field_loss: 6.3723, roadside_len_loss: 8.4591, roadside_field_loss: 1.7013, roadside_view_loss: 4.6440, roadside_inv_len_loss: 5.6725, roadside_inv_field_loss: 2.7610, depth_loss: 0.1716, loss: 95.0005, grad_norm: 380.3981
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:39,575 - mme - INFO - laneline pred/gt: 7551/731
2025-03-06 15:07:39,576 - mme - INFO - roadside pred/gt: 7776/731
2025-03-06 15:07:39,866 - mme - INFO - Epoch [1][56/624]	lr: 1.189e-06, eta: 2:24:05, time: 0.407, data_time: 0.013, memory: 6893, laneline_hm_loss: 3.3316, roadside_hm_loss: 4.8866, laneline_st_off_loss: 12.0163, roadside_st_off_loss: 13.2888, mask_loss: 0.8395, lane_type_mask_loss: 1.1229, lane_color_mask_loss: 0.4610, side_type_mask_loss: 0.7087, laneline_len_loss: 8.8794, laneline_field_loss: 2.4046, laneline_view_loss: 7.7913, laneline_inv_len_loss: 7.1415, laneline_inv_field_loss: 5.6994, roadside_len_loss: 9.1444, roadside_field_loss: 1.6111, roadside_view_loss: 4.5817, roadside_inv_len_loss: 6.1748, roadside_inv_field_loss: 2.6688, depth_loss: 0.1516, loss: 92.9038, grad_norm: 356.8515
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:39,977 - mme - INFO - laneline pred/gt: 7585/660
2025-03-06 15:07:39,977 - mme - INFO - roadside pred/gt: 7776/660
2025-03-06 15:07:40,265 - mme - INFO - Epoch [1][57/624]	lr: 1.209e-06, eta: 2:23:44, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.6124, roadside_hm_loss: 5.3547, laneline_st_off_loss: 11.4836, roadside_st_off_loss: 12.5373, mask_loss: 0.8113, lane_type_mask_loss: 1.0875, lane_color_mask_loss: 0.4644, side_type_mask_loss: 0.6998, laneline_len_loss: 8.7432, laneline_field_loss: 2.5012, laneline_view_loss: 8.4792, laneline_inv_len_loss: 7.0328, laneline_inv_field_loss: 6.2955, roadside_len_loss: 9.0101, roadside_field_loss: 1.6346, roadside_view_loss: 4.9894, roadside_inv_len_loss: 6.0189, roadside_inv_field_loss: 2.9666, depth_loss: 0.1877, loss: 93.9100, grad_norm: 350.2779
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:40,375 - mme - INFO - laneline pred/gt: 7588/896
2025-03-06 15:07:40,376 - mme - INFO - roadside pred/gt: 7776/896
2025-03-06 15:07:40,664 - mme - INFO - Epoch [1][58/624]	lr: 1.229e-06, eta: 2:23:21, time: 0.393, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7678, roadside_hm_loss: 3.9064, laneline_st_off_loss: 11.8636, roadside_st_off_loss: 14.3108, mask_loss: 0.6506, lane_type_mask_loss: 1.0894, lane_color_mask_loss: 0.4549, side_type_mask_loss: 0.7131, laneline_len_loss: 5.9792, laneline_field_loss: 2.8218, laneline_view_loss: 9.8792, laneline_inv_len_loss: 4.8054, laneline_inv_field_loss: 7.1928, roadside_len_loss: 6.1760, roadside_field_loss: 1.8419, roadside_view_loss: 5.6088, roadside_inv_len_loss: 4.1384, roadside_inv_field_loss: 3.1546, depth_loss: 0.2581, loss: 87.6129, grad_norm: 321.3382
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:40,775 - mme - INFO - laneline pred/gt: 7629/693
2025-03-06 15:07:40,776 - mme - INFO - roadside pred/gt: 7776/693
2025-03-06 15:07:41,063 - mme - INFO - Epoch [1][59/624]	lr: 1.248e-06, eta: 2:23:01, time: 0.399, data_time: 0.014, memory: 6893, laneline_hm_loss: 3.4356, roadside_hm_loss: 4.9836, laneline_st_off_loss: 9.6599, roadside_st_off_loss: 10.9008, mask_loss: 0.7409, lane_type_mask_loss: 1.0771, lane_color_mask_loss: 0.4670, side_type_mask_loss: 0.7061, laneline_len_loss: 7.3869, laneline_field_loss: 2.3492, laneline_view_loss: 7.4572, laneline_inv_len_loss: 5.8416, laneline_inv_field_loss: 5.7475, roadside_len_loss: 7.6010, roadside_field_loss: 1.5466, roadside_view_loss: 4.3654, roadside_inv_len_loss: 5.0863, roadside_inv_field_loss: 2.7651, depth_loss: 0.1428, loss: 82.2607, grad_norm: 364.3546
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:41,174 - mme - INFO - laneline pred/gt: 7536/900
2025-03-06 15:07:41,175 - mme - INFO - roadside pred/gt: 7776/900
2025-03-06 15:07:41,454 - mme - INFO - Epoch [1][60/624]	lr: 1.268e-06, eta: 2:22:41, time: 0.398, data_time: 0.012, memory: 6893, laneline_hm_loss: 2.7333, roadside_hm_loss: 3.8129, laneline_st_off_loss: 11.8201, roadside_st_off_loss: 13.2526, mask_loss: 0.6395, lane_type_mask_loss: 1.0722, lane_color_mask_loss: 0.4439, side_type_mask_loss: 0.6883, laneline_len_loss: 5.6180, laneline_field_loss: 2.5746, laneline_view_loss: 8.6105, laneline_inv_len_loss: 4.4216, laneline_inv_field_loss: 6.5965, roadside_len_loss: 5.8443, roadside_field_loss: 1.6614, roadside_view_loss: 4.7985, roadside_inv_len_loss: 3.7906, roadside_inv_field_loss: 2.9292, depth_loss: 0.2156, loss: 81.5235, grad_norm: 328.7099
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:41,563 - mme - INFO - laneline pred/gt: 7543/672
2025-03-06 15:07:41,564 - mme - INFO - roadside pred/gt: 7776/672
2025-03-06 15:07:41,856 - mme - INFO - Epoch [1][61/624]	lr: 1.288e-06, eta: 2:22:23, time: 0.401, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.5185, roadside_hm_loss: 5.0385, laneline_st_off_loss: 10.4316, roadside_st_off_loss: 12.5773, mask_loss: 0.7434, lane_type_mask_loss: 1.0753, lane_color_mask_loss: 0.4489, side_type_mask_loss: 0.6853, laneline_len_loss: 7.5483, laneline_field_loss: 2.5689, laneline_view_loss: 8.6728, laneline_inv_len_loss: 6.0895, laneline_inv_field_loss: 6.5005, roadside_len_loss: 7.8368, roadside_field_loss: 1.6485, roadside_view_loss: 5.0144, roadside_inv_len_loss: 5.2091, roadside_inv_field_loss: 3.0609, depth_loss: 0.1077, loss: 88.7759, grad_norm: 396.0734
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:41,964 - mme - INFO - laneline pred/gt: 7543/898
2025-03-06 15:07:41,965 - mme - INFO - roadside pred/gt: 7776/898
2025-03-06 15:07:42,256 - mme - INFO - Epoch [1][62/624]	lr: 1.308e-06, eta: 2:22:05, time: 0.400, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.8212, roadside_hm_loss: 3.8096, laneline_st_off_loss: 12.9710, roadside_st_off_loss: 13.9164, mask_loss: 0.6975, lane_type_mask_loss: 1.0713, lane_color_mask_loss: 0.4438, side_type_mask_loss: 0.6837, laneline_len_loss: 6.4055, laneline_field_loss: 3.0063, laneline_view_loss: 8.9128, laneline_inv_len_loss: 5.1933, laneline_inv_field_loss: 6.5158, roadside_len_loss: 6.6572, roadside_field_loss: 1.8988, roadside_view_loss: 4.5758, roadside_inv_len_loss: 4.4432, roadside_inv_field_loss: 2.6605, depth_loss: 0.1989, loss: 86.8824, grad_norm: 350.7653
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:42,364 - mme - INFO - laneline pred/gt: 7502/875
2025-03-06 15:07:42,365 - mme - INFO - roadside pred/gt: 7776/875
2025-03-06 15:07:42,654 - mme - INFO - Epoch [1][63/624]	lr: 1.328e-06, eta: 2:21:47, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.8193, roadside_hm_loss: 3.8984, laneline_st_off_loss: 12.7854, roadside_st_off_loss: 13.8139, mask_loss: 0.6897, lane_type_mask_loss: 1.0616, lane_color_mask_loss: 0.4421, side_type_mask_loss: 0.6670, laneline_len_loss: 6.3547, laneline_field_loss: 2.6717, laneline_view_loss: 9.5885, laneline_inv_len_loss: 5.2045, laneline_inv_field_loss: 7.3740, roadside_len_loss: 6.6430, roadside_field_loss: 1.6924, roadside_view_loss: 5.4227, roadside_inv_len_loss: 4.4384, roadside_inv_field_loss: 3.2791, depth_loss: 0.1968, loss: 89.0433, grad_norm: 294.4196
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:42,763 - mme - INFO - laneline pred/gt: 7453/861
2025-03-06 15:07:42,763 - mme - INFO - roadside pred/gt: 7774/861
2025-03-06 15:07:43,054 - mme - INFO - Epoch [1][64/624]	lr: 1.347e-06, eta: 2:21:30, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7881, roadside_hm_loss: 3.9338, laneline_st_off_loss: 10.5956, roadside_st_off_loss: 11.2979, mask_loss: 0.6557, lane_type_mask_loss: 1.0551, lane_color_mask_loss: 0.4470, side_type_mask_loss: 0.6728, laneline_len_loss: 6.1773, laneline_field_loss: 2.7137, laneline_view_loss: 9.2145, laneline_inv_len_loss: 5.1893, laneline_inv_field_loss: 6.8729, roadside_len_loss: 6.4729, roadside_field_loss: 1.7700, roadside_view_loss: 5.1171, roadside_inv_len_loss: 4.4488, roadside_inv_field_loss: 3.0157, depth_loss: 0.2579, loss: 82.6963, grad_norm: 287.9174
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:43,163 - mme - INFO - laneline pred/gt: 7429/614
2025-03-06 15:07:43,164 - mme - INFO - roadside pred/gt: 7776/614
2025-03-06 15:07:43,454 - mme - INFO - Epoch [1][65/624]	lr: 1.367e-06, eta: 2:21:14, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.7282, roadside_hm_loss: 5.3781, laneline_st_off_loss: 10.1846, roadside_st_off_loss: 12.5538, mask_loss: 0.8009, lane_type_mask_loss: 1.0399, lane_color_mask_loss: 0.4379, side_type_mask_loss: 0.6496, laneline_len_loss: 7.9111, laneline_field_loss: 2.4133, laneline_view_loss: 8.0319, laneline_inv_len_loss: 6.6955, laneline_inv_field_loss: 5.9871, roadside_len_loss: 8.2658, roadside_field_loss: 1.6602, roadside_view_loss: 4.9453, roadside_inv_len_loss: 5.6442, roadside_inv_field_loss: 2.9964, depth_loss: 0.1343, loss: 89.4581, grad_norm: 352.6098
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:43,563 - mme - INFO - laneline pred/gt: 7421/769
2025-03-06 15:07:43,564 - mme - INFO - roadside pred/gt: 7776/769
2025-03-06 15:07:43,855 - mme - INFO - Epoch [1][66/624]	lr: 1.387e-06, eta: 2:20:58, time: 0.401, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.1198, roadside_hm_loss: 4.2989, laneline_st_off_loss: 11.5643, roadside_st_off_loss: 13.3418, mask_loss: 0.7194, lane_type_mask_loss: 1.0058, lane_color_mask_loss: 0.4261, side_type_mask_loss: 0.6705, laneline_len_loss: 6.8133, laneline_field_loss: 2.6029, laneline_view_loss: 8.8697, laneline_inv_len_loss: 5.6395, laneline_inv_field_loss: 6.5458, roadside_len_loss: 7.0985, roadside_field_loss: 1.7275, roadside_view_loss: 5.1240, roadside_inv_len_loss: 4.7507, roadside_inv_field_loss: 3.0222, depth_loss: 0.1998, loss: 87.5406, grad_norm: 355.7198
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:43,965 - mme - INFO - laneline pred/gt: 7404/467
2025-03-06 15:07:43,965 - mme - INFO - roadside pred/gt: 7775/467
2025-03-06 15:07:44,256 - mme - INFO - Epoch [1][67/624]	lr: 1.407e-06, eta: 2:20:43, time: 0.402, data_time: 0.007, memory: 6893, laneline_hm_loss: 4.6848, roadside_hm_loss: 6.9609, laneline_st_off_loss: 10.0908, roadside_st_off_loss: 11.1627, mask_loss: 0.9548, lane_type_mask_loss: 1.0055, lane_color_mask_loss: 0.4473, side_type_mask_loss: 0.6555, laneline_len_loss: 10.9512, laneline_field_loss: 2.0839, laneline_view_loss: 5.8815, laneline_inv_len_loss: 9.3112, laneline_inv_field_loss: 4.3402, roadside_len_loss: 11.5008, roadside_field_loss: 1.5358, roadside_view_loss: 3.7421, roadside_inv_len_loss: 7.6697, roadside_inv_field_loss: 2.3415, depth_loss: 0.0814, loss: 95.4018, grad_norm: 441.8106
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:44,365 - mme - INFO - laneline pred/gt: 7423/793
2025-03-06 15:07:44,365 - mme - INFO - roadside pred/gt: 7776/793
2025-03-06 15:07:44,657 - mme - INFO - Epoch [1][68/624]	lr: 1.427e-06, eta: 2:20:29, time: 0.401, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.9650, roadside_hm_loss: 4.1484, laneline_st_off_loss: 10.5114, roadside_st_off_loss: 11.9541, mask_loss: 0.6343, lane_type_mask_loss: 0.9877, lane_color_mask_loss: 0.4167, side_type_mask_loss: 0.6690, laneline_len_loss: 5.7953, laneline_field_loss: 2.5830, laneline_view_loss: 8.6899, laneline_inv_len_loss: 4.7557, laneline_inv_field_loss: 6.5447, roadside_len_loss: 6.0784, roadside_field_loss: 1.6524, roadside_view_loss: 4.9720, roadside_inv_len_loss: 3.9374, roadside_inv_field_loss: 3.0370, depth_loss: 0.2246, loss: 80.5569, grad_norm: 342.7898
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:44,766 - mme - INFO - laneline pred/gt: 7361/851
2025-03-06 15:07:44,766 - mme - INFO - roadside pred/gt: 7776/851
2025-03-06 15:07:45,054 - mme - INFO - Epoch [1][69/624]	lr: 1.446e-06, eta: 2:20:13, time: 0.397, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.7811, roadside_hm_loss: 3.8317, laneline_st_off_loss: 11.4099, roadside_st_off_loss: 12.5714, mask_loss: 0.6577, lane_type_mask_loss: 0.9953, lane_color_mask_loss: 0.4084, side_type_mask_loss: 0.6529, laneline_len_loss: 6.1402, laneline_field_loss: 2.8320, laneline_view_loss: 10.1146, laneline_inv_len_loss: 5.2272, laneline_inv_field_loss: 7.4732, roadside_len_loss: 6.3424, roadside_field_loss: 1.8105, roadside_view_loss: 5.6048, roadside_inv_len_loss: 4.4144, roadside_inv_field_loss: 3.2177, depth_loss: 0.2706, loss: 86.7560, grad_norm: 343.9923
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:45,164 - mme - INFO - laneline pred/gt: 7334/870
2025-03-06 15:07:45,165 - mme - INFO - roadside pred/gt: 7776/870
2025-03-06 15:07:45,457 - mme - INFO - Epoch [1][70/624]	lr: 1.466e-06, eta: 2:20:00, time: 0.403, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7236, roadside_hm_loss: 3.6564, laneline_st_off_loss: 11.4641, roadside_st_off_loss: 12.2618, mask_loss: 0.5943, lane_type_mask_loss: 0.9666, lane_color_mask_loss: 0.3952, side_type_mask_loss: 0.6538, laneline_len_loss: 4.8563, laneline_field_loss: 2.5132, laneline_view_loss: 8.4293, laneline_inv_len_loss: 4.0133, laneline_inv_field_loss: 6.5746, roadside_len_loss: 5.0797, roadside_field_loss: 1.6302, roadside_view_loss: 4.8738, roadside_inv_len_loss: 3.2869, roadside_inv_field_loss: 2.9936, depth_loss: 0.2559, loss: 77.2226, grad_norm: 342.0298
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:45,566 - mme - INFO - laneline pred/gt: 7351/498
2025-03-06 15:07:45,567 - mme - INFO - roadside pred/gt: 7774/498
2025-03-06 15:07:45,856 - mme - INFO - Epoch [1][71/624]	lr: 1.486e-06, eta: 2:19:46, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.2075, roadside_hm_loss: 6.3249, laneline_st_off_loss: 8.8516, roadside_st_off_loss: 9.4103, mask_loss: 0.7555, lane_type_mask_loss: 0.9989, lane_color_mask_loss: 0.4251, side_type_mask_loss: 0.6452, laneline_len_loss: 7.1142, laneline_field_loss: 2.1916, laneline_view_loss: 6.9901, laneline_inv_len_loss: 6.1689, laneline_inv_field_loss: 5.3159, roadside_len_loss: 7.5926, roadside_field_loss: 1.5673, roadside_view_loss: 4.6244, roadside_inv_len_loss: 4.9927, roadside_inv_field_loss: 2.9295, depth_loss: 0.1318, loss: 81.2380, grad_norm: 373.6541
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:45,965 - mme - INFO - laneline pred/gt: 7344/486
2025-03-06 15:07:45,966 - mme - INFO - roadside pred/gt: 7773/486
2025-03-06 15:07:46,247 - mme - INFO - Epoch [1][72/624]	lr: 1.506e-06, eta: 2:19:31, time: 0.391, data_time: 0.006, memory: 6893, laneline_hm_loss: 4.3442, roadside_hm_loss: 6.4444, laneline_st_off_loss: 9.4756, roadside_st_off_loss: 9.9293, mask_loss: 0.8115, lane_type_mask_loss: 0.9729, lane_color_mask_loss: 0.4307, side_type_mask_loss: 0.6328, laneline_len_loss: 8.2655, laneline_field_loss: 1.9251, laneline_view_loss: 5.4759, laneline_inv_len_loss: 7.2491, laneline_inv_field_loss: 4.1521, roadside_len_loss: 8.8742, roadside_field_loss: 1.4946, roadside_view_loss: 3.7667, roadside_inv_len_loss: 5.8908, roadside_inv_field_loss: 2.3813, depth_loss: 0.1510, loss: 82.6675, grad_norm: 370.9011
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:46,363 - mme - INFO - laneline pred/gt: 7240/863
2025-03-06 15:07:46,364 - mme - INFO - roadside pred/gt: 7774/863
2025-03-06 15:07:46,656 - mme - INFO - Epoch [1][73/624]	lr: 1.526e-06, eta: 2:19:20, time: 0.409, data_time: 0.013, memory: 6893, laneline_hm_loss: 2.7752, roadside_hm_loss: 3.6326, laneline_st_off_loss: 12.0975, roadside_st_off_loss: 12.6379, mask_loss: 0.6514, lane_type_mask_loss: 0.9482, lane_color_mask_loss: 0.3859, side_type_mask_loss: 0.6139, laneline_len_loss: 5.1390, laneline_field_loss: 2.6831, laneline_view_loss: 9.0901, laneline_inv_len_loss: 4.4109, laneline_inv_field_loss: 6.7450, roadside_len_loss: 5.5936, roadside_field_loss: 1.7088, roadside_view_loss: 4.9101, roadside_inv_len_loss: 3.4565, roadside_inv_field_loss: 2.9101, depth_loss: 0.2524, loss: 80.6423, grad_norm: 305.4144
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:46,765 - mme - INFO - laneline pred/gt: 7211/672
2025-03-06 15:07:46,765 - mme - INFO - roadside pred/gt: 7774/672
2025-03-06 15:07:47,054 - mme - INFO - Epoch [1][74/624]	lr: 1.545e-06, eta: 2:19:07, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.2749, roadside_hm_loss: 4.6019, laneline_st_off_loss: 10.2566, roadside_st_off_loss: 12.1765, mask_loss: 0.6963, lane_type_mask_loss: 0.9794, lane_color_mask_loss: 0.4267, side_type_mask_loss: 0.6049, laneline_len_loss: 6.0764, laneline_field_loss: 2.6792, laneline_view_loss: 8.4501, laneline_inv_len_loss: 5.5275, laneline_inv_field_loss: 6.0179, roadside_len_loss: 6.5263, roadside_field_loss: 1.8029, roadside_view_loss: 4.8649, roadside_inv_len_loss: 4.3904, roadside_inv_field_loss: 2.7630, depth_loss: 0.1925, loss: 82.3084, grad_norm: 374.5518
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:47,164 - mme - INFO - laneline pred/gt: 7214/784
2025-03-06 15:07:47,164 - mme - INFO - roadside pred/gt: 7773/784
2025-03-06 15:07:47,459 - mme - INFO - Epoch [1][75/624]	lr: 1.565e-06, eta: 2:18:56, time: 0.404, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.8698, roadside_hm_loss: 3.9072, laneline_st_off_loss: 10.0782, roadside_st_off_loss: 9.9622, mask_loss: 0.6211, lane_type_mask_loss: 0.9506, lane_color_mask_loss: 0.4040, side_type_mask_loss: 0.6004, laneline_len_loss: 5.2678, laneline_field_loss: 2.5438, laneline_view_loss: 8.4540, laneline_inv_len_loss: 4.7334, laneline_inv_field_loss: 6.3124, roadside_len_loss: 5.6926, roadside_field_loss: 1.6777, roadside_view_loss: 4.8061, roadside_inv_len_loss: 3.7988, roadside_inv_field_loss: 2.8579, depth_loss: 0.2505, loss: 75.7886, grad_norm: 364.1930
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:47,569 - mme - INFO - laneline pred/gt: 7190/731
2025-03-06 15:07:47,569 - mme - INFO - roadside pred/gt: 7769/731
2025-03-06 15:07:47,850 - mme - INFO - Epoch [1][76/624]	lr: 1.585e-06, eta: 2:18:42, time: 0.392, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.0314, roadside_hm_loss: 4.1339, laneline_st_off_loss: 10.3916, roadside_st_off_loss: 10.9009, mask_loss: 0.6837, lane_type_mask_loss: 0.9400, lane_color_mask_loss: 0.4103, side_type_mask_loss: 0.6062, laneline_len_loss: 5.3309, laneline_field_loss: 2.3966, laneline_view_loss: 8.0975, laneline_inv_len_loss: 4.8797, laneline_inv_field_loss: 6.1132, roadside_len_loss: 5.8480, roadside_field_loss: 1.6437, roadside_view_loss: 4.9286, roadside_inv_len_loss: 3.8061, roadside_inv_field_loss: 2.9718, depth_loss: 0.1594, loss: 77.2735, grad_norm: 323.7628
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:47,965 - mme - INFO - laneline pred/gt: 7097/697
2025-03-06 15:07:47,966 - mme - INFO - roadside pred/gt: 7765/697
2025-03-06 15:07:48,247 - mme - INFO - Epoch [1][77/624]	lr: 1.605e-06, eta: 2:18:29, time: 0.397, data_time: 0.012, memory: 6893, laneline_hm_loss: 3.1528, roadside_hm_loss: 4.2979, laneline_st_off_loss: 12.1377, roadside_st_off_loss: 12.9045, mask_loss: 0.6834, lane_type_mask_loss: 0.8887, lane_color_mask_loss: 0.3812, side_type_mask_loss: 0.6069, laneline_len_loss: 6.1698, laneline_field_loss: 2.7044, laneline_view_loss: 9.5978, laneline_inv_len_loss: 5.6691, laneline_inv_field_loss: 7.1163, roadside_len_loss: 6.7117, roadside_field_loss: 1.7914, roadside_view_loss: 5.4466, roadside_inv_len_loss: 4.3374, roadside_inv_field_loss: 3.2369, depth_loss: 0.1865, loss: 88.0213, grad_norm: 386.6156
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:48,357 - mme - INFO - laneline pred/gt: 7049/851
2025-03-06 15:07:48,357 - mme - INFO - roadside pred/gt: 7770/851
2025-03-06 15:07:48,647 - mme - INFO - Epoch [1][78/624]	lr: 1.625e-06, eta: 2:18:16, time: 0.393, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.6337, roadside_hm_loss: 3.4825, laneline_st_off_loss: 11.1793, roadside_st_off_loss: 11.5354, mask_loss: 0.5957, lane_type_mask_loss: 0.9013, lane_color_mask_loss: 0.3755, side_type_mask_loss: 0.5824, laneline_len_loss: 4.3547, laneline_field_loss: 2.7418, laneline_view_loss: 8.6162, laneline_inv_len_loss: 3.8619, laneline_inv_field_loss: 6.4811, roadside_len_loss: 4.7370, roadside_field_loss: 1.7394, roadside_view_loss: 4.7044, roadside_inv_len_loss: 2.9906, roadside_inv_field_loss: 2.8257, depth_loss: 0.1889, loss: 74.5275, grad_norm: 329.2663
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:48,756 - mme - INFO - laneline pred/gt: 7090/755
2025-03-06 15:07:48,756 - mme - INFO - roadside pred/gt: 7770/755
2025-03-06 15:07:49,047 - mme - INFO - Epoch [1][79/624]	lr: 1.644e-06, eta: 2:18:07, time: 0.406, data_time: 0.012, memory: 6893, laneline_hm_loss: 2.8813, roadside_hm_loss: 3.9112, laneline_st_off_loss: 12.3931, roadside_st_off_loss: 11.8019, mask_loss: 0.6216, lane_type_mask_loss: 0.8950, lane_color_mask_loss: 0.3760, side_type_mask_loss: 0.5787, laneline_len_loss: 4.9812, laneline_field_loss: 2.6034, laneline_view_loss: 8.7078, laneline_inv_len_loss: 4.6585, laneline_inv_field_loss: 6.3930, roadside_len_loss: 5.5310, roadside_field_loss: 1.6970, roadside_view_loss: 4.8570, roadside_inv_len_loss: 3.4780, roadside_inv_field_loss: 2.8648, depth_loss: 0.1315, loss: 79.3620, grad_norm: 344.7296
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:49,158 - mme - INFO - laneline pred/gt: 6973/643
2025-03-06 15:07:49,158 - mme - INFO - roadside pred/gt: 7766/643
2025-03-06 15:07:49,448 - mme - INFO - Epoch [1][80/624]	lr: 1.664e-06, eta: 2:17:56, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.2655, roadside_hm_loss: 4.5374, laneline_st_off_loss: 9.6996, roadside_st_off_loss: 11.4189, mask_loss: 0.6471, lane_type_mask_loss: 0.8901, lane_color_mask_loss: 0.3770, side_type_mask_loss: 0.5699, laneline_len_loss: 5.1078, laneline_field_loss: 2.5482, laneline_view_loss: 8.2830, laneline_inv_len_loss: 4.8999, laneline_inv_field_loss: 6.1118, roadside_len_loss: 5.7315, roadside_field_loss: 1.7518, roadside_view_loss: 4.9559, roadside_inv_len_loss: 3.6435, roadside_inv_field_loss: 2.9572, depth_loss: 0.1956, loss: 77.5920, grad_norm: 360.1018
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:49,557 - mme - INFO - laneline pred/gt: 6888/775
2025-03-06 15:07:49,558 - mme - INFO - roadside pred/gt: 7772/775
2025-03-06 15:07:49,847 - mme - INFO - Epoch [1][81/624]	lr: 1.684e-06, eta: 2:17:45, time: 0.398, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.8425, roadside_hm_loss: 3.7546, laneline_st_off_loss: 10.6303, roadside_st_off_loss: 11.1951, mask_loss: 0.6271, lane_type_mask_loss: 0.8791, lane_color_mask_loss: 0.3610, side_type_mask_loss: 0.5700, laneline_len_loss: 4.4717, laneline_field_loss: 2.4820, laneline_view_loss: 8.4277, laneline_inv_len_loss: 4.1538, laneline_inv_field_loss: 6.4129, roadside_len_loss: 5.0616, roadside_field_loss: 1.5939, roadside_view_loss: 4.8359, roadside_inv_len_loss: 3.0076, roadside_inv_field_loss: 2.9364, depth_loss: 0.1625, loss: 74.4057, grad_norm: 358.6964
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:49,957 - mme - INFO - laneline pred/gt: 6935/887
2025-03-06 15:07:49,957 - mme - INFO - roadside pred/gt: 7772/887
2025-03-06 15:07:50,242 - mme - INFO - Epoch [1][82/624]	lr: 1.704e-06, eta: 2:17:34, time: 0.396, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.4544, roadside_hm_loss: 3.2683, laneline_st_off_loss: 10.4563, roadside_st_off_loss: 11.0560, mask_loss: 0.6050, lane_type_mask_loss: 0.8816, lane_color_mask_loss: 0.3673, side_type_mask_loss: 0.5494, laneline_len_loss: 3.9947, laneline_field_loss: 2.7927, laneline_view_loss: 9.9614, laneline_inv_len_loss: 3.9908, laneline_inv_field_loss: 7.3582, roadside_len_loss: 4.4782, roadside_field_loss: 1.8008, roadside_view_loss: 5.5034, roadside_inv_len_loss: 2.9279, roadside_inv_field_loss: 3.1430, depth_loss: 0.2567, loss: 75.8461, grad_norm: 306.8260
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:50,357 - mme - INFO - laneline pred/gt: 6759/720
2025-03-06 15:07:50,358 - mme - INFO - roadside pred/gt: 7769/720
2025-03-06 15:07:50,645 - mme - INFO - Epoch [1][83/624]	lr: 1.724e-06, eta: 2:17:24, time: 0.403, data_time: 0.011, memory: 6893, laneline_hm_loss: 2.8547, roadside_hm_loss: 3.8817, laneline_st_off_loss: 8.9954, roadside_st_off_loss: 10.0315, mask_loss: 0.5803, lane_type_mask_loss: 0.8435, lane_color_mask_loss: 0.3523, side_type_mask_loss: 0.5490, laneline_len_loss: 4.1351, laneline_field_loss: 2.6415, laneline_view_loss: 8.7207, laneline_inv_len_loss: 3.8909, laneline_inv_field_loss: 6.3833, roadside_len_loss: 4.7515, roadside_field_loss: 1.6973, roadside_view_loss: 4.8659, roadside_inv_len_loss: 2.6724, roadside_inv_field_loss: 2.9122, depth_loss: 0.1637, loss: 70.9231, grad_norm: 340.0395
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:50,754 - mme - INFO - laneline pred/gt: 6849/498
2025-03-06 15:07:50,755 - mme - INFO - roadside pred/gt: 7761/498
2025-03-06 15:07:51,044 - mme - INFO - Epoch [1][84/624]	lr: 1.743e-06, eta: 2:17:14, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.8840, roadside_hm_loss: 5.5826, laneline_st_off_loss: 8.3827, roadside_st_off_loss: 8.5132, mask_loss: 0.7609, lane_type_mask_loss: 0.8507, lane_color_mask_loss: 0.3747, side_type_mask_loss: 0.5354, laneline_len_loss: 6.1597, laneline_field_loss: 2.0706, laneline_view_loss: 6.4586, laneline_inv_len_loss: 6.0892, laneline_inv_field_loss: 4.7950, roadside_len_loss: 7.0687, roadside_field_loss: 1.4946, roadside_view_loss: 4.1794, roadside_inv_len_loss: 4.1811, roadside_inv_field_loss: 2.6087, depth_loss: 0.0900, loss: 74.0797, grad_norm: 413.5210
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:51,154 - mme - INFO - laneline pred/gt: 6627/839
2025-03-06 15:07:51,155 - mme - INFO - roadside pred/gt: 7749/839
2025-03-06 15:07:51,442 - mme - INFO - Epoch [1][85/624]	lr: 1.763e-06, eta: 2:17:04, time: 0.398, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.6364, roadside_hm_loss: 3.3630, laneline_st_off_loss: 11.5236, roadside_st_off_loss: 11.3063, mask_loss: 0.5861, lane_type_mask_loss: 0.8387, lane_color_mask_loss: 0.3558, side_type_mask_loss: 0.5190, laneline_len_loss: 3.4568, laneline_field_loss: 2.8239, laneline_view_loss: 9.2169, laneline_inv_len_loss: 3.3535, laneline_inv_field_loss: 6.5351, roadside_len_loss: 4.0506, roadside_field_loss: 1.8222, roadside_view_loss: 4.9232, roadside_inv_len_loss: 2.2846, roadside_inv_field_loss: 2.7797, depth_loss: 0.2632, loss: 72.6384, grad_norm: 321.0110
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:51,551 - mme - INFO - laneline pred/gt: 6676/666
2025-03-06 15:07:51,552 - mme - INFO - roadside pred/gt: 7757/666
2025-03-06 15:07:51,834 - mme - INFO - Epoch [1][86/624]	lr: 1.783e-06, eta: 2:16:53, time: 0.392, data_time: 0.006, memory: 6893, laneline_hm_loss: 3.0166, roadside_hm_loss: 4.1244, laneline_st_off_loss: 8.5547, roadside_st_off_loss: 8.3190, mask_loss: 0.6116, lane_type_mask_loss: 0.8334, lane_color_mask_loss: 0.3380, side_type_mask_loss: 0.5327, laneline_len_loss: 3.7089, laneline_field_loss: 2.3701, laneline_view_loss: 7.7295, laneline_inv_len_loss: 3.8237, laneline_inv_field_loss: 5.8405, roadside_len_loss: 4.3344, roadside_field_loss: 1.5870, roadside_view_loss: 4.5902, roadside_inv_len_loss: 2.7229, roadside_inv_field_loss: 2.8232, depth_loss: 0.1948, loss: 66.0555, grad_norm: 319.8026
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:51,951 - mme - INFO - laneline pred/gt: 6730/748
2025-03-06 15:07:51,952 - mme - INFO - roadside pred/gt: 7729/748
2025-03-06 15:07:52,242 - mme - INFO - Epoch [1][87/624]	lr: 1.803e-06, eta: 2:16:46, time: 0.408, data_time: 0.013, memory: 6893, laneline_hm_loss: 2.7185, roadside_hm_loss: 3.6173, laneline_st_off_loss: 10.6662, roadside_st_off_loss: 10.2198, mask_loss: 0.6078, lane_type_mask_loss: 0.8259, lane_color_mask_loss: 0.3561, side_type_mask_loss: 0.5427, laneline_len_loss: 3.3310, laneline_field_loss: 2.5704, laneline_view_loss: 8.2799, laneline_inv_len_loss: 3.5227, laneline_inv_field_loss: 6.1612, roadside_len_loss: 3.7978, roadside_field_loss: 1.6633, roadside_view_loss: 4.5972, roadside_inv_len_loss: 2.4141, roadside_inv_field_loss: 2.7725, depth_loss: 0.1175, loss: 68.7822, grad_norm: 323.8261
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:52,351 - mme - INFO - laneline pred/gt: 6623/786
2025-03-06 15:07:52,352 - mme - INFO - roadside pred/gt: 7757/786
2025-03-06 15:07:52,641 - mme - INFO - Epoch [1][88/624]	lr: 1.823e-06, eta: 2:16:36, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.6263, roadside_hm_loss: 3.4682, laneline_st_off_loss: 10.6494, roadside_st_off_loss: 11.2384, mask_loss: 0.5876, lane_type_mask_loss: 0.7915, lane_color_mask_loss: 0.3341, side_type_mask_loss: 0.5220, laneline_len_loss: 3.0269, laneline_field_loss: 2.9993, laneline_view_loss: 10.2066, laneline_inv_len_loss: 3.1358, laneline_inv_field_loss: 7.3137, roadside_len_loss: 3.5912, roadside_field_loss: 1.9278, roadside_view_loss: 5.4391, roadside_inv_len_loss: 2.0609, roadside_inv_field_loss: 3.0836, depth_loss: 0.2001, loss: 73.2026, grad_norm: 342.4000
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:52,752 - mme - INFO - laneline pred/gt: 6559/798
2025-03-06 15:07:52,752 - mme - INFO - roadside pred/gt: 7738/798
2025-03-06 15:07:53,041 - mme - INFO - Epoch [1][89/624]	lr: 1.842e-06, eta: 2:16:28, time: 0.402, data_time: 0.008, memory: 6893, laneline_hm_loss: 2.6257, roadside_hm_loss: 3.3711, laneline_st_off_loss: 11.1658, roadside_st_off_loss: 9.9500, mask_loss: 0.5896, lane_type_mask_loss: 0.7795, lane_color_mask_loss: 0.3348, side_type_mask_loss: 0.5188, laneline_len_loss: 2.5931, laneline_field_loss: 2.6920, laneline_view_loss: 9.2233, laneline_inv_len_loss: 2.7746, laneline_inv_field_loss: 6.7995, roadside_len_loss: 3.1174, roadside_field_loss: 1.7671, roadside_view_loss: 5.2163, roadside_inv_len_loss: 1.7632, roadside_inv_field_loss: 3.0620, depth_loss: 0.2115, loss: 68.5552, grad_norm: 315.0916
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:53,151 - mme - INFO - laneline pred/gt: 6522/687
2025-03-06 15:07:53,152 - mme - INFO - roadside pred/gt: 7737/687
2025-03-06 15:07:53,432 - mme - INFO - Epoch [1][90/624]	lr: 1.862e-06, eta: 2:16:17, time: 0.391, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.9116, roadside_hm_loss: 3.7801, laneline_st_off_loss: 10.2740, roadside_st_off_loss: 10.2077, mask_loss: 0.6203, lane_type_mask_loss: 0.7679, lane_color_mask_loss: 0.3168, side_type_mask_loss: 0.4904, laneline_len_loss: 2.6259, laneline_field_loss: 2.4765, laneline_view_loss: 8.5950, laneline_inv_len_loss: 3.0229, laneline_inv_field_loss: 6.3919, roadside_len_loss: 3.2248, roadside_field_loss: 1.6682, roadside_view_loss: 5.1215, roadside_inv_len_loss: 1.9929, roadside_inv_field_loss: 3.0287, depth_loss: 0.1599, loss: 67.6771, grad_norm: 359.7356
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:53,551 - mme - INFO - laneline pred/gt: 6442/649
2025-03-06 15:07:53,551 - mme - INFO - roadside pred/gt: 7714/649
2025-03-06 15:07:53,840 - mme - INFO - Epoch [1][91/624]	lr: 1.882e-06, eta: 2:16:10, time: 0.408, data_time: 0.014, memory: 6893, laneline_hm_loss: 3.0248, roadside_hm_loss: 3.9814, laneline_st_off_loss: 7.3144, roadside_st_off_loss: 7.9081, mask_loss: 0.6136, lane_type_mask_loss: 0.7359, lane_color_mask_loss: 0.3177, side_type_mask_loss: 0.4921, laneline_len_loss: 3.1128, laneline_field_loss: 2.3311, laneline_view_loss: 7.2823, laneline_inv_len_loss: 3.2012, laneline_inv_field_loss: 5.3190, roadside_len_loss: 3.7758, roadside_field_loss: 1.5843, roadside_view_loss: 4.2556, roadside_inv_len_loss: 1.9523, roadside_inv_field_loss: 2.5336, depth_loss: 0.1819, loss: 59.9180, grad_norm: 353.9634
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:53,949 - mme - INFO - laneline pred/gt: 6351/769
2025-03-06 15:07:53,950 - mme - INFO - roadside pred/gt: 7731/769
2025-03-06 15:07:54,241 - mme - INFO - Epoch [1][92/624]	lr: 1.902e-06, eta: 2:16:02, time: 0.401, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.5280, roadside_hm_loss: 3.3297, laneline_st_off_loss: 8.4286, roadside_st_off_loss: 8.4087, mask_loss: 0.5511, lane_type_mask_loss: 0.7724, lane_color_mask_loss: 0.3245, side_type_mask_loss: 0.4849, laneline_len_loss: 1.9382, laneline_field_loss: 2.5562, laneline_view_loss: 8.6931, laneline_inv_len_loss: 2.2898, laneline_inv_field_loss: 6.6178, roadside_len_loss: 2.3980, roadside_field_loss: 1.6185, roadside_view_loss: 4.9126, roadside_inv_len_loss: 1.3816, roadside_inv_field_loss: 2.9859, depth_loss: 0.1961, loss: 60.4159, grad_norm: 283.3711
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:54,351 - mme - INFO - laneline pred/gt: 6277/726
2025-03-06 15:07:54,352 - mme - INFO - roadside pred/gt: 7729/726
2025-03-06 15:07:54,643 - mme - INFO - Epoch [1][93/624]	lr: 1.921e-06, eta: 2:15:55, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.6381, roadside_hm_loss: 3.4615, laneline_st_off_loss: 8.8820, roadside_st_off_loss: 9.2866, mask_loss: 0.5710, lane_type_mask_loss: 0.7495, lane_color_mask_loss: 0.3250, side_type_mask_loss: 0.4777, laneline_len_loss: 1.9213, laneline_field_loss: 2.4353, laneline_view_loss: 8.1867, laneline_inv_len_loss: 2.2983, laneline_inv_field_loss: 6.1901, roadside_len_loss: 2.3874, roadside_field_loss: 1.5880, roadside_view_loss: 4.7688, roadside_inv_len_loss: 1.3733, roadside_inv_field_loss: 2.9120, depth_loss: 0.1833, loss: 60.6359, grad_norm: 292.0326
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:54,752 - mme - INFO - laneline pred/gt: 6142/686
2025-03-06 15:07:54,753 - mme - INFO - roadside pred/gt: 7689/686
2025-03-06 15:07:55,043 - mme - INFO - Epoch [1][94/624]	lr: 1.941e-06, eta: 2:15:47, time: 0.400, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.7817, roadside_hm_loss: 3.6528, laneline_st_off_loss: 9.4373, roadside_st_off_loss: 8.8583, mask_loss: 0.6070, lane_type_mask_loss: 0.6916, lane_color_mask_loss: 0.3020, side_type_mask_loss: 0.4669, laneline_len_loss: 2.1235, laneline_field_loss: 2.3283, laneline_view_loss: 7.2945, laneline_inv_len_loss: 2.2605, laneline_inv_field_loss: 5.3758, roadside_len_loss: 2.6247, roadside_field_loss: 1.5894, roadside_view_loss: 4.3199, roadside_inv_len_loss: 1.2661, roadside_inv_field_loss: 2.5705, depth_loss: 0.1468, loss: 58.6978, grad_norm: 350.7265
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:55,154 - mme - INFO - laneline pred/gt: 6090/592
2025-03-06 15:07:55,154 - mme - INFO - roadside pred/gt: 7702/592
2025-03-06 15:07:55,446 - mme - INFO - Epoch [1][95/624]	lr: 1.961e-06, eta: 2:15:40, time: 0.403, data_time: 0.007, memory: 6893, laneline_hm_loss: 3.1229, roadside_hm_loss: 4.1031, laneline_st_off_loss: 9.7823, roadside_st_off_loss: 9.3010, mask_loss: 0.6776, lane_type_mask_loss: 0.6872, lane_color_mask_loss: 0.2927, side_type_mask_loss: 0.4526, laneline_len_loss: 1.7740, laneline_field_loss: 2.3581, laneline_view_loss: 7.5856, laneline_inv_len_loss: 2.1325, laneline_inv_field_loss: 5.5992, roadside_len_loss: 2.3233, roadside_field_loss: 1.6030, roadside_view_loss: 4.5590, roadside_inv_len_loss: 1.2266, roadside_inv_field_loss: 2.7587, depth_loss: 0.1301, loss: 60.4694, grad_norm: 325.4767
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:55,555 - mme - INFO - laneline pred/gt: 6010/915
2025-03-06 15:07:55,556 - mme - INFO - roadside pred/gt: 7676/915
2025-03-06 15:07:55,840 - mme - INFO - Epoch [1][96/624]	lr: 1.981e-06, eta: 2:15:31, time: 0.395, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.3334, roadside_hm_loss: 2.7490, laneline_st_off_loss: 11.2211, roadside_st_off_loss: 10.4055, mask_loss: 0.6086, lane_type_mask_loss: 0.6647, lane_color_mask_loss: 0.2782, side_type_mask_loss: 0.4397, laneline_len_loss: 1.2086, laneline_field_loss: 2.7359, laneline_view_loss: 9.7012, laneline_inv_len_loss: 1.3946, laneline_inv_field_loss: 7.3225, roadside_len_loss: 1.5169, roadside_field_loss: 1.6914, roadside_view_loss: 5.2365, roadside_inv_len_loss: 0.8391, roadside_inv_field_loss: 3.1444, depth_loss: 0.1611, loss: 63.6523, grad_norm: 278.9698
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:55,958 - mme - INFO - laneline pred/gt: 5767/706
2025-03-06 15:07:55,958 - mme - INFO - roadside pred/gt: 7698/706
2025-03-06 15:07:56,250 - mme - INFO - Epoch [1][97/624]	lr: 2.001e-06, eta: 2:15:25, time: 0.409, data_time: 0.014, memory: 6893, laneline_hm_loss: 2.6540, roadside_hm_loss: 3.4224, laneline_st_off_loss: 8.3265, roadside_st_off_loss: 8.5900, mask_loss: 0.5877, lane_type_mask_loss: 0.6351, lane_color_mask_loss: 0.2820, side_type_mask_loss: 0.4488, laneline_len_loss: 1.3095, laneline_field_loss: 2.6882, laneline_view_loss: 9.2996, laneline_inv_len_loss: 1.7191, laneline_inv_field_loss: 6.9749, roadside_len_loss: 1.6536, roadside_field_loss: 1.6989, roadside_view_loss: 5.2240, roadside_inv_len_loss: 0.9183, roadside_inv_field_loss: 3.1417, depth_loss: 0.1586, loss: 59.7331, grad_norm: 306.5873
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:56,358 - mme - INFO - laneline pred/gt: 5734/906
2025-03-06 15:07:56,358 - mme - INFO - roadside pred/gt: 7631/906
2025-03-06 15:07:56,653 - mme - INFO - Epoch [1][98/624]	lr: 2.020e-06, eta: 2:15:18, time: 0.403, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.3075, roadside_hm_loss: 2.6941, laneline_st_off_loss: 10.1189, roadside_st_off_loss: 9.8478, mask_loss: 0.5501, lane_type_mask_loss: 0.6354, lane_color_mask_loss: 0.2802, side_type_mask_loss: 0.4228, laneline_len_loss: 0.7813, laneline_field_loss: 2.6980, laneline_view_loss: 8.9248, laneline_inv_len_loss: 1.0666, laneline_inv_field_loss: 6.4818, roadside_len_loss: 1.0554, roadside_field_loss: 1.7866, roadside_view_loss: 4.8097, roadside_inv_len_loss: 0.5731, roadside_inv_field_loss: 2.7468, depth_loss: 0.2622, loss: 58.0430, grad_norm: 277.0060
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:56,762 - mme - INFO - laneline pred/gt: 5634/762
2025-03-06 15:07:56,763 - mme - INFO - roadside pred/gt: 7663/762
2025-03-06 15:07:57,051 - mme - INFO - Epoch [1][99/624]	lr: 2.040e-06, eta: 2:15:11, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.5240, roadside_hm_loss: 3.0547, laneline_st_off_loss: 10.2165, roadside_st_off_loss: 10.3923, mask_loss: 0.6050, lane_type_mask_loss: 0.6258, lane_color_mask_loss: 0.2649, side_type_mask_loss: 0.4318, laneline_len_loss: 0.9728, laneline_field_loss: 2.6832, laneline_view_loss: 9.5050, laneline_inv_len_loss: 1.1869, laneline_inv_field_loss: 7.0558, roadside_len_loss: 1.2847, roadside_field_loss: 1.8007, roadside_view_loss: 5.5688, roadside_inv_len_loss: 0.6350, roadside_inv_field_loss: 3.2629, depth_loss: 0.2091, loss: 62.2799, grad_norm: 265.7728
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:57,160 - mme - INFO - laneline pred/gt: 5538/720
2025-03-06 15:07:57,161 - mme - INFO - roadside pred/gt: 7557/720
2025-03-06 15:07:57,444 - mme - INFO - Epoch [1][100/624]	lr: 2.060e-06, eta: 2:15:02, time: 0.393, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.5441, roadside_hm_loss: 3.2025, laneline_st_off_loss: 10.5207, roadside_st_off_loss: 11.8251, mask_loss: 0.6206, lane_type_mask_loss: 0.6269, lane_color_mask_loss: 0.2708, side_type_mask_loss: 0.4057, laneline_len_loss: 0.9463, laneline_field_loss: 2.8002, laneline_view_loss: 9.7490, laneline_inv_len_loss: 1.2402, laneline_inv_field_loss: 6.9616, roadside_len_loss: 1.1736, roadside_field_loss: 1.9186, roadside_view_loss: 5.6387, roadside_inv_len_loss: 0.7005, roadside_inv_field_loss: 3.1759, depth_loss: 0.1407, loss: 64.4616, grad_norm: 360.7679
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:57,561 - mme - INFO - laneline pred/gt: 5535/731
2025-03-06 15:07:57,561 - mme - INFO - roadside pred/gt: 7518/730
2025-03-06 15:07:57,844 - mme - INFO - Epoch [1][101/624]	lr: 2.080e-06, eta: 2:14:56, time: 0.401, data_time: 0.014, memory: 6893, laneline_hm_loss: 2.5456, roadside_hm_loss: 3.1191, laneline_st_off_loss: 10.0798, roadside_st_off_loss: 10.8807, mask_loss: 0.6024, lane_type_mask_loss: 0.5940, lane_color_mask_loss: 0.2569, side_type_mask_loss: 0.3947, laneline_len_loss: 0.9817, laneline_field_loss: 3.6396, laneline_view_loss: 10.7514, laneline_inv_len_loss: 1.1937, laneline_inv_field_loss: 6.9954, roadside_len_loss: 1.2319, roadside_field_loss: 2.3468, roadside_view_loss: 5.5278, roadside_inv_len_loss: 0.5932, roadside_inv_field_loss: 2.8487, depth_loss: 0.1781, loss: 64.7615, grad_norm: 301.1594
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:57,959 - mme - INFO - laneline pred/gt: 5526/842
2025-03-06 15:07:57,960 - mme - INFO - roadside pred/gt: 7459/842
2025-03-06 15:07:58,252 - mme - INFO - Epoch [1][102/624]	lr: 2.100e-06, eta: 2:14:50, time: 0.408, data_time: 0.012, memory: 6893, laneline_hm_loss: 2.5416, roadside_hm_loss: 2.8298, laneline_st_off_loss: 10.2008, roadside_st_off_loss: 9.0570, mask_loss: 0.6145, lane_type_mask_loss: 0.5429, lane_color_mask_loss: 0.2367, side_type_mask_loss: 0.3721, laneline_len_loss: 0.7748, laneline_field_loss: 2.7278, laneline_view_loss: 8.6736, laneline_inv_len_loss: 0.8531, laneline_inv_field_loss: 6.2429, roadside_len_loss: 0.9921, roadside_field_loss: 1.7779, roadside_view_loss: 4.8224, roadside_inv_len_loss: 0.4620, roadside_inv_field_loss: 2.7150, depth_loss: 0.1971, loss: 56.6342, grad_norm: 288.0996
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:58,362 - mme - INFO - laneline pred/gt: 5334/564
2025-03-06 15:07:58,363 - mme - INFO - roadside pred/gt: 7569/564
2025-03-06 15:07:58,652 - mme - INFO - Epoch [1][103/624]	lr: 2.119e-06, eta: 2:14:44, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.8702, roadside_hm_loss: 3.8066, laneline_st_off_loss: 6.9341, roadside_st_off_loss: 6.0531, mask_loss: 0.5787, lane_type_mask_loss: 0.6014, lane_color_mask_loss: 0.2762, side_type_mask_loss: 0.4088, laneline_len_loss: 0.7836, laneline_field_loss: 2.2759, laneline_view_loss: 7.4957, laneline_inv_len_loss: 1.0020, laneline_inv_field_loss: 5.5779, roadside_len_loss: 0.9367, roadside_field_loss: 1.5711, roadside_view_loss: 4.6494, roadside_inv_len_loss: 0.5312, roadside_inv_field_loss: 2.8624, depth_loss: 0.0932, loss: 49.3079, grad_norm: 262.2693
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:58,760 - mme - INFO - laneline pred/gt: 5268/827
2025-03-06 15:07:58,761 - mme - INFO - roadside pred/gt: 7408/827
2025-03-06 15:07:59,051 - mme - INFO - Epoch [1][104/624]	lr: 2.139e-06, eta: 2:14:37, time: 0.399, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.4198, roadside_hm_loss: 2.7630, laneline_st_off_loss: 10.3810, roadside_st_off_loss: 8.4476, mask_loss: 0.5822, lane_type_mask_loss: 0.5445, lane_color_mask_loss: 0.2345, side_type_mask_loss: 0.3669, laneline_len_loss: 0.5454, laneline_field_loss: 2.6374, laneline_view_loss: 9.2186, laneline_inv_len_loss: 0.6881, laneline_inv_field_loss: 6.8084, roadside_len_loss: 0.6610, roadside_field_loss: 1.7747, roadside_view_loss: 5.3475, roadside_inv_len_loss: 0.4014, roadside_inv_field_loss: 3.0758, depth_loss: 0.2220, loss: 57.1199, grad_norm: 300.7256
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:59,161 - mme - INFO - laneline pred/gt: 5247/913
2025-03-06 15:07:59,161 - mme - INFO - roadside pred/gt: 7369/913
2025-03-06 15:07:59,451 - mme - INFO - Epoch [1][105/624]	lr: 2.159e-06, eta: 2:14:30, time: 0.400, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.2495, roadside_hm_loss: 2.4694, laneline_st_off_loss: 7.9179, roadside_st_off_loss: 7.6390, mask_loss: 0.5034, lane_type_mask_loss: 0.5269, lane_color_mask_loss: 0.2320, side_type_mask_loss: 0.3570, laneline_len_loss: 0.4745, laneline_field_loss: 2.9390, laneline_view_loss: 10.7003, laneline_inv_len_loss: 0.5730, laneline_inv_field_loss: 7.7942, roadside_len_loss: 0.5954, roadside_field_loss: 1.8443, roadside_view_loss: 5.8398, roadside_inv_len_loss: 0.3380, roadside_inv_field_loss: 3.3276, depth_loss: 0.1912, loss: 56.5124, grad_norm: 292.1512
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:59,560 - mme - INFO - laneline pred/gt: 5118/618
2025-03-06 15:07:59,561 - mme - INFO - roadside pred/gt: 7329/618
2025-03-06 15:07:59,845 - mme - INFO - Epoch [1][106/624]	lr: 2.179e-06, eta: 2:14:23, time: 0.394, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.7829, roadside_hm_loss: 3.4226, laneline_st_off_loss: 8.8814, roadside_st_off_loss: 7.3408, mask_loss: 0.6480, lane_type_mask_loss: 0.5151, lane_color_mask_loss: 0.2460, side_type_mask_loss: 0.3486, laneline_len_loss: 0.7096, laneline_field_loss: 2.2874, laneline_view_loss: 7.4310, laneline_inv_len_loss: 0.7477, laneline_inv_field_loss: 5.4733, roadside_len_loss: 0.8656, roadside_field_loss: 1.6171, roadside_view_loss: 4.5571, roadside_inv_len_loss: 0.3946, roadside_inv_field_loss: 2.7585, depth_loss: 0.1356, loss: 51.1631, grad_norm: 277.7823
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:07:59,968 - mme - INFO - laneline pred/gt: 5027/919
2025-03-06 15:07:59,969 - mme - INFO - roadside pred/gt: 7242/919
2025-03-06 15:08:00,257 - mme - INFO - Epoch [1][107/624]	lr: 2.199e-06, eta: 2:14:19, time: 0.411, data_time: 0.016, memory: 6893, laneline_hm_loss: 2.4190, roadside_hm_loss: 2.5411, laneline_st_off_loss: 11.1944, roadside_st_off_loss: 10.9967, mask_loss: 0.5559, lane_type_mask_loss: 0.4634, lane_color_mask_loss: 0.2138, side_type_mask_loss: 0.3272, laneline_len_loss: 0.4548, laneline_field_loss: 3.5048, laneline_view_loss: 11.1728, laneline_inv_len_loss: 0.5409, laneline_inv_field_loss: 7.4853, roadside_len_loss: 0.5520, roadside_field_loss: 2.0338, roadside_view_loss: 5.4319, roadside_inv_len_loss: 0.3093, roadside_inv_field_loss: 2.8276, depth_loss: 0.1970, loss: 63.2214, grad_norm: 254.0843
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:00,384 - mme - INFO - laneline pred/gt: 4770/630
2025-03-06 15:08:00,384 - mme - INFO - roadside pred/gt: 7265/630
2025-03-06 15:08:00,670 - mme - INFO - Epoch [1][108/624]	lr: 2.218e-06, eta: 2:14:15, time: 0.413, data_time: 0.022, memory: 6893, laneline_hm_loss: 2.6116, roadside_hm_loss: 3.1608, laneline_st_off_loss: 7.7798, roadside_st_off_loss: 7.4199, mask_loss: 0.5710, lane_type_mask_loss: 0.4924, lane_color_mask_loss: 0.2425, side_type_mask_loss: 0.3442, laneline_len_loss: 0.3764, laneline_field_loss: 2.3223, laneline_view_loss: 7.6840, laneline_inv_len_loss: 0.6037, laneline_inv_field_loss: 5.7851, roadside_len_loss: 0.4448, roadside_field_loss: 1.5598, roadside_view_loss: 4.5210, roadside_inv_len_loss: 0.3537, roadside_inv_field_loss: 2.7915, depth_loss: 0.1623, loss: 49.2266, grad_norm: 278.1552
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:00,788 - mme - INFO - laneline pred/gt: 4714/692
2025-03-06 15:08:00,788 - mme - INFO - roadside pred/gt: 7064/692
2025-03-06 15:08:01,086 - mme - INFO - Epoch [1][109/624]	lr: 2.238e-06, eta: 2:14:12, time: 0.416, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.6122, roadside_hm_loss: 2.9537, laneline_st_off_loss: 7.9471, roadside_st_off_loss: 7.5766, mask_loss: 0.6098, lane_type_mask_loss: 0.4451, lane_color_mask_loss: 0.2149, side_type_mask_loss: 0.3152, laneline_len_loss: 0.3586, laneline_field_loss: 2.4007, laneline_view_loss: 7.4413, laneline_inv_len_loss: 0.4530, laneline_inv_field_loss: 5.5482, roadside_len_loss: 0.4525, roadside_field_loss: 1.5890, roadside_view_loss: 4.2715, roadside_inv_len_loss: 0.2666, roadside_inv_field_loss: 2.6179, depth_loss: 0.1350, loss: 48.2089, grad_norm: 260.8101
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:01,197 - mme - INFO - laneline pred/gt: 4714/708
2025-03-06 15:08:01,198 - mme - INFO - roadside pred/gt: 7082/708
2025-03-06 15:08:01,493 - mme - INFO - Epoch [1][110/624]	lr: 2.258e-06, eta: 2:14:07, time: 0.407, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.5974, roadside_hm_loss: 2.9068, laneline_st_off_loss: 9.1702, roadside_st_off_loss: 7.9725, mask_loss: 0.5532, lane_type_mask_loss: 0.4364, lane_color_mask_loss: 0.2047, side_type_mask_loss: 0.3043, laneline_len_loss: 0.3273, laneline_field_loss: 2.6235, laneline_view_loss: 9.0948, laneline_inv_len_loss: 0.4379, laneline_inv_field_loss: 6.7103, roadside_len_loss: 0.3780, roadside_field_loss: 1.7773, roadside_view_loss: 5.3364, roadside_inv_len_loss: 0.2477, roadside_inv_field_loss: 3.1606, depth_loss: 0.1271, loss: 54.3665, grad_norm: 263.6099
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:01,606 - mme - INFO - laneline pred/gt: 4752/653
2025-03-06 15:08:01,606 - mme - INFO - roadside pred/gt: 6928/653
2025-03-06 15:08:01,897 - mme - INFO - Epoch [1][111/624]	lr: 2.278e-06, eta: 2:14:01, time: 0.403, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.6137, roadside_hm_loss: 3.0159, laneline_st_off_loss: 8.1488, roadside_st_off_loss: 7.7202, mask_loss: 0.5847, lane_type_mask_loss: 0.4318, lane_color_mask_loss: 0.2162, side_type_mask_loss: 0.3147, laneline_len_loss: 0.4021, laneline_field_loss: 2.4330, laneline_view_loss: 7.8873, laneline_inv_len_loss: 0.4461, laneline_inv_field_loss: 5.8929, roadside_len_loss: 0.4646, roadside_field_loss: 1.6193, roadside_view_loss: 4.5525, roadside_inv_len_loss: 0.2622, roadside_inv_field_loss: 2.8296, depth_loss: 0.0830, loss: 49.9185, grad_norm: 299.3289
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:02,021 - mme - INFO - laneline pred/gt: 4640/663
2025-03-06 15:08:02,022 - mme - INFO - roadside pred/gt: 6741/663
2025-03-06 15:08:02,324 - mme - INFO - Epoch [1][112/624]	lr: 2.298e-06, eta: 2:14:00, time: 0.428, data_time: 0.009, memory: 6893, laneline_hm_loss: 2.5841, roadside_hm_loss: 2.9127, laneline_st_off_loss: 8.4536, roadside_st_off_loss: 7.6389, mask_loss: 0.5719, lane_type_mask_loss: 0.4082, lane_color_mask_loss: 0.2038, side_type_mask_loss: 0.2993, laneline_len_loss: 0.3178, laneline_field_loss: 2.5876, laneline_view_loss: 9.1752, laneline_inv_len_loss: 0.4090, laneline_inv_field_loss: 6.8032, roadside_len_loss: 0.3685, roadside_field_loss: 1.7167, roadside_view_loss: 5.3699, roadside_inv_len_loss: 0.2556, roadside_inv_field_loss: 3.2190, depth_loss: 0.1589, loss: 53.4539, grad_norm: 252.6665
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:02,443 - mme - INFO - laneline pred/gt: 4568/730
2025-03-06 15:08:02,444 - mme - INFO - roadside pred/gt: 6718/730
2025-03-06 15:08:02,734 - mme - INFO - Epoch [1][113/624]	lr: 2.317e-06, eta: 2:13:56, time: 0.410, data_time: 0.009, memory: 6893, laneline_hm_loss: 2.3931, roadside_hm_loss: 2.6618, laneline_st_off_loss: 6.6172, roadside_st_off_loss: 6.0704, mask_loss: 0.5621, lane_type_mask_loss: 0.4037, lane_color_mask_loss: 0.2190, side_type_mask_loss: 0.2982, laneline_len_loss: 0.2699, laneline_field_loss: 2.3848, laneline_view_loss: 7.0207, laneline_inv_len_loss: 0.3514, laneline_inv_field_loss: 5.2567, roadside_len_loss: 0.3190, roadside_field_loss: 1.5297, roadside_view_loss: 3.8241, roadside_inv_len_loss: 0.2250, roadside_inv_field_loss: 2.3399, depth_loss: 0.1224, loss: 42.8690, grad_norm: 190.0548
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:02,852 - mme - INFO - laneline pred/gt: 4676/789
2025-03-06 15:08:02,853 - mme - INFO - roadside pred/gt: 6718/789
2025-03-06 15:08:03,158 - mme - INFO - Epoch [1][114/624]	lr: 2.337e-06, eta: 2:13:55, time: 0.423, data_time: 0.009, memory: 6893, laneline_hm_loss: 2.3701, roadside_hm_loss: 2.5768, laneline_st_off_loss: 8.0085, roadside_st_off_loss: 7.3218, mask_loss: 0.5546, lane_type_mask_loss: 0.3914, lane_color_mask_loss: 0.2036, side_type_mask_loss: 0.2867, laneline_len_loss: 0.2509, laneline_field_loss: 2.6294, laneline_view_loss: 9.3826, laneline_inv_len_loss: 0.3477, laneline_inv_field_loss: 7.0652, roadside_len_loss: 0.2977, roadside_field_loss: 1.6611, roadside_view_loss: 5.1962, roadside_inv_len_loss: 0.2280, roadside_inv_field_loss: 3.1284, depth_loss: 0.2294, loss: 52.1302, grad_norm: 238.4111
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:03,273 - mme - INFO - laneline pred/gt: 4418/617
2025-03-06 15:08:03,274 - mme - INFO - roadside pred/gt: 6562/617
2025-03-06 15:08:03,566 - mme - INFO - Epoch [1][115/624]	lr: 2.357e-06, eta: 2:13:50, time: 0.408, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.5878, roadside_hm_loss: 2.9480, laneline_st_off_loss: 6.9284, roadside_st_off_loss: 6.6814, mask_loss: 0.5518, lane_type_mask_loss: 0.3583, lane_color_mask_loss: 0.1962, side_type_mask_loss: 0.2720, laneline_len_loss: 0.2430, laneline_field_loss: 2.6357, laneline_view_loss: 8.8297, laneline_inv_len_loss: 0.3704, laneline_inv_field_loss: 6.0918, roadside_len_loss: 0.3001, roadside_field_loss: 1.8019, roadside_view_loss: 5.1743, roadside_inv_len_loss: 0.2172, roadside_inv_field_loss: 2.8729, depth_loss: 0.1405, loss: 49.2013, grad_norm: 249.4809
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:03,678 - mme - INFO - laneline pred/gt: 4457/584
2025-03-06 15:08:03,679 - mme - INFO - roadside pred/gt: 6508/584
2025-03-06 15:08:03,969 - mme - INFO - Epoch [1][116/624]	lr: 2.377e-06, eta: 2:13:45, time: 0.403, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.6685, roadside_hm_loss: 3.0497, laneline_st_off_loss: 6.4609, roadside_st_off_loss: 6.0405, mask_loss: 0.5759, lane_type_mask_loss: 0.3598, lane_color_mask_loss: 0.1968, side_type_mask_loss: 0.2653, laneline_len_loss: 0.2685, laneline_field_loss: 2.3306, laneline_view_loss: 7.8814, laneline_inv_len_loss: 0.3775, laneline_inv_field_loss: 5.8384, roadside_len_loss: 0.3077, roadside_field_loss: 1.6161, roadside_view_loss: 4.9616, roadside_inv_len_loss: 0.2432, roadside_inv_field_loss: 3.0116, depth_loss: 0.1447, loss: 46.5986, grad_norm: 212.0481
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:04,086 - mme - INFO - laneline pred/gt: 4264/517
2025-03-06 15:08:04,087 - mme - INFO - roadside pred/gt: 6384/517
2025-03-06 15:08:04,372 - mme - INFO - Epoch [1][117/624]	lr: 2.397e-06, eta: 2:13:40, time: 0.403, data_time: 0.007, memory: 6893, laneline_hm_loss: 2.7853, roadside_hm_loss: 3.3054, laneline_st_off_loss: 6.1997, roadside_st_off_loss: 5.9119, mask_loss: 0.5874, lane_type_mask_loss: 0.3198, lane_color_mask_loss: 0.1974, side_type_mask_loss: 0.2603, laneline_len_loss: 0.2910, laneline_field_loss: 2.2620, laneline_view_loss: 7.3776, laneline_inv_len_loss: 0.3814, laneline_inv_field_loss: 5.4830, roadside_len_loss: 0.3208, roadside_field_loss: 1.5566, roadside_view_loss: 4.6056, roadside_inv_len_loss: 0.2113, roadside_inv_field_loss: 2.8583, depth_loss: 0.1196, loss: 45.0344, grad_norm: 250.4154
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:04,483 - mme - INFO - laneline pred/gt: 4218/620
2025-03-06 15:08:04,483 - mme - INFO - roadside pred/gt: 6186/620
2025-03-06 15:08:04,768 - mme - INFO - Epoch [1][118/624]	lr: 2.416e-06, eta: 2:13:34, time: 0.396, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.5563, roadside_hm_loss: 2.8504, laneline_st_off_loss: 6.7763, roadside_st_off_loss: 6.2206, mask_loss: 0.5645, lane_type_mask_loss: 0.3366, lane_color_mask_loss: 0.2010, side_type_mask_loss: 0.2616, laneline_len_loss: 0.2424, laneline_field_loss: 2.3597, laneline_view_loss: 7.6325, laneline_inv_len_loss: 0.3214, laneline_inv_field_loss: 5.5268, roadside_len_loss: 0.2731, roadside_field_loss: 1.6220, roadside_view_loss: 4.5677, roadside_inv_len_loss: 0.1991, roadside_inv_field_loss: 2.7108, depth_loss: 0.1323, loss: 45.3552, grad_norm: 220.5472
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:04,886 - mme - INFO - laneline pred/gt: 4085/863
2025-03-06 15:08:04,888 - mme - INFO - roadside pred/gt: 5969/863
2025-03-06 15:08:05,174 - mme - INFO - Epoch [1][119/624]	lr: 2.436e-06, eta: 2:13:30, time: 0.406, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.3388, roadside_hm_loss: 2.3745, laneline_st_off_loss: 6.8138, roadside_st_off_loss: 6.0964, mask_loss: 0.5023, lane_type_mask_loss: 0.2749, lane_color_mask_loss: 0.1724, side_type_mask_loss: 0.2230, laneline_len_loss: 0.1790, laneline_field_loss: 2.6687, laneline_view_loss: 9.1304, laneline_inv_len_loss: 0.2493, laneline_inv_field_loss: 6.6900, roadside_len_loss: 0.1989, roadside_field_loss: 1.7012, roadside_view_loss: 4.8274, roadside_inv_len_loss: 0.1622, roadside_inv_field_loss: 2.8070, depth_loss: 0.2308, loss: 47.6408, grad_norm: 193.2332
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:05,283 - mme - INFO - laneline pred/gt: 4139/616
2025-03-06 15:08:05,284 - mme - INFO - roadside pred/gt: 5975/623
2025-03-06 15:08:05,578 - mme - INFO - Epoch [1][120/624]	lr: 2.456e-06, eta: 2:13:25, time: 0.404, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.5561, roadside_hm_loss: 2.7292, laneline_st_off_loss: 7.0650, roadside_st_off_loss: 6.5279, mask_loss: 0.5564, lane_type_mask_loss: 0.3138, lane_color_mask_loss: 0.1910, side_type_mask_loss: 0.2412, laneline_len_loss: 0.2112, laneline_field_loss: 2.4212, laneline_view_loss: 8.3210, laneline_inv_len_loss: 0.2879, laneline_inv_field_loss: 6.2829, roadside_len_loss: 0.2338, roadside_field_loss: 1.7065, roadside_view_loss: 5.0672, roadside_inv_len_loss: 0.1806, roadside_inv_field_loss: 3.1231, depth_loss: 0.1479, loss: 48.1638, grad_norm: 222.5656
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:05,696 - mme - INFO - laneline pred/gt: 4129/811
2025-03-06 15:08:05,697 - mme - INFO - roadside pred/gt: 5621/811
2025-03-06 15:08:06,010 - mme - INFO - Epoch [1][121/624]	lr: 2.476e-06, eta: 2:13:25, time: 0.431, data_time: 0.009, memory: 6893, laneline_hm_loss: 2.4298, roadside_hm_loss: 2.4477, laneline_st_off_loss: 7.8545, roadside_st_off_loss: 6.7728, mask_loss: 0.5460, lane_type_mask_loss: 0.2525, lane_color_mask_loss: 0.1637, side_type_mask_loss: 0.2062, laneline_len_loss: 0.1807, laneline_field_loss: 2.5057, laneline_view_loss: 8.7679, laneline_inv_len_loss: 0.2574, laneline_inv_field_loss: 6.5237, roadside_len_loss: 0.1966, roadside_field_loss: 1.6700, roadside_view_loss: 5.0971, roadside_inv_len_loss: 0.1727, roadside_inv_field_loss: 2.9401, depth_loss: 0.2126, loss: 49.1977, grad_norm: 243.0895
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:06,152 - mme - INFO - laneline pred/gt: 3963/512
2025-03-06 15:08:06,153 - mme - INFO - roadside pred/gt: 5367/512
2025-03-06 15:08:06,468 - mme - INFO - Epoch [1][122/624]	lr: 2.496e-06, eta: 2:13:29, time: 0.459, data_time: 0.029, memory: 6893, laneline_hm_loss: 2.6509, roadside_hm_loss: 2.9920, laneline_st_off_loss: 4.8252, roadside_st_off_loss: 4.5804, mask_loss: 0.6068, lane_type_mask_loss: 0.2997, lane_color_mask_loss: 0.1906, side_type_mask_loss: 0.2298, laneline_len_loss: 0.2190, laneline_field_loss: 2.0896, laneline_view_loss: 6.0553, laneline_inv_len_loss: 0.2942, laneline_inv_field_loss: 4.4796, roadside_len_loss: 0.2368, roadside_field_loss: 1.4803, roadside_view_loss: 3.7926, roadside_inv_len_loss: 0.1893, roadside_inv_field_loss: 2.3751, depth_loss: 0.1399, loss: 37.7271, grad_norm: 158.3557
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:06,591 - mme - INFO - laneline pred/gt: 4181/781
2025-03-06 15:08:06,591 - mme - INFO - roadside pred/gt: 5420/781
2025-03-06 15:08:06,880 - mme - INFO - Epoch [1][123/624]	lr: 2.515e-06, eta: 2:13:26, time: 0.413, data_time: 0.011, memory: 6893, laneline_hm_loss: 2.3556, roadside_hm_loss: 2.4928, laneline_st_off_loss: 7.0021, roadside_st_off_loss: 6.2829, mask_loss: 0.5431, lane_type_mask_loss: 0.2519, lane_color_mask_loss: 0.1634, side_type_mask_loss: 0.2044, laneline_len_loss: 0.1653, laneline_field_loss: 2.6795, laneline_view_loss: 9.1920, laneline_inv_len_loss: 0.2445, laneline_inv_field_loss: 6.7100, roadside_len_loss: 0.1873, roadside_field_loss: 1.7173, roadside_view_loss: 5.0214, roadside_inv_len_loss: 0.1569, roadside_inv_field_loss: 2.9391, depth_loss: 0.1766, loss: 48.4861, grad_norm: 195.5798
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:06,996 - mme - INFO - laneline pred/gt: 3829/730
2025-03-06 15:08:06,996 - mme - INFO - roadside pred/gt: 4934/730
2025-03-06 15:08:07,279 - mme - INFO - Epoch [1][124/624]	lr: 2.535e-06, eta: 2:13:21, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.3298, roadside_hm_loss: 2.4000, laneline_st_off_loss: 7.2779, roadside_st_off_loss: 6.6333, mask_loss: 0.5183, lane_type_mask_loss: 0.2381, lane_color_mask_loss: 0.1540, side_type_mask_loss: 0.1964, laneline_len_loss: 0.1886, laneline_field_loss: 2.5429, laneline_view_loss: 8.8599, laneline_inv_len_loss: 0.2480, laneline_inv_field_loss: 6.6308, roadside_len_loss: 0.2004, roadside_field_loss: 1.5969, roadside_view_loss: 4.9316, roadside_inv_len_loss: 0.1684, roadside_inv_field_loss: 2.9927, depth_loss: 0.1575, loss: 48.2654, grad_norm: 221.8941
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:07,388 - mme - INFO - laneline pred/gt: 3843/819
2025-03-06 15:08:07,389 - mme - INFO - roadside pred/gt: 4966/819
2025-03-06 15:08:07,671 - mme - INFO - Epoch [1][125/624]	lr: 2.555e-06, eta: 2:13:15, time: 0.392, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.2383, roadside_hm_loss: 2.3132, laneline_st_off_loss: 7.0843, roadside_st_off_loss: 5.9022, mask_loss: 0.4983, lane_type_mask_loss: 0.2086, lane_color_mask_loss: 0.1436, side_type_mask_loss: 0.1847, laneline_len_loss: 0.1601, laneline_field_loss: 2.7468, laneline_view_loss: 10.0923, laneline_inv_len_loss: 0.2290, laneline_inv_field_loss: 7.3116, roadside_len_loss: 0.1732, roadside_field_loss: 1.6985, roadside_view_loss: 5.5093, roadside_inv_len_loss: 0.1525, roadside_inv_field_loss: 3.1378, depth_loss: 0.1671, loss: 49.9514, grad_norm: 198.7756
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:07,784 - mme - INFO - laneline pred/gt: 4136/740
2025-03-06 15:08:07,785 - mme - INFO - roadside pred/gt: 4974/740
2025-03-06 15:08:08,065 - mme - INFO - Epoch [1][126/624]	lr: 2.575e-06, eta: 2:13:09, time: 0.393, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.3764, roadside_hm_loss: 2.5165, laneline_st_off_loss: 6.8271, roadside_st_off_loss: 7.5281, mask_loss: 0.5593, lane_type_mask_loss: 0.2114, lane_color_mask_loss: 0.1482, side_type_mask_loss: 0.1855, laneline_len_loss: 0.1635, laneline_field_loss: 2.6523, laneline_view_loss: 9.4570, laneline_inv_len_loss: 0.2288, laneline_inv_field_loss: 6.9492, roadside_len_loss: 0.1813, roadside_field_loss: 1.7634, roadside_view_loss: 5.3627, roadside_inv_len_loss: 0.1485, roadside_inv_field_loss: 3.1166, depth_loss: 0.2044, loss: 50.5802, grad_norm: 215.6779
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:08,174 - mme - INFO - laneline pred/gt: 4024/682
2025-03-06 15:08:08,174 - mme - INFO - roadside pred/gt: 4924/682
2025-03-06 15:08:08,457 - mme - INFO - Epoch [1][127/624]	lr: 2.595e-06, eta: 2:13:03, time: 0.393, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.4374, roadside_hm_loss: 2.4987, laneline_st_off_loss: 5.8799, roadside_st_off_loss: 4.8205, mask_loss: 0.5262, lane_type_mask_loss: 0.2310, lane_color_mask_loss: 0.1618, side_type_mask_loss: 0.1888, laneline_len_loss: 0.1854, laneline_field_loss: 2.4202, laneline_view_loss: 7.9397, laneline_inv_len_loss: 0.2399, laneline_inv_field_loss: 5.7975, roadside_len_loss: 0.1930, roadside_field_loss: 1.5969, roadside_view_loss: 4.5943, roadside_inv_len_loss: 0.1601, roadside_inv_field_loss: 2.7274, depth_loss: 0.1735, loss: 42.7721, grad_norm: 181.5637
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:08,570 - mme - INFO - laneline pred/gt: 3793/658
2025-03-06 15:08:08,570 - mme - INFO - roadside pred/gt: 4466/658
2025-03-06 15:08:08,852 - mme - INFO - Epoch [1][128/624]	lr: 2.614e-06, eta: 2:12:57, time: 0.394, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.2656, roadside_hm_loss: 2.4411, laneline_st_off_loss: 5.2009, roadside_st_off_loss: 4.7039, mask_loss: 0.5115, lane_type_mask_loss: 0.2077, lane_color_mask_loss: 0.1506, side_type_mask_loss: 0.1801, laneline_len_loss: 0.1630, laneline_field_loss: 2.3584, laneline_view_loss: 7.7674, laneline_inv_len_loss: 0.2389, laneline_inv_field_loss: 5.8593, roadside_len_loss: 0.1775, roadside_field_loss: 1.5293, roadside_view_loss: 4.4980, roadside_inv_len_loss: 0.1605, roadside_inv_field_loss: 2.7961, depth_loss: 0.1253, loss: 41.3350, grad_norm: 154.3111
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:08,961 - mme - INFO - laneline pred/gt: 3907/730
2025-03-06 15:08:08,961 - mme - INFO - roadside pred/gt: 4391/730
2025-03-06 15:08:09,243 - mme - INFO - Epoch [1][129/624]	lr: 2.634e-06, eta: 2:12:52, time: 0.391, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.3119, roadside_hm_loss: 2.4666, laneline_st_off_loss: 4.9020, roadside_st_off_loss: 4.8368, mask_loss: 0.5202, lane_type_mask_loss: 0.1894, lane_color_mask_loss: 0.1404, side_type_mask_loss: 0.1661, laneline_len_loss: 0.1584, laneline_field_loss: 2.3009, laneline_view_loss: 7.7046, laneline_inv_len_loss: 0.2199, laneline_inv_field_loss: 5.6990, roadside_len_loss: 0.1700, roadside_field_loss: 1.5526, roadside_view_loss: 4.5526, roadside_inv_len_loss: 0.1477, roadside_inv_field_loss: 2.6738, depth_loss: 0.1510, loss: 40.8640, grad_norm: 177.7496
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:09,359 - mme - INFO - laneline pred/gt: 3878/863
2025-03-06 15:08:09,359 - mme - INFO - roadside pred/gt: 4372/863
2025-03-06 15:08:09,641 - mme - INFO - Epoch [1][130/624]	lr: 2.654e-06, eta: 2:12:47, time: 0.398, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0485, roadside_hm_loss: 2.1543, laneline_st_off_loss: 6.4483, roadside_st_off_loss: 5.7804, mask_loss: 0.5023, lane_type_mask_loss: 0.1909, lane_color_mask_loss: 0.1365, side_type_mask_loss: 0.1614, laneline_len_loss: 0.1343, laneline_field_loss: 2.6115, laneline_view_loss: 9.3115, laneline_inv_len_loss: 0.1876, laneline_inv_field_loss: 6.9734, roadside_len_loss: 0.1443, roadside_field_loss: 1.6991, roadside_view_loss: 5.2436, roadside_inv_len_loss: 0.1272, roadside_inv_field_loss: 3.1057, depth_loss: 0.2131, loss: 47.1740, grad_norm: 169.4412
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:09,749 - mme - INFO - laneline pred/gt: 3995/399
2025-03-06 15:08:09,750 - mme - INFO - roadside pred/gt: 4464/399
2025-03-06 15:08:10,043 - mme - INFO - Epoch [1][131/624]	lr: 2.674e-06, eta: 2:12:42, time: 0.402, data_time: 0.005, memory: 6893, laneline_hm_loss: 3.0527, roadside_hm_loss: 3.2065, laneline_st_off_loss: 4.9943, roadside_st_off_loss: 4.2549, mask_loss: 0.6582, lane_type_mask_loss: 0.1962, lane_color_mask_loss: 0.1384, side_type_mask_loss: 0.1685, laneline_len_loss: 0.1776, laneline_field_loss: 1.9861, laneline_view_loss: 6.5813, laneline_inv_len_loss: 0.2881, laneline_inv_field_loss: 4.7981, roadside_len_loss: 0.1897, roadside_field_loss: 1.5565, roadside_view_loss: 4.7651, roadside_inv_len_loss: 0.1749, roadside_inv_field_loss: 2.9202, depth_loss: 0.0965, loss: 40.2038, grad_norm: 174.0666
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:10,152 - mme - INFO - laneline pred/gt: 3927/757
2025-03-06 15:08:10,153 - mme - INFO - roadside pred/gt: 4117/757
2025-03-06 15:08:10,445 - mme - INFO - Epoch [1][132/624]	lr: 2.694e-06, eta: 2:12:38, time: 0.403, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.1107, roadside_hm_loss: 2.2619, laneline_st_off_loss: 5.8244, roadside_st_off_loss: 5.7430, mask_loss: 0.4956, lane_type_mask_loss: 0.1708, lane_color_mask_loss: 0.1274, side_type_mask_loss: 0.1556, laneline_len_loss: 0.1671, laneline_field_loss: 2.4410, laneline_view_loss: 8.5938, laneline_inv_len_loss: 0.2138, laneline_inv_field_loss: 6.5972, roadside_len_loss: 0.1752, roadside_field_loss: 1.5591, roadside_view_loss: 4.9794, roadside_inv_len_loss: 0.1562, roadside_inv_field_loss: 3.0744, depth_loss: 0.2079, loss: 45.0546, grad_norm: 168.5270
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:10,554 - mme - INFO - laneline pred/gt: 3875/830
2025-03-06 15:08:10,554 - mme - INFO - roadside pred/gt: 4080/830
2025-03-06 15:08:10,837 - mme - INFO - Epoch [1][133/624]	lr: 2.713e-06, eta: 2:12:33, time: 0.392, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.0077, roadside_hm_loss: 2.1753, laneline_st_off_loss: 4.6103, roadside_st_off_loss: 3.8640, mask_loss: 0.4989, lane_type_mask_loss: 0.1848, lane_color_mask_loss: 0.1349, side_type_mask_loss: 0.1620, laneline_len_loss: 0.1420, laneline_field_loss: 2.4378, laneline_view_loss: 8.4476, laneline_inv_len_loss: 0.2021, laneline_inv_field_loss: 6.3763, roadside_len_loss: 0.1476, roadside_field_loss: 1.5415, roadside_view_loss: 4.7315, roadside_inv_len_loss: 0.1420, roadside_inv_field_loss: 2.8767, depth_loss: 0.2393, loss: 40.9222, grad_norm: 120.3220
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:10,954 - mme - INFO - laneline pred/gt: 3688/641
2025-03-06 15:08:10,954 - mme - INFO - roadside pred/gt: 3863/641
2025-03-06 15:08:11,237 - mme - INFO - Epoch [1][134/624]	lr: 2.733e-06, eta: 2:12:29, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.2630, roadside_hm_loss: 2.4472, laneline_st_off_loss: 5.1031, roadside_st_off_loss: 4.8116, mask_loss: 0.5183, lane_type_mask_loss: 0.1617, lane_color_mask_loss: 0.1273, side_type_mask_loss: 0.1479, laneline_len_loss: 0.1676, laneline_field_loss: 2.2564, laneline_view_loss: 7.5288, laneline_inv_len_loss: 0.2279, laneline_inv_field_loss: 5.7540, roadside_len_loss: 0.1787, roadside_field_loss: 1.4755, roadside_view_loss: 4.4088, roadside_inv_len_loss: 0.1646, roadside_inv_field_loss: 2.7744, depth_loss: 0.1763, loss: 40.6933, grad_norm: 174.9841
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:11,347 - mme - INFO - laneline pred/gt: 3774/593
2025-03-06 15:08:11,347 - mme - INFO - roadside pred/gt: 3835/593
2025-03-06 15:08:11,636 - mme - INFO - Epoch [1][135/624]	lr: 2.753e-06, eta: 2:12:24, time: 0.399, data_time: 0.005, memory: 6893, laneline_hm_loss: 2.3857, roadside_hm_loss: 2.5743, laneline_st_off_loss: 5.6623, roadside_st_off_loss: 4.2653, mask_loss: 0.5460, lane_type_mask_loss: 0.1578, lane_color_mask_loss: 0.1253, side_type_mask_loss: 0.1404, laneline_len_loss: 0.1783, laneline_field_loss: 2.3541, laneline_view_loss: 7.6382, laneline_inv_len_loss: 0.2059, laneline_inv_field_loss: 5.5094, roadside_len_loss: 0.1800, roadside_field_loss: 1.5952, roadside_view_loss: 4.5634, roadside_inv_len_loss: 0.1391, roadside_inv_field_loss: 2.7014, depth_loss: 0.1592, loss: 41.0812, grad_norm: 180.8703
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:11,746 - mme - INFO - laneline pred/gt: 3782/897
2025-03-06 15:08:11,746 - mme - INFO - roadside pred/gt: 3651/897
2025-03-06 15:08:12,034 - mme - INFO - Epoch [1][136/624]	lr: 2.773e-06, eta: 2:12:20, time: 0.397, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.8358, roadside_hm_loss: 2.0209, laneline_st_off_loss: 5.2185, roadside_st_off_loss: 4.1228, mask_loss: 0.4550, lane_type_mask_loss: 0.1456, lane_color_mask_loss: 0.1137, side_type_mask_loss: 0.1338, laneline_len_loss: 0.1349, laneline_field_loss: 2.7255, laneline_view_loss: 9.8123, laneline_inv_len_loss: 0.1827, laneline_inv_field_loss: 7.3170, roadside_len_loss: 0.1383, roadside_field_loss: 1.7020, roadside_view_loss: 5.3200, roadside_inv_len_loss: 0.1336, roadside_inv_field_loss: 3.1351, depth_loss: 0.1785, loss: 44.8259, grad_norm: 137.6337
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:12,143 - mme - INFO - laneline pred/gt: 3705/841
2025-03-06 15:08:12,144 - mme - INFO - roadside pred/gt: 3579/841
2025-03-06 15:08:12,434 - mme - INFO - Epoch [1][137/624]	lr: 2.792e-06, eta: 2:12:16, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0210, roadside_hm_loss: 2.1329, laneline_st_off_loss: 5.1977, roadside_st_off_loss: 5.5693, mask_loss: 0.5008, lane_type_mask_loss: 0.1481, lane_color_mask_loss: 0.1193, side_type_mask_loss: 0.1367, laneline_len_loss: 0.1428, laneline_field_loss: 2.4872, laneline_view_loss: 8.7570, laneline_inv_len_loss: 0.1849, laneline_inv_field_loss: 6.4527, roadside_len_loss: 0.1469, roadside_field_loss: 1.5962, roadside_view_loss: 4.7992, roadside_inv_len_loss: 0.1376, roadside_inv_field_loss: 2.7764, depth_loss: 0.2086, loss: 43.5153, grad_norm: 153.3412
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:12,544 - mme - INFO - laneline pred/gt: 3988/805
2025-03-06 15:08:12,545 - mme - INFO - roadside pred/gt: 3805/805
2025-03-06 15:08:12,833 - mme - INFO - Epoch [1][138/624]	lr: 2.812e-06, eta: 2:12:11, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.9819, roadside_hm_loss: 2.1989, laneline_st_off_loss: 5.2959, roadside_st_off_loss: 4.9465, mask_loss: 0.4792, lane_type_mask_loss: 0.1371, lane_color_mask_loss: 0.1131, side_type_mask_loss: 0.1291, laneline_len_loss: 0.1461, laneline_field_loss: 2.5750, laneline_view_loss: 9.0341, laneline_inv_len_loss: 0.1829, laneline_inv_field_loss: 6.6971, roadside_len_loss: 0.1494, roadside_field_loss: 1.6909, roadside_view_loss: 5.1446, roadside_inv_len_loss: 0.1356, roadside_inv_field_loss: 2.9942, depth_loss: 0.1982, loss: 44.2297, grad_norm: 185.3093
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:12,943 - mme - INFO - laneline pred/gt: 3713/876
2025-03-06 15:08:12,944 - mme - INFO - roadside pred/gt: 3571/876
2025-03-06 15:08:13,233 - mme - INFO - Epoch [1][139/624]	lr: 2.832e-06, eta: 2:12:07, time: 0.401, data_time: 0.007, memory: 6893, laneline_hm_loss: 1.8642, roadside_hm_loss: 2.0356, laneline_st_off_loss: 4.5272, roadside_st_off_loss: 3.9884, mask_loss: 0.4739, lane_type_mask_loss: 0.1343, lane_color_mask_loss: 0.1038, side_type_mask_loss: 0.1247, laneline_len_loss: 0.1381, laneline_field_loss: 2.5750, laneline_view_loss: 9.0609, laneline_inv_len_loss: 0.1735, laneline_inv_field_loss: 6.7607, roadside_len_loss: 0.1409, roadside_field_loss: 1.6404, roadside_view_loss: 5.0925, roadside_inv_len_loss: 0.1325, roadside_inv_field_loss: 2.9923, depth_loss: 0.2362, loss: 42.1950, grad_norm: 113.2077
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:13,343 - mme - INFO - laneline pred/gt: 3838/897
2025-03-06 15:08:13,344 - mme - INFO - roadside pred/gt: 3688/897
2025-03-06 15:08:13,630 - mme - INFO - Epoch [1][140/624]	lr: 2.852e-06, eta: 2:12:03, time: 0.397, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.8189, roadside_hm_loss: 2.0307, laneline_st_off_loss: 5.1935, roadside_st_off_loss: 4.8537, mask_loss: 0.4633, lane_type_mask_loss: 0.1293, lane_color_mask_loss: 0.1064, side_type_mask_loss: 0.1196, laneline_len_loss: 0.1308, laneline_field_loss: 2.6191, laneline_view_loss: 9.4757, laneline_inv_len_loss: 0.1744, laneline_inv_field_loss: 7.2259, roadside_len_loss: 0.1325, roadside_field_loss: 1.5556, roadside_view_loss: 4.9722, roadside_inv_len_loss: 0.1330, roadside_inv_field_loss: 3.0213, depth_loss: 0.2698, loss: 44.4256, grad_norm: 145.5935
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:13,739 - mme - INFO - laneline pred/gt: 3868/799
2025-03-06 15:08:13,740 - mme - INFO - roadside pred/gt: 3657/799
2025-03-06 15:08:14,021 - mme - INFO - Epoch [1][141/624]	lr: 2.872e-06, eta: 2:11:58, time: 0.391, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0367, roadside_hm_loss: 2.1871, laneline_st_off_loss: 6.5589, roadside_st_off_loss: 5.8870, mask_loss: 0.4839, lane_type_mask_loss: 0.1192, lane_color_mask_loss: 0.1090, side_type_mask_loss: 0.1169, laneline_len_loss: 0.1282, laneline_field_loss: 2.7953, laneline_view_loss: 9.6529, laneline_inv_len_loss: 0.1681, laneline_inv_field_loss: 6.9527, roadside_len_loss: 0.1303, roadside_field_loss: 1.7893, roadside_view_loss: 5.2475, roadside_inv_len_loss: 0.1202, roadside_inv_field_loss: 2.9696, depth_loss: 0.1569, loss: 47.6095, grad_norm: 197.6493
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:14,140 - mme - INFO - laneline pred/gt: 4016/637
2025-03-06 15:08:14,141 - mme - INFO - roadside pred/gt: 3833/637
2025-03-06 15:08:14,425 - mme - INFO - Epoch [1][142/624]	lr: 2.891e-06, eta: 2:11:55, time: 0.404, data_time: 0.016, memory: 6893, laneline_hm_loss: 2.1370, roadside_hm_loss: 2.2506, laneline_st_off_loss: 4.4083, roadside_st_off_loss: 3.8878, mask_loss: 0.5143, lane_type_mask_loss: 0.1277, lane_color_mask_loss: 0.1094, side_type_mask_loss: 0.1213, laneline_len_loss: 0.1469, laneline_field_loss: 2.3289, laneline_view_loss: 8.0094, laneline_inv_len_loss: 0.1925, laneline_inv_field_loss: 6.0084, roadside_len_loss: 0.1478, roadside_field_loss: 1.5492, roadside_view_loss: 4.7419, roadside_inv_len_loss: 0.1404, roadside_inv_field_loss: 2.9196, depth_loss: 0.1434, loss: 39.8850, grad_norm: 155.9458
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:14,534 - mme - INFO - laneline pred/gt: 3959/889
2025-03-06 15:08:14,534 - mme - INFO - roadside pred/gt: 3879/889
2025-03-06 15:08:14,825 - mme - INFO - Epoch [1][143/624]	lr: 2.911e-06, eta: 2:11:51, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.8407, roadside_hm_loss: 2.1333, laneline_st_off_loss: 5.2620, roadside_st_off_loss: 4.5195, mask_loss: 0.4755, lane_type_mask_loss: 0.1130, lane_color_mask_loss: 0.0975, side_type_mask_loss: 0.1090, laneline_len_loss: 0.1171, laneline_field_loss: 2.6317, laneline_view_loss: 9.4924, laneline_inv_len_loss: 0.1489, laneline_inv_field_loss: 6.9194, roadside_len_loss: 0.1178, roadside_field_loss: 1.6631, roadside_view_loss: 5.1250, roadside_inv_len_loss: 0.1144, roadside_inv_field_loss: 2.9528, depth_loss: 0.2345, loss: 44.0676, grad_norm: 376.3893
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:14,934 - mme - INFO - laneline pred/gt: 4087/877
2025-03-06 15:08:14,934 - mme - INFO - roadside pred/gt: 3926/877
2025-03-06 15:08:15,223 - mme - INFO - Epoch [1][144/624]	lr: 2.931e-06, eta: 2:11:47, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.8812, roadside_hm_loss: 1.9849, laneline_st_off_loss: 4.5244, roadside_st_off_loss: 4.0765, mask_loss: 0.4535, lane_type_mask_loss: 0.1247, lane_color_mask_loss: 0.1104, side_type_mask_loss: 0.1166, laneline_len_loss: 0.1437, laneline_field_loss: 2.6946, laneline_view_loss: 9.2250, laneline_inv_len_loss: 0.1795, laneline_inv_field_loss: 6.7524, roadside_len_loss: 0.1440, roadside_field_loss: 1.6309, roadside_view_loss: 4.8699, roadside_inv_len_loss: 0.1448, roadside_inv_field_loss: 2.8409, depth_loss: 0.1595, loss: 42.0573, grad_norm: 159.0104
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:15,333 - mme - INFO - laneline pred/gt: 4021/700
2025-03-06 15:08:15,333 - mme - INFO - roadside pred/gt: 3881/700
2025-03-06 15:08:15,625 - mme - INFO - Epoch [1][145/624]	lr: 2.951e-06, eta: 2:11:43, time: 0.401, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.9487, roadside_hm_loss: 2.1636, laneline_st_off_loss: 4.2958, roadside_st_off_loss: 3.9574, mask_loss: 0.4738, lane_type_mask_loss: 0.1097, lane_color_mask_loss: 0.0966, side_type_mask_loss: 0.1056, laneline_len_loss: 0.1277, laneline_field_loss: 2.6742, laneline_view_loss: 9.7554, laneline_inv_len_loss: 0.1636, laneline_inv_field_loss: 7.2018, roadside_len_loss: 0.1297, roadside_field_loss: 1.7258, roadside_view_loss: 5.5136, roadside_inv_len_loss: 0.1222, roadside_inv_field_loss: 3.2406, depth_loss: 0.1604, loss: 43.9662, grad_norm: 143.4383
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:15,734 - mme - INFO - laneline pred/gt: 4042/714
2025-03-06 15:08:15,734 - mme - INFO - roadside pred/gt: 3924/714
2025-03-06 15:08:16,023 - mme - INFO - Epoch [1][146/624]	lr: 2.971e-06, eta: 2:11:39, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.9421, roadside_hm_loss: 2.0853, laneline_st_off_loss: 4.1308, roadside_st_off_loss: 4.6247, mask_loss: 0.4669, lane_type_mask_loss: 0.1091, lane_color_mask_loss: 0.0973, side_type_mask_loss: 0.1086, laneline_len_loss: 0.1404, laneline_field_loss: 2.7907, laneline_view_loss: 9.3409, laneline_inv_len_loss: 0.1722, laneline_inv_field_loss: 6.6445, roadside_len_loss: 0.1395, roadside_field_loss: 1.7496, roadside_view_loss: 5.0782, roadside_inv_len_loss: 0.1346, roadside_inv_field_loss: 2.9034, depth_loss: 0.1628, loss: 42.8213, grad_norm: 131.0489
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:16,132 - mme - INFO - laneline pred/gt: 4048/828
2025-03-06 15:08:16,132 - mme - INFO - roadside pred/gt: 4155/828
2025-03-06 15:08:16,426 - mme - INFO - Epoch [1][147/624]	lr: 2.990e-06, eta: 2:11:36, time: 0.403, data_time: 0.005, memory: 6893, laneline_hm_loss: 1.9067, roadside_hm_loss: 2.1003, laneline_st_off_loss: 4.2236, roadside_st_off_loss: 3.9731, mask_loss: 0.4659, lane_type_mask_loss: 0.1166, lane_color_mask_loss: 0.1001, side_type_mask_loss: 0.1091, laneline_len_loss: 0.1400, laneline_field_loss: 2.5624, laneline_view_loss: 8.8730, laneline_inv_len_loss: 0.1751, laneline_inv_field_loss: 6.5991, roadside_len_loss: 0.1404, roadside_field_loss: 1.6190, roadside_view_loss: 4.8918, roadside_inv_len_loss: 0.1362, roadside_inv_field_loss: 2.9269, depth_loss: 0.2103, loss: 41.2694, grad_norm: 131.8484
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:16,536 - mme - INFO - laneline pred/gt: 3942/708
2025-03-06 15:08:16,536 - mme - INFO - roadside pred/gt: 4101/708
2025-03-06 15:08:16,825 - mme - INFO - Epoch [1][148/624]	lr: 3.010e-06, eta: 2:11:32, time: 0.393, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0586, roadside_hm_loss: 2.2335, laneline_st_off_loss: 4.0285, roadside_st_off_loss: 3.4645, mask_loss: 0.4979, lane_type_mask_loss: 0.1023, lane_color_mask_loss: 0.0966, side_type_mask_loss: 0.1019, laneline_len_loss: 0.1403, laneline_field_loss: 2.4176, laneline_view_loss: 8.5235, laneline_inv_len_loss: 0.1717, laneline_inv_field_loss: 6.1439, roadside_len_loss: 0.1420, roadside_field_loss: 1.5540, roadside_view_loss: 4.9731, roadside_inv_len_loss: 0.1362, roadside_inv_field_loss: 2.8480, depth_loss: 0.1472, loss: 39.7813, grad_norm: 144.1460
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:16,936 - mme - INFO - laneline pred/gt: 4006/1030
2025-03-06 15:08:16,936 - mme - INFO - roadside pred/gt: 4126/1030
2025-03-06 15:08:17,225 - mme - INFO - Epoch [1][149/624]	lr: 3.030e-06, eta: 2:11:29, time: 0.406, data_time: 0.013, memory: 6893, laneline_hm_loss: 1.6316, roadside_hm_loss: 1.7817, laneline_st_off_loss: 4.4530, roadside_st_off_loss: 4.3297, mask_loss: 0.4194, lane_type_mask_loss: 0.1078, lane_color_mask_loss: 0.0996, side_type_mask_loss: 0.1073, laneline_len_loss: 0.0984, laneline_field_loss: 2.6939, laneline_view_loss: 9.4397, laneline_inv_len_loss: 0.1255, laneline_inv_field_loss: 7.1939, roadside_len_loss: 0.0997, roadside_field_loss: 1.6920, roadside_view_loss: 4.8368, roadside_inv_len_loss: 0.0998, roadside_inv_field_loss: 2.8521, depth_loss: 0.2968, loss: 42.3588, grad_norm: 132.9461
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:17,334 - mme - INFO - laneline pred/gt: 4043/810
2025-03-06 15:08:17,335 - mme - INFO - roadside pred/gt: 4112/810
2025-03-06 15:08:17,623 - mme - INFO - Epoch [1][150/624]	lr: 3.050e-06, eta: 2:11:25, time: 0.397, data_time: 0.005, memory: 6893, laneline_hm_loss: 1.8269, roadside_hm_loss: 1.9296, laneline_st_off_loss: 3.9144, roadside_st_off_loss: 3.3929, mask_loss: 0.4594, lane_type_mask_loss: 0.1025, lane_color_mask_loss: 0.0890, side_type_mask_loss: 0.1016, laneline_len_loss: 0.1223, laneline_field_loss: 2.7628, laneline_view_loss: 9.6065, laneline_inv_len_loss: 0.1628, laneline_inv_field_loss: 6.9656, roadside_len_loss: 0.1245, roadside_field_loss: 1.7573, roadside_view_loss: 5.2104, roadside_inv_len_loss: 0.1241, roadside_inv_field_loss: 3.0051, depth_loss: 0.1673, loss: 41.8251, grad_norm: 112.9583
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:17,732 - mme - INFO - laneline pred/gt: 4051/781
2025-03-06 15:08:17,732 - mme - INFO - roadside pred/gt: 4220/781
2025-03-06 15:08:18,023 - mme - INFO - Epoch [1][151/624]	lr: 3.070e-06, eta: 2:11:22, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0031, roadside_hm_loss: 2.1080, laneline_st_off_loss: 3.9040, roadside_st_off_loss: 3.7088, mask_loss: 0.5046, lane_type_mask_loss: 0.0989, lane_color_mask_loss: 0.0894, side_type_mask_loss: 0.0936, laneline_len_loss: 0.1138, laneline_field_loss: 2.3878, laneline_view_loss: 7.8903, laneline_inv_len_loss: 0.1464, laneline_inv_field_loss: 5.6648, roadside_len_loss: 0.1116, roadside_field_loss: 1.5734, roadside_view_loss: 4.3508, roadside_inv_len_loss: 0.1091, roadside_inv_field_loss: 2.5023, depth_loss: 0.1586, loss: 37.5193, grad_norm: 134.1409
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:18,133 - mme - INFO - laneline pred/gt: 3896/831
2025-03-06 15:08:18,133 - mme - INFO - roadside pred/gt: 4232/831
2025-03-06 15:08:18,422 - mme - INFO - Epoch [1][152/624]	lr: 3.089e-06, eta: 2:11:18, time: 0.399, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.7016, roadside_hm_loss: 1.8248, laneline_st_off_loss: 3.9004, roadside_st_off_loss: 3.8360, mask_loss: 0.4528, lane_type_mask_loss: 0.1067, lane_color_mask_loss: 0.0931, side_type_mask_loss: 0.1026, laneline_len_loss: 0.1411, laneline_field_loss: 2.5355, laneline_view_loss: 9.1694, laneline_inv_len_loss: 0.1588, laneline_inv_field_loss: 6.8205, roadside_len_loss: 0.1420, roadside_field_loss: 1.6261, roadside_view_loss: 5.2092, roadside_inv_len_loss: 0.1359, roadside_inv_field_loss: 3.0329, depth_loss: 0.1870, loss: 41.1766, grad_norm: 140.0689
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:18,531 - mme - INFO - laneline pred/gt: 3867/635
2025-03-06 15:08:18,532 - mme - INFO - roadside pred/gt: 4307/635
2025-03-06 15:08:18,825 - mme - INFO - Epoch [1][153/624]	lr: 3.109e-06, eta: 2:11:15, time: 0.402, data_time: 0.006, memory: 6893, laneline_hm_loss: 2.0969, roadside_hm_loss: 2.2550, laneline_st_off_loss: 3.2059, roadside_st_off_loss: 3.0722, mask_loss: 0.5362, lane_type_mask_loss: 0.1051, lane_color_mask_loss: 0.1007, side_type_mask_loss: 0.1001, laneline_len_loss: 0.1365, laneline_field_loss: 2.1773, laneline_view_loss: 6.9543, laneline_inv_len_loss: 0.1541, laneline_inv_field_loss: 5.0094, roadside_len_loss: 0.1358, roadside_field_loss: 1.4826, roadside_view_loss: 3.9809, roadside_inv_len_loss: 0.1257, roadside_inv_field_loss: 2.3052, depth_loss: 0.1316, loss: 34.0655, grad_norm: 119.8524
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:18,934 - mme - INFO - laneline pred/gt: 3805/812
2025-03-06 15:08:18,935 - mme - INFO - roadside pred/gt: 4259/812
2025-03-06 15:08:19,226 - mme - INFO - Epoch [1][154/624]	lr: 3.129e-06, eta: 2:11:12, time: 0.401, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.7270, roadside_hm_loss: 1.9139, laneline_st_off_loss: 3.5254, roadside_st_off_loss: 3.5754, mask_loss: 0.4154, lane_type_mask_loss: 0.0999, lane_color_mask_loss: 0.0909, side_type_mask_loss: 0.0987, laneline_len_loss: 0.1408, laneline_field_loss: 2.5442, laneline_view_loss: 9.1581, laneline_inv_len_loss: 0.1617, laneline_inv_field_loss: 6.9896, roadside_len_loss: 0.1410, roadside_field_loss: 1.5593, roadside_view_loss: 4.9673, roadside_inv_len_loss: 0.1438, roadside_inv_field_loss: 3.0272, depth_loss: 0.2302, loss: 40.5097, grad_norm: 99.4082
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:19,335 - mme - INFO - laneline pred/gt: 3662/789
2025-03-06 15:08:19,335 - mme - INFO - roadside pred/gt: 4187/789
2025-03-06 15:08:19,618 - mme - INFO - Epoch [1][155/624]	lr: 3.149e-06, eta: 2:11:08, time: 0.392, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.8182, roadside_hm_loss: 1.9417, laneline_st_off_loss: 3.5318, roadside_st_off_loss: 3.3724, mask_loss: 0.4386, lane_type_mask_loss: 0.0941, lane_color_mask_loss: 0.0905, side_type_mask_loss: 0.0959, laneline_len_loss: 0.1264, laneline_field_loss: 2.4360, laneline_view_loss: 8.0914, laneline_inv_len_loss: 0.1510, laneline_inv_field_loss: 6.0463, roadside_len_loss: 0.1266, roadside_field_loss: 1.4980, roadside_view_loss: 4.2387, roadside_inv_len_loss: 0.1306, roadside_inv_field_loss: 2.5613, depth_loss: 0.1942, loss: 36.9839, grad_norm: 105.9014
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:19,736 - mme - INFO - laneline pred/gt: 3731/739
2025-03-06 15:08:19,736 - mme - INFO - roadside pred/gt: 4284/739
2025-03-06 15:08:20,017 - mme - INFO - Epoch [1][156/624]	lr: 3.169e-06, eta: 2:11:04, time: 0.400, data_time: 0.015, memory: 6893, laneline_hm_loss: 1.8913, roadside_hm_loss: 2.0161, laneline_st_off_loss: 3.3323, roadside_st_off_loss: 3.3432, mask_loss: 0.4742, lane_type_mask_loss: 0.0968, lane_color_mask_loss: 0.0974, side_type_mask_loss: 0.0988, laneline_len_loss: 0.1241, laneline_field_loss: 2.4196, laneline_view_loss: 7.9967, laneline_inv_len_loss: 0.1528, laneline_inv_field_loss: 5.8433, roadside_len_loss: 0.1251, roadside_field_loss: 1.5845, roadside_view_loss: 4.4994, roadside_inv_len_loss: 0.1259, roadside_inv_field_loss: 2.6220, depth_loss: 0.2196, loss: 37.0630, grad_norm: 104.8336
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:20,134 - mme - INFO - laneline pred/gt: 3758/802
2025-03-06 15:08:20,134 - mme - INFO - roadside pred/gt: 4340/802
2025-03-06 15:08:20,426 - mme - INFO - Epoch [1][157/624]	lr: 3.188e-06, eta: 2:11:02, time: 0.408, data_time: 0.013, memory: 6893, laneline_hm_loss: 1.7862, roadside_hm_loss: 1.9437, laneline_st_off_loss: 3.4036, roadside_st_off_loss: 2.9240, mask_loss: 0.4384, lane_type_mask_loss: 0.0903, lane_color_mask_loss: 0.0811, side_type_mask_loss: 0.0921, laneline_len_loss: 0.1218, laneline_field_loss: 2.6386, laneline_view_loss: 9.2657, laneline_inv_len_loss: 0.1486, laneline_inv_field_loss: 6.8277, roadside_len_loss: 0.1256, roadside_field_loss: 1.6643, roadside_view_loss: 5.1332, roadside_inv_len_loss: 0.1252, roadside_inv_field_loss: 2.9827, depth_loss: 0.1849, loss: 39.9776, grad_norm: 134.5889
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:20,535 - mme - INFO - laneline pred/gt: 3548/807
2025-03-06 15:08:20,536 - mme - INFO - roadside pred/gt: 4100/807
2025-03-06 15:08:20,826 - mme - INFO - Epoch [1][158/624]	lr: 3.208e-06, eta: 2:10:59, time: 0.401, data_time: 0.007, memory: 6893, laneline_hm_loss: 1.6906, roadside_hm_loss: 1.8354, laneline_st_off_loss: 4.2245, roadside_st_off_loss: 3.9044, mask_loss: 0.4027, lane_type_mask_loss: 0.0871, lane_color_mask_loss: 0.0809, side_type_mask_loss: 0.0871, laneline_len_loss: 0.1361, laneline_field_loss: 2.5109, laneline_view_loss: 9.1590, laneline_inv_len_loss: 0.1470, laneline_inv_field_loss: 6.9547, roadside_len_loss: 0.1359, roadside_field_loss: 1.5754, roadside_view_loss: 5.0734, roadside_inv_len_loss: 0.1359, roadside_inv_field_loss: 3.0527, depth_loss: 0.1914, loss: 41.3851, grad_norm: 141.1676
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:20,936 - mme - INFO - laneline pred/gt: 3555/1032
2025-03-06 15:08:20,936 - mme - INFO - roadside pred/gt: 4178/1032
2025-03-06 15:08:21,226 - mme - INFO - Epoch [1][159/624]	lr: 3.228e-06, eta: 2:10:56, time: 0.400, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.5999, roadside_hm_loss: 1.7442, laneline_st_off_loss: 4.1534, roadside_st_off_loss: 4.0327, mask_loss: 0.4112, lane_type_mask_loss: 0.0805, lane_color_mask_loss: 0.0762, side_type_mask_loss: 0.0834, laneline_len_loss: 0.1004, laneline_field_loss: 2.9580, laneline_view_loss: 11.3410, laneline_inv_len_loss: 0.1166, laneline_inv_field_loss: 8.3466, roadside_len_loss: 0.0988, roadside_field_loss: 1.8002, roadside_view_loss: 5.7946, roadside_inv_len_loss: 0.1031, roadside_inv_field_loss: 3.2188, depth_loss: 0.2827, loss: 46.3423, grad_norm: 111.2389
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:21,336 - mme - INFO - laneline pred/gt: 3364/887
2025-03-06 15:08:21,336 - mme - INFO - roadside pred/gt: 4179/887
2025-03-06 15:08:21,627 - mme - INFO - Epoch [1][160/624]	lr: 3.248e-06, eta: 2:10:53, time: 0.401, data_time: 0.006, memory: 6893, laneline_hm_loss: 1.5932, roadside_hm_loss: 1.7993, laneline_st_off_loss: 3.9810, roadside_st_off_loss: 3.0384, mask_loss: 0.4347, lane_type_mask_loss: 0.0890, lane_color_mask_loss: 0.0802, side_type_mask_loss: 0.0907, laneline_len_loss: 0.1257, laneline_field_loss: 2.5206, laneline_view_loss: 9.0639, laneline_inv_len_loss: 0.1380, laneline_inv_field_loss: 6.8555, roadside_len_loss: 0.1259, roadside_field_loss: 1.5274, roadside_view_loss: 4.8457, roadside_inv_len_loss: 0.1295, roadside_inv_field_loss: 2.9092, depth_loss: 0.2466, loss: 39.5944, grad_norm: 179.1098
Shape of depth_pred: torch.Size([8, 1, 72, 216])
2025-03-06 15:08:21,737 - mme - INFO - laneline pred/gt: 3398/776
2025-03-06 15:08:21,737 - mme - INFO - roadside pred/gt: 4138/776
