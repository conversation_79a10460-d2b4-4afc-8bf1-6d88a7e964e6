{"model_files": {"net": {"input": {"image": "image", "image2": "image2"}, "output": {"laneline_field": "laneline_field", "attributes": "attributes", "heat_nms_weight_by_relu6_proposal_heatmap": "heat_nms_weight_by_relu6_proposal_heatmap", "heat_nms_weight_by_relu6_line_mask": "heat_nms_weight_by_relu6_line_mask", "sigmoid_proposal_offset": "sigmoid_proposal_offset", "sigmoid_line_mask": "sigmoid_line_mask", "laneline_vec_field": "laneline_vec_field", "roadside_vec_field": "roadside_vec_field", "field_pass_simp_laneline_inv_vector_field": "field_pass_simp_laneline_inv_vector_field", "scale_length_roadside_length": "scale_length_roadside_length"}, "backend": "kestrel_onnx", "net": "net/model.onnx", "max_batch_size": 1}}, "image_width": 864, "image_height": 288, "input_w": 864, "input_h": 288, "long_scale": 864, "short_scale": 288, "pixel_means": [75.3, 76.6, 77.6], "pixel_stds": [50.5, 53.8, 54.3], "num_classes": 2, "image_channel": 3, "model_preprocess_support": true, "proposal_heatmap_thr": 0.6, "key_points_size_thr": 35, "roadside_key_points_size_thr": 35, "breakpoints_thr": 0.6, "with_breakpoints": true, "lane_proposal_thresh": 0.6, "roadside_proposal_thresh": 0.6, "momentum": 0.4, "view_distance": 1, "field_scale": -1, "length_scale": 0, "num_row_ref_point": 2, "slice_top": 0, "slice_bottom": 288, "slice_left": 0, "slice_right": 864, "overlap_thr": 3, "th_dp_radius_laneline": 2, "th_dp_radius_roadside": 9, "line_lenght_thr": 10}