import ipdb
import torch
from torch import nn
from torch.nn import functional as F

from pillar.models import MODELS
from .condlanenet import CondLaneNet
from ...core.post_processing import CondLanePostProcessorBeta


class LaneGtGather(nn.Module):

    def __init__(self, soft_label=True, x_weight=False):
        super().__init__()
        self.soft_label = soft_label
        self.x_weight = x_weight

    def pooling(self, t, **kwargs):
        mask = kwargs[f"{t}_mask"]
        label = mask.float()
        H, W = kwargs[f"{t}_type"].shape[-2:]
        mask_attr = F.adaptive_max_pool2d(label, (H, W))
        kwargs[f"{t}_mask_attr"] = mask_attr > 0
        return kwargs

    @torch.no_grad()
    def forward(self, **kwargs):
        kwargs = self.pooling("laneline", **kwargs)
        kwargs = self.pooling("roadside", **kwargs)
        kwargs["mix_mask"] = kwargs["laneline_mask"] | kwargs["roadside_mask"]
        return kwargs


@MODELS.register_module()
class FieldLaneNetBeta(CondLaneNet):

    def __init__(self, post_processor={}, gt_gather={}, *args, **kwargs):
        super().__init__(post_processor=post_processor, *args, **kwargs)
        self.post_processor = CondLanePostProcessorBeta(**post_processor)
        self.gt_gather = LaneGtGather(**gt_gather)

    def forward_train(self, img, img_metas, **kwargs):
        output, _ = self.extract_feat(img)
        output = self.head.forward_train(output)

        kwargs = self.gt_gather(**kwargs)
        losses = self.losses(output, img_metas, **kwargs)
        return losses
