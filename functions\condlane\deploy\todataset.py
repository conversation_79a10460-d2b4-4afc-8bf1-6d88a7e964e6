import os
import json
import base64
import subprocess

import torch
import numpy as np
from tqdm import tqdm

from .utils import model_infer


def write_file(path):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    return open(path, "w")


def dump_tsr(path, npy):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    np.save(path, npy)


def dump_bin(path, npy):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    npy.tofile(path)


def dump_tsrs(data_dict, data_id, outdir, data_name, data_type):
    tensors = {}
    for k, v in data_dict.items():
        file = os.path.join(k, f"{data_id}.npy")
        if data_type == "feature_map":
            file = base64.b64encode(v[:1].tobytes()).decode()
        elif data_type == "blob_data":
            dump_tsr(os.path.join(outdir, data_name, file), v[:1])
        else:
            raise NotImplementedError
        tensors[k] = {data_type: file, "#keson_code": "TSR"}
    meta_dict = {"id": data_id, "tensors": {data_name: tensors}}
    return meta_dict


def dump_bins(data_dict, data_id, outdir):
    for k, v in data_dict.items():
        file = os.path.join(k, f"{data_id}.bin")
        dump_bin(os.path.join(outdir, file), v[:1])


def dump_json(name, type, file):
    data = dict(
        name=name,
        type=type,
        file=file,
        security="private",
    )
    file = file + ".json"
    json.dump(data, open(file, "w"), indent=2)
    print(f">>> python -m adela.cmd -p <pid> --dataset_add {file} [--force]")


def tar_data(files, outdir):
    subprocess.run(
        ["tar", "cvf", "data.tar", *files],
        check=True,
        cwd=outdir,
    )
    print(f">>> {os.path.join(outdir, 'data.tar')}")


def todataset(model, data, data_info, args):
    align_dir = os.path.join(args.outdir, "align")
    quant_dir = os.path.join(args.outdir, "quant")
    files = {}
    for phase in ["proposal", "detect", "net"]:
        fi = os.path.join(align_dir, phase, "input.txt")
        fo = os.path.join(align_dir, phase, "ground_truth.txt")
        files[phase] = [write_file(fi), write_file(fo)]

    for i in tqdm(range(data.shape[0])):
        outputs = model_infer(model, data[i:i + 1], args.dup_i, th=0.1)
        for phase, v_io in outputs.items():
            for v in v_io.values():
                for k in v:
                    if torch.is_tensor(v[k]):
                        v[k] = v[k].detach().cpu().numpy()
            dump_bins(v_io["input"], i, quant_dir)

            for k in list(v_io["input"].keys()):
                if not k.startswith("image"):
                    continue
                image = v_io["input"][k]
                if args.u8:
                    image = data_info[i][-2][None]
                if args.nhwc:
                    image = image.transpose(0, 2, 3, 1)
                v_io["input"][k] = image

            ret = dump_tsrs(v_io["input"], i, align_dir, phase, "blob_data")
            files[phase][0].write(json.dumps(ret) + "\n")
            #ret = dump_tsrs(v_io["output"], i, align_dir, phase, "blob_data")
            ret = dump_tsrs(v_io["output"], i, align_dir, phase, "feature_map")
            files[phase][1].write(json.dumps(ret) + "\n")

    names = []
    for phase, v_io in outputs.items():
        for f in files[phase]:
            f.close()
        if args.version == 2:
            dump_json(
                "condlane_tensor",
                "tensor_comparation",
                os.path.join(align_dir, phase),
            )
        else:
            keys = list(v_io["input"].keys())
            names.extend(keys)
            tar_data(
                ["input.txt", "ground_truth.txt"] + keys,
                os.path.join(align_dir, phase),
            )

    ret = {k: v for m in outputs.values() for k, v in m["output"].items()}
    if args.version == 2:
        dump_json("condlane_quant", "quant", quant_dir)
    else:
        tar_data(names, quant_dir)
    return ret
