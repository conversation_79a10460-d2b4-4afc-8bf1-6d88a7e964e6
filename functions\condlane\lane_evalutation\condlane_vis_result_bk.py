from fileinput import filename
import os
import cv2
import json
import ipdb
import numpy as np
import tqdm 
# from tqdm import tqdm
from multiprocessing import Pool
from argparse import ArgumentParser
try:
    from petrel_client.client import Client
except Exception as e:
    print(e)
    pass
# from petrel_client.client import Client
import cv2
from PIL import Image, ImageDraw, ImageFont
from scipy.ndimage.filters import maximum_filter1d, minimum_filter1d, uniform_filter1d
#from scipy.signal.medfilt import medfilt

client = None

# B G R (0, 0, 255), 
COLORS = [
    (255, 255, 0), (255, 0, 255), (255, 0, 0), (0, 255, 0), (0, 255, 255), (128, 255, 0),
    (255, 128, 0), (128, 0, 255), (255, 0, 128), (0, 128, 255), (0, 255, 128), (128, 255, 255),
    (255, 128, 255), (255, 255, 128), (60, 180, 0), (180, 60, 0), (0, 60, 180), (0, 180, 60),
    (60, 0, 180), (180, 0, 60), (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),(255, 0, 255),
    (0, 255, 255), (128, 255, 0), (255, 128, 0), (128, 0, 255),
]
Lane_type = [
    "NO_LANE", "OTHER_LANE_TYPE", "SOLID_LANE",
    "DASHED_LANE", "SHORT_THICK_DASHED_LANE",
    "SPLIT_LANE", "MERGE_LANE",
    "LEFT_DASHED_RIGHT_SOLID", "LEFT_SOLID_RIGHT_DASHED",
    "DOUBLE_SOLID", "DOUBLE_DASHED",
    "FISHBONE_SOLID", "FISHBONE_DASHED", "SPLIT_MERGE_LANE",
    "INVALID_VALUE"
]
Lane_type_cn = [
    u'无车道线', u'其他线型', u'实线', u'虚线', u'短粗线',
    u'分离线', u'合并线', u'左虚右实', u'左实右虚',
    u'双实线', u'双虚线', u'鱼骨实线', u'鱼骨虚线', u'分离合并线',
    u'无效属性'
]
Lane_color = [
    "NO_LANE", "OTHER_LANE_COLOR", "WHITE",
    "YELLOW", "ORANGE", "BLUE"
]
Road_type = [
    "NO_SIDE", "ROAD_CURB_EDGE", "FENCE_EDGE",
    "WALL_EDGE_OR_PLANT_EDGE", "MOVABLE_EDGE", "OTHER_EDGE"
]

def parse_args():
    parser = ArgumentParser()
    #parser.add_argument("-i", "--prefix", default="s3://sh30_rdpro2_datasets", help="prefix of image_path")
    parser.add_argument("-i", "--prefix", default="", help="prefix of image_path")
    parser.add_argument("-o", "--outdir", default="vis", help="output dir for vis")
    parser.add_argument("-n", "--num",  default=10, type=int, help="number for vis")
    parser.add_argument('--workers', default=4, type=int, help='num of workers')
    parser.add_argument("-c", "--client_conf",
                        default="/mnt/lustre/config/petreloss_lane.conf",
                        help="config for petrel client")
    parser.add_argument("result_files", nargs="+", help="list of result files")
    parser.add_argument("--ori_json_file", default="front_120.json", help="video json file")
    args = parser.parse_args()
    return args

def abspath(file, img_prefix):
    if len(file) <= 0: return ""
    if "s3://" in file or file.startswith("/"):  #xxx:s3://bucket/relpath #/prefix/relpath
        if file.startswith("shlg:s3://shlg_GAC"):
            file = file.replace("shlg:s3://shlg_GAC", "s3://sh30_GAC")
        return file
    if len(img_prefix) <= 0 or file.startswith(img_prefix): return file
    return os.path.join(img_prefix, file)

def relpath(file, img_prefix=""):
    if len(file) <= 0: return "", ""
    if "s3://" in file: # xxx:s3://bucket/relpath -> relpath
        prefix, file = file.split("s3://", 1)
        bucket, file = file.split("/", 1)
        prefix = f"{prefix}s3://{bucket}"
    else:
        prefix = img_prefix if len(img_prefix) > 0 else "/"
        # /prefix/relpath -> prefix/relpath  # /prefix/relpath -> relpath
        file = os.path.relpath(file, prefix)
    return prefix, file

def draw_lines_roadside(img, points, color=(0, 255, 0)):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    for i in range(len(points) - 1):
        img = cv2.line(img, (int(points[i][0]), int(points[i][1])),
                 (int(points[i + 1][0]), int(points[i + 1][1])), color, 4)
    return img

def draw_lanelines(img, points, idx=1, width=4):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    for i in range(len(points) - 1):
        img = cv2.line(img, (int(points[i][0]), int(points[i][1])),
                 (int(points[i + 1][0]), int(points[i + 1][1])), idx, width)
    return img

def cv2AddChineseText(img, text, position, textColor=(0,255,0),textSize=30):
    if(isinstance(img, np.ndarray)):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw      = ImageDraw.Draw(img)   # 字体格式
    # fontStyle = ImageFont.truetype("/work/home/<USER>/Projects/kerr/conditional-lane-detection-one-two-stage/functions/condlane/lane_evalutation/simsun.ttc", textSize, encoding="utf-8")
    fontStyle = ImageFont.truetype("/home/<USER>/condlane_new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/functions/condlane/lane_evalutation/simsun.ttc", textSize, encoding="utf-8")
    draw.text(position, text, textColor, font=fontStyle)     # 绘制文本
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)

def smooth_points(points):
    new_points = []
    x_1, y_1 = points[0][0], points[0][1]
    new_points.append([x_1, y_1])
    for i in range(1, len(points)-1):
        x_1, y_1 = points[i-1][0], points[i-1][1]
        x0, y0   = points[i][0], points[i][1]
        x1, y1   = points[i+1][0], points[i+1][1]
        x_m, y_m = round((x_1+x0+x1)/3.0), round((y_1+y0+y1)/3.0)
        new_points.append([x_m, y_m])
    x1, y1   = points[-1][0], points[-1][1]
    new_points.append([x1, y1])
    return new_points

def draw_lines(img, points, score=0.0, color=(0, 255, 0), lane_type=0, off_w=80, off_h=30):
    im_h, im_w = img.shape[:2]
    if isinstance(points, dict): points = [[_["x"], _["y"]] for _ in points["points"]]
    points = [[p[0], p[1]] for p in points if 0 <=p[0]< im_w and 0 <=p[1]< im_h]
    if len(points) > 2: points = smooth_points(points)
    for i in range(0, len(points)-1):
        x0, y0 = round(points[i][0]), round(points[i][1])
        x1, y1 = round(points[i+1][0]), round(points[i+1][1])
        img = cv2.line(img, (x0, y0), (x1, y1), color, 2)
        #
        cv2.circle(img, (x0, y0), 5, (0,0,255), -1)
  
    # visual first point
    f_x0, f_y0 = round(points[-1][0]), round(points[-1][1])
    cv2.circle(img,(f_x0, f_y0), 5, (0,0,255), -1)
    f_x0, f_y0 = f_x0 + 10, f_y0 + 10
    f_x0 = f_x0 if (im_w - f_x0 > off_w) else (im_w - off_w)
    f_y0 = f_y0 if (im_h - f_y0 > off_h) else (im_h - off_h)
    # # 图片对象、文本、像素、字体、字体大小、颜色、字体粗细*****cv2默认为BGR顺序
    text = format(score,".2f") + " " + Lane_type_cn[lane_type]
    img  = cv2AddChineseText(img, text, (f_x0, f_y0), (255, 0, 0), 50)

    return img

def draw_points(img, points, point_scores=[], color=(0, 0, 255)):
    if isinstance(points, dict):
        points = [[_["x"], _["y"]] for _ in points["points"]]
    # for pt in points:
    if point_scores != []: point_scores = point_scores["scores"]
    for i, pt in enumerate(points):
        if point_scores != []:
            assert len(point_scores)==len(points), "bk_points is not equal to bk_scores"
            font = cv2.FONT_HERSHEY_DUPLEX
            text = format(point_scores[i],".2f") + " "+ str((int(pt[0]), int(pt[1])))
            if pt[1] > 1080 and point_scores[i] >= 0.75: # and bk_flag
                img = cv2.circle(img, (int(pt[0]), int(pt[1])), 16, color, 2)
                cv2.putText(img, text, (int(pt[0])+10, int(pt[1])+10), font, 1.0, color, 2)

    return img

def draw_roi_boxs(img, roi):
    t, l, h, w = roi[0],roi[1],roi[2],roi[3]  #top 540, left 0, h 1620, w 3840
    x_min,y_min,x_max,y_max= l,t,l+w,t+h
    cv2.rectangle(img,(x_min,y_min),(x_max,y_max),(255,0,255),4) #color B G R // thinkness
    return img

def load_img(image_path, prefix=""):
    image_path = os.path.join(prefix, image_path)
    if "s3://" in image_path:
        img_bytes = client.Get(image_path, update_cache=False)
        assert img_bytes is not None, image_path
        img_mem_view = memoryview(img_bytes)
        img_array = np.frombuffer(img_mem_view, np.uint8)
        assert len(img_array) > 0, image_path
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
    else:
        img = cv2.imread(image_path)
    return img

def argmax(data, keys=None):
    if isinstance(data, dict):
        data = [data[k] for k in keys]
    return int(np.argmax(data))

def vis_result_pool(data):
    image_path, lines = data[0], data[1]
    prefix, image_path = relpath(image_path, img_prefix="")
    img = load_img(image_path, prefix)
    if len(lines) > 0:
        for idx, line in enumerate(lines):
            points = line["key_points"]
            if isinstance(points, dict): points = [[_["x"], _["y"]] for _ in points["points"]]
            if len(points) < 2: continue
            ####----draw efficient ROI----####
            roi = [line["roi"]["top"], line["roi"]["left"], line["roi"]["height"], line["roi"]["width"]]
            img = draw_roi_boxs(img, roi)
            if line["label"] in [0, "LANELINE", "laneline"]:
                lane_type_data = line.get("attribute",{}).get("lane_type",[])
                if type(lane_type_data) is not int: 
                    type_len       = len(lane_type_data)
                    lane_type      = argmax(lane_type_data, Lane_type[:type_len])
                else: lane_type = lane_type_data  # for ground_truth.txt visual
                if "camera_0_" in image_path or "fov30" in image_path:
                    break_points = line.get("break_points", [])
                    img = draw_points(img, break_points, line.get("bk_scores", []))
                    img = draw_lines(img, points, line["confidence"], COLORS[lane_type], lane_type,\
                        off_w=100, off_h=60)
                else:
                    img = draw_lines(img, points, line["confidence"], COLORS[lane_type], lane_type)
            else:
                if "camera_2_" in image_path or "fov120" in image_path:
                    img = draw_lines_roadside(img, line["key_points"], (0, 0, 255))
    subfolder = image_path.split('/')[-2]
    if "jira-XJ3" in image_path.split('/')[-3]:
        subfolder = image_path.split('/')[-3] + "_" + image_path.split('/')[-2]
    if subfolder in ["center_camera_fov120","center_camera_fov30","center_camera_fov30#s3"]:
        subfolder=image_path.split('/')[-3] + "_" + image_path.split('/')[-2]
    out_path = os.path.join(args.outdir, subfolder, os.path.basename(image_path))
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    im_h, im_w = img.shape[:2]
    if im_h > 1080: img = cv2.resize(img, (int(im_w/2), int(im_h/2)))
    quality = 85
    cv2.imwrite(out_path, img, [cv2.IMWRITE_JPEG_QUALITY, quality])
    return 1

def parse_result_tbxu(result_file, img_path_list):
    if isinstance(result_file, str): res_lines = open(result_file, 'r').readlines()
    data = {}
    for image_path in img_path_list: data.setdefault(image_path, [])
    for line in res_lines:
        line = json.loads(line)
        image_path = os.path.join(line["img_prefix"], line["image_path"])
        if image_path in img_path_list: data[image_path].append(line)            
    return data
    
def parse_result(result_files):
    if isinstance(result_files, str):
        result_files = [result_files]
    data = {}
    for res_file in result_files:
        for line in open(res_file):
            line = json.loads(line)
            #image_path = line["image_path"]
            image_path = os.path.join(line["img_prefix"], line["image_path"])
            data.setdefault(image_path, [])
            data[image_path].append(line)
    return data
    
def remove_duplicate(anno_lines, img_path_list):
    new_anno_lines, new_img_lists = [], []
    for i, img_path in enumerate(img_path_list):
        if img_path not in new_img_lists:
            new_img_lists.append(img_path)
            new_anno_lines.append(anno_lines[i])
    return new_anno_lines, new_img_lists

if __name__ == "__main__":
    args = parse_args()
    data = parse_result(args.result_files)
    # client = Client(args.client_conf)
    ##
    # ori_lane_json_path = os.path.join(os.path.dirname(args.result_files[0]),"../../",args.ori_json_file)
    # anno_lines = open(ori_lane_json_path, 'r').readlines()
    # img_path_list = []
    # for i in range(len(anno_lines)):
        # img_filepath = (json.loads(anno_lines[i]))['filename']
        # img_path_list.append(img_filepath)
    # anno_lines, img_path_list = remove_duplicate(anno_lines, img_path_list)
    # data = parse_result_tbxu(args.result_files[0], img_path_list)
    # assert len(data.items())==len(img_path_list), "img_num not equal"
    # 
    print("video has {} items.".format(len(data)))
    new_list = [(key, val) for key, val in data.items()]
    vis_result_pool(new_list[0])
    ####
    pool = Pool(args.workers)
    results = list(tqdm.tqdm(pool.imap(vis_result_pool, new_list), total=len(new_list)))
    pool.close()
    pool.join()
