### To compute performance metrics for ONNX model:
1. Put the onnx model in `./onnx_models` dir
2. Open `config.yaml` and update 'path_onnx'
3. Put the ground_truth.txt file in `./eval_files` dir
4. Run `main.py`

### To get visual outputs from the ONNX model:
1. Open `visualize_onnx_results.py` and update test images dir path
2. Run `visualize_onnx_results.py`
3. A new directory "frames_output_{some_random_nums} will be created which will contain all the visual outputs by the onnx model