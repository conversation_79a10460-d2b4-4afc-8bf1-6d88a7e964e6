from modulefinder import Module
import warnings

import torch
import torch.nn as nn
from mmcv.runner import BaseModule

from ..builder import MODELS

warnings.filterwarnings('ignore')


class BasicBlock3x3(BaseModule):

    def __init__(self, in_planes, out_planes, stride=1, dilation=1):
        super(BasicBlock3x3, self).__init__()
        # if dilation == 1:
        #     self.conv1 = nn.Conv2d(in_planes, out_planes, kernel_size=3, \
        #                   stride=stride, padding=1, bias=False)
        # elif dilation == 2:
        #     self.conv1 = nn.Conv2d(in_planes, out_planes, kernel_size=3, \
        #                   stride=1, padding=2, dilation=2, bias=False)
        self.conv1 = nn.Conv2d(
            in_planes,
            out_planes,
            kernel_size=3,
            stride=stride,
            padding=1,
            bias=False)
        self.bn1 = torch.nn.BatchNorm2d(out_planes)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        return out


class BasicBlock1x1(BaseModule):

    def __init__(self, in_planes, out_planes):
        super(BasicBlock1x1, self).__init__()
        self.conv1 = nn.Conv2d(
            in_planes,
            out_planes,
            kernel_size=1,
            stride=1,
            padding=0,
            bias=False)
        self.bn1 = torch.nn.BatchNorm2d(out_planes)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        return out


class Bottleneck(BaseModule):

    def __init__(self,
                 in_planes,
                 stride=1,
                 dilation=1,
                 keep=False,
                 downsample=None):
        super(Bottleneck, self).__init__()
        if keep:
            out_planes = in_planes
        else:
            out_planes = in_planes // 2
        self.block1 = BasicBlock1x1(in_planes, out_planes)
        self.block2 = BasicBlock3x3(out_planes, out_planes // 2)
        self.block3 = BasicBlock3x3(out_planes // 2, out_planes // 4)
        self.block4 = BasicBlock3x3(out_planes // 4, out_planes // 4)
        self.downsample = downsample

    def forward(self, x):
        if self.downsample:
            x = self.downsample(x)

        out1 = self.block1(x)
        out2 = self.block2(out1)
        out3 = self.block3(out2)
        out4 = self.block4(out3)
        out = torch.cat([out1, out2, out3, out4], 1)
        return out


block_dict = {
    'Bottleneck': Bottleneck,
}


@MODELS.register_module()
class JnetOri(BaseModule):

    def __init__(self,
                 layers,
                 out_channels,
                 block='Bottleneck',
                 init_cfg=None):
        super().__init__(init_cfg=init_cfg)
        
        block = block_dict[block]
        self.conv1 = nn.Conv2d(
            3, 32, kernel_size=5, stride=2, padding=2, bias=False)
        self.bn1 = torch.nn.BatchNorm2d(32)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(
            kernel_size=2, stride=2, padding=0, ceil_mode=True)
        self.avgpool = nn.AvgPool2d(7, stride=1)
        self.layer1 = self._make_layer(block, 32, layers[0])
        self.layer2 = self._make_layer(block, 64, layers[1])
        self.layer3 = self._make_layer(block, 128, layers[2])

        # deconv params
        self.last_channel = 256
        self.num_deconv_layers = 2
        # self.num_deconv_filters = [256, 256]
        self.num_deconv_filters = [64, 64]
        self.num_deconv_kernels = [4, 4]
        self.deconv_with_bias = False

        # used for deconv layers
        self.deconv_layers = self._make_deconv_layer(self.num_deconv_layers,
                                                     self.num_deconv_filters,
                                                     self.num_deconv_kernels)

        # self.final_layer = nn.Conv2d(in_channels=256,\
        #                out_channels=out_channels,\
        #                kernel_size=1, stride=1, padding=0)
        self.final_layer = nn.Conv2d(
            in_channels=64,
            out_channels=out_channels,
            kernel_size=1,
            stride=1,
            padding=0)

    def _make_layer(self, block, in_planes, blocks, keep=False):
        layers = []
        out_planes = in_planes * 2
        downsample = nn.Sequential(
            nn.Conv2d(
                in_planes,
                out_planes,
                kernel_size=1,
                stride=1,
                padding=0,
                bias=False), torch.nn.BatchNorm2d(out_planes), self.relu,
            nn.MaxPool2d(kernel_size=2, stride=2, padding=0, ceil_mode=True))
        in_planes = out_planes
        layers.append(block(in_planes, downsample=downsample, keep=keep))
        if keep:
            in_planes = in_planes * 2
        for i in range(1, blocks):
            layers.append(block(in_planes, downsample=None))
        return nn.Sequential(*layers)

    def _make_deconv_layer(self, num_layers, num_filters, num_kernels):
        assert num_layers == len(
            num_filters
        ), 'ERROR: num_deconv_layers is different len(num_deconv_filters)'
        assert num_layers == len(
            num_kernels
        ), 'ERROR: num_deconv_layers is different len(num_deconv_kernels)'

        layers = []
        for i in range(num_layers):
            kernel, padding, output_padding = num_kernels[i], 1, 0
            planes = num_filters[i]

            layers.append(
                nn.ConvTranspose2d(
                    in_channels=self.last_channel,
                    out_channels=planes,
                    kernel_size=kernel,
                    stride=2,
                    padding=padding,
                    output_padding=output_padding,
                    bias=self.deconv_with_bias))
            layers.append(nn.BatchNorm2d(planes))
            layers.append(nn.ReLU(inplace=True))
            self.last_channel = planes

        return nn.Sequential(*layers)

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # nn.init.kaiming_normal_(m.weight, mode='fan_out',\
                #        nonlinearity='relu')
                nn.init.normal_(m.weight, std=0.001)
                for name, _ in m.named_parameters():
                    if name in ['bias']:
                        nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.ConvTranspose2d):
                nn.init.normal_(m.weight, std=0.001)
                for name, _ in m.named_parameters():
                    if name in ['bias']:
                        nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.ConvTranspose2d):
                nn.init.normal_(m.weight, std=0.001)
                if self.deconv_with_bias:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        c1 = self.relu(x)

        c2 = self.layer1(c1)
        c3 = self.layer2(c2)
        out = self.layer3(c3)

        # c5 = self.deconv_layers(c4)
        # out = self.final_layer(c5)
        # print(out.shape)
        return out
