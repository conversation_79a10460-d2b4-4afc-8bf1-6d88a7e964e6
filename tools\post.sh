#!/usr/bin/bash
set -x

PILLAR_HOME="$(dirname $0)/.."

python -m pip show pillar
if [ $( echo $? ) == 1 ]; then
    python -m pip uninstall -y pillar
    python -m pip install -e ${PILLAR_HOME} --global-option="--function=all"
fi

POST_TYPE=${1}
DEBUG=${DEBUG:-false}

if [ $DEBUG == true ]; then
    DEBUG="--debug"
else
    unset DEBUG
fi

case $POST_TYPE in
    tf)
        FUNCTION_NAME=${2}
        OTHER_ARGS=${@:3}

        export WORK_DIR=$PILLAR_HOME/work_dirs/${FUNCTION_NAME}

        pillar run ${DEBUG} convert_tf \
            --log-dir=${WORK_DIR} \
            --out-dir=$PILLAR_HOME/work_dirs/analysis/${FUNCTION_NAME} \
            ${OTHER_ARGS}
        ;;
    acc)
        FUNCTION_NAME=${2}
        DATA_LIST=${@:3}

        export WORK_DIR=$PILLAR_HOME/work_dirs/${FUNCTION_NAME}

        pillar run ${DEBUG} calculate_accuracy \
            $PILLAR_HOME/functions/${FUNCTION_NAME}/configs \
            --data-list $DATA_LIST
        ;;
    flops)
        CONFIG=${2}
        OTHER_ARGS=${@:3}

        if [ -z "$CONFIG" ]; then
            echo "config is required"
            exit 1
        fi

        CONFIG_PATH=${CONFIG#*functions/}
        FUNCTION_NAME=${CONFIG_PATH%%/*}
        CONFIG_FILE=${CONFIG##*/}
        CONFIG_STEM=${CONFIG_FILE%.*}
        export WORK_DIR=$PILLAR_HOME/work_dirs/$FUNCTION_NAME/$CONFIG_STEM

        pillar run ${DEBUG} get_flops \
            ${CONFIG} \
            ${OTHER_ARGS}
        ;;
    deploy)
        export LAUNCHER=${LAUNCHER:-slurm}
        export PARTITION=${PARTITION:-ad_rd}
        export GPUS=${GPUS:-1}
        export GPUS_PER_NODE=${GPUS_PER_NODE:-$((${GPUS}>8?8:${GPUS}))}
        export CPUS_PER_TASK=${CPUS_PER_TASK:-5}
        export SRUN_ARGS=${SRUN_ARGS:-""}

        CONFIG=${2}
        DEPLOY_CONFIG=${3}
        CHECKPOINT=${4}
        OTHER_ARGS=${@:5}

        if [ -z "$CONFIG" ]; then
            echo "config is required"
            exit 1
        fi

        if [ -z "$DEPLOY_CONFIG" ]; then
            echo "deploy config is required"
            exit 1
        fi

        CONFIG_PATH=${CONFIG#*functions/}
        FUNCTION_NAME=${CONFIG_PATH%%/*}
        CONFIG_FILE=${CONFIG##*/}
        CONFIG_STEM=${CONFIG_FILE%.*}
        export WORK_DIR=$PILLAR_HOME/work_dirs/$FUNCTION_NAME/$CONFIG_STEM/deploy
        
        pillar run ${DEBUG} deploy \
            ${CONFIG} \
            ${DEPLOY_CONFIG} \
            ${CHECKPOINT} \
            --output-dir=${WORK_DIR} \
            ${OTHER_ARGS}
        ;;
    *)
        echo "invalid post type"
        exit 1
        ;;
esac
