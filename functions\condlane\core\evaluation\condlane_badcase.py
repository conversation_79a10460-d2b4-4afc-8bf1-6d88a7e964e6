import os
import sys
import shutil
from collections import Counter

import ipdb
from petrel_client.client import Client

from .condlane_format import parse_results, group_results
from .condlane_metric import eval_parallel
from scripts.vis_result import vis_results


def mining_badcase(result, preds, gts):
    meta = result["meta"]
    image_path = meta["image_path"]
    pred = preds.get(image_path, [])
    gt = gts.get(image_path)
    pred_idx = set(range(len(pred)))
    gt_idx = set(range(len(gt)))
    for i, j, s in zip(meta["pred_idx"], meta["gt_idx"],
                       result["lanes_matches"]["iou_16_1"]["iou_16_1"]):
        if s >= 0.1:
            pred_idx.remove(i)
            gt_idx.remove(j)
    pred = [pred[i] for i in pred_idx]
    gt = [gt[j] for j in gt_idx]
    for _ in pred:
        _["badcase"] = "mistake"
        _["key_points"] = dict(
            points=[dict(x=x, y=y) for x, y in _["key_points"]])
        _["break_points"] = dict(
            points=[dict(x=x, y=y) for x, y in _["break_points"]])
    for _ in gt:
        _["badcase"] = "missing"
        _["key_points"] = dict(
            points=[dict(x=x, y=y) for x, y in _["key_points"]])
        _["break_points"] = dict(
            points=[dict(x=x, y=y) for x, y in _["break_points"]])
    return image_path, pred + gt


def mining_badcases(
    gt_path,
    pred_path,
    categories,
    n,
    types=["LANELINE", "ROADSIDE"],
):
    gt_path = parse_results(gt_path, "gt")
    pred_path = parse_results(pred_path, "pred")
    gts = group_results(gt_path)
    preds = group_results(pred_path)
    results_dict = eval_parallel(gts, preds, categories, n, types)

    badcases = {}
    for k in types:
        for _ in results_dict[k]:
            image_path, badcase = mining_badcase(_, preds[k], gts[k])
            if len(badcase) > 0:
                badcases.setdefault(image_path, [])
                badcases[image_path].extend(badcase)
    return badcases


def diff_cases(cases, refs):
    keys = ["label", "badcase"]
    imgs = set([*cases.keys(), *refs.keys()])
    for img in imgs:
        if img not in cases:
            continue
        if img not in refs:
            continue
        case = [tuple([line[k] for k in keys]) for line in cases[img]]
        case = Counter(case)
        ref = [tuple([line[k] for k in keys]) for line in refs[img]]
        ref = Counter(ref)
        ks = set([*case.keys(), *ref.keys()])
        consistent = True
        for k in ks:
            if k not in case:
                consistent = False
                break
            if k not in ref:
                consistent = False
                break
            if case[k] != ref[k]:
                consistent = False
                break
            consistent = True
        if consistent:
            cases.pop(img)
            refs.pop(img)


categories = {
    "lane_type": [
        "NO_LANE",
        "OTHER_LANE_TYPE",
        "SOLID_LANE",
        "DASHED_LANE",
        "SHORT_THICK_DASHED_LANE",
        "SPLIT_LANE",
        "MERGE_LANE",
        "LEFT_DASHED_RIGHT_SOLID",
        "LEFT_SOLID_RIGHT_DASHED",
        "DOUBLE_SOLID",
        "DOUBLE_DASHED",
        "FISHBONE_SOLID",
        "FISHBONE_DASHED",
    ],
    "lane_color": [
        "NO_LANE",
        "OTHER_LANE_COLOR",
        "WHITE",
        "YELLOW",
        "ORANGE",
        "BLUE",
    ],
    "roadside": [
        "NO_SIDE",
        "ROAD_CURB_EDGE",
        "FENCE_EDGE",
        "WALL_EDGE_OR_PLANT_EDGE",
        "MOVABLE_EDGE",
        "OTHER_EDGE",
    ],
}
client = Client("/mnt/lustre/share_data/wanzhihe/lane_data/petreloss_lane.conf")
n = 32
types = sys.argv[5:]

badcases = mining_badcases(sys.argv[1], sys.argv[2], categories, n, types)
if os.path.exists(sys.argv[3]):
    refcases = mining_badcases(sys.argv[3], sys.argv[4], categories, n, types)
    diff_cases(badcases, refcases)
    if os.path.exists("vis_1"):
        shutil.rmtree("vis_1")
    vis_results(refcases, client, outdir="vis_1", num=n)

if os.path.exists("vis_0"):
    shutil.rmtree("vis_0")
vis_results(badcases, client, outdir="vis_0", num=n)
with open("badcases.txt", "w") as f:
    keys = list(badcases.keys())
    if os.path.exists(sys.argv[3]):
        keys = keys + list(refcases.keys())
        keys = list(set(keys))
    keys.sort()
    for k in keys:
        f.write(k + "\n")
