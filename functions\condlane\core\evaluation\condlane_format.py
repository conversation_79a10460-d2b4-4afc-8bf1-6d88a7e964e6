import os
import json
from copy import deepcopy

import pickle
import numpy as np
from scipy.interpolate import interp1d
from prettytable import PrettyTable, MSWORD_FRIENDLY


def curve_interp(points):
    n = len(points)
    if points[0, 1] < points[-1, 1]:
        points = points[::-1]
    t = np.arange(n)
    fx = interp1d(t, points[:, 0])
    fy = interp1d(t, points[:, 1])
    length = np.linalg.norm(points[:-1] - points[1:], 2, -1).sum()
    m = int(length // 8)
    if m <= n:
        return points
    m = min(m, 72)
    t = np.linspace(0, n - 1, m)
    points = np.stack([fx(t), fy(t)], -1)
    return points


def parse_results(filepath, pkl_key="gt"):
    #print("==== INSIDE PARSE_RESULTS filepath(gt_path) is ==========: ", filepath)
    if isinstance(filepath, str):
        #print("===== INSIDE IF ISINSTANCE(FILEPATH, STR) =======")
        if filepath.endswith(".pkl"):
            #print("====== INSIDE IF FILEPATH.ENDSWITH.PKL ========")
            pkl = pickle.load(open(filepath, "rb"))
            lines = []
            for _ in pkl:
                lines.extend(_[pkl_key])
        else:
            #print("======= INSIDE LINES= [JSON.LOADS] ==========")
            lines = [json.loads(_) for _ in open(filepath)]
    else:
        #print("======== INSIDE LINES=FILEPATH ======")
        lines = filepath
        #print("====== LINES IS =====: ", lines)
    return lines


def group_results(lines):
    results = {}
    for data in lines:
        data = deepcopy(data)
        key = data.get("image_path", data.get("id", None))
        assert key is not None

        points = data["key_points"]["points"]
        if len(points) <= 1:
            continue
        points = np.array([[_["x"], _["y"]] for _ in points])
        points = curve_interp(points)
        data["key_points"] = points

        breakpoints = data.get("break_points", {}).get("points", [])
        breakpoints = [[_["x"], _["y"]] for _ in breakpoints]
        data["break_points"] = breakpoints

        label = data["label"]
        results.setdefault(label, {})
        results[label].setdefault(key, [])
        results[label][key].append(data)
    return results


def collect(results):
    if len(results) <= 0:
        return results
    if isinstance(results[0], dict):
        ret = {}
        for result in results:
            for k, v, in result.items():
                ret.setdefault(k, [])
                ret[k].append(v)
        ret = {k: collect(v) for k, v in ret.items()}
        return ret
    if isinstance(results[0], list):
        ret = []
        for result in results:
            ret.extend(result)
        return ret
    if isinstance(results[0], (str, np.ndarray)):
        return results
    return sum(results)


def format_dict(data, table_name=""):
    if len(data) <= 0:
        return f"{table_name}: NO PREDICTION OR GROUNDTRUTH !"
    table = PrettyTable()
    table.set_style(MSWORD_FRIENDLY)
    rows = []
    for row_name, row in data.items():
        rows.append([row_name] + ["%.6g" % _ for _ in row.values()])
    table.field_names = [table_name, *row.keys()]
    table.add_row(["-"] * len(table.field_names))
    for row in rows:
        table.add_row(row)
    return str(table)


def format_list(data, prec, rec, table_name=""):
    if len(data) <= 0:
        return f"{table_name}: NO PREDICTION OR GROUNDTRUTH !"
    table = PrettyTable()
    table.set_style(MSWORD_FRIENDLY)
    rows = []
    for r, row in enumerate(data):
        rows.append([r, "%.3g" % prec[r], "%.3g" % rec[r], *row])
    table.field_names = [table_name, "prec", "rec", *range(len(row))]
    table.add_row(["-"] * len(table.field_names))
    for row in rows:
        table.add_row(row)
    return str(table)
