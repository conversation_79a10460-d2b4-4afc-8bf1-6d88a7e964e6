import os
import re
import sys
import shutil
import warnings
import argparse
from setuptools import find_packages, setup
import torch
from torch.utils.cpp_extension import (BuildExtension, CppExtension,
                                       CUDAExtension)


REPO_PATH = os.path.relpath(os.path.realpath(
    os.path.dirname(__file__)), os.getcwd())

FUNCTION_LIST = list(filter(lambda func_name: not func_name.startswith(
    '__'), os.listdir(f'{REPO_PATH}/functions'))) + ['all']

DEFAULT_VERSION = '0.0.0'


def readme():
    """Parse readme content"""
    content_file = os.path.join(REPO_PATH, 'README.md')
    if not os.path.isfile(content_file):
        warnings.warn(f'README file {content_file} does not exist')
        return None
    with open(content_file, 'r', encoding='utf-8') as file:
        content = file.read()
    return content


def parse_args():
    """Parse arguments for pillar"""
    parser = argparse.ArgumentParser(
        description='setup pillar with specific function')
    parser.add_argument(
        '--function', choices=FUNCTION_LIST, help='function to setup')

    return parser.parse_known_args()


def add_plugins(func_name, build_args, overwrite=True):
    """Add plugins into the package.

    These files will be added by creating a symlink to the originals if the
    package is installed in `editable` mode (e.g. pip install -e .), or by
    copying from the originals otherwise.
    """
    cmpnt_list = ['tools', 'configs']

    # parse installment mode
    if 'develop' in build_args:
        # installed by `pip install -e .`
        mode = 'symlink'
    elif 'sdist' in build_args or 'bdist_wheel' in build_args:
        # installed by `pip install .`
        # or create source distribution by `python setup.py sdist`
        mode = 'copy'
    else:
        return cmpnt_list

    plugin_path = f'{REPO_PATH}/pillar/.plugin'
    os.makedirs(plugin_path, exist_ok=True)

    if func_name == 'all':
        cmpnt_list.append('functions')
    elif func_name is not None:
        cmpnt_list.append(f'functions/{func_name}')

    for cmpnt_name in cmpnt_list:
        src_path = os.path.join(REPO_PATH, cmpnt_name)
        if not os.path.exists(src_path):
            warnings.warn(f'Component path {src_path} does not exist')
            continue
        dst_path = os.path.join(plugin_path, cmpnt_name)

        if overwrite:
            if not os.path.exists(dst_path):
                pass
            elif os.path.islink(dst_path):
                os.remove(dst_path)
            elif 'functions/' in cmpnt_name:
                parent_path = dst_path.rsplit('/', 1)[0]
                _ = os.remove(parent_path) if os.path.islink(
                    parent_path) else shutil.rmtree(parent_path)
            elif os.path.isdir(dst_path):
                shutil.rmtree(dst_path)
        if os.path.exists(dst_path):
            warnings.warn(f'Destination path {dst_path} has existed')

        dst_dir = os.path.dirname(dst_path)
        if not os.path.exists(dst_dir):
            os.makedirs(dst_dir)

        if mode == 'symlink':
            src_relpath = os.path.relpath(src_path, dst_dir)
            os.symlink(src_relpath, dst_path)
        elif mode == 'copy':
            if os.path.isfile(src_path):
                shutil.copyfile(src_path, dst_path)
            elif os.path.isdir(src_path):
                shutil.copytree(src_path, dst_path)
            else:
                warnings.warn(f'Cannot copy file {src_path}')
        else:
            raise ValueError(f'Invalid mode {mode}')
    return cmpnt_list


def parse_version_info(version_file):
    """Parse version information for version file"""
    if not os.path.isfile(version_file):
        warnings.warn(
            f'version file {version_file} does not exist and '
            f'default version {DEFAULT_VERSION} is employed')
        return DEFAULT_VERSION
    with open(version_file, 'r', encoding='utf-8') as file:
        exec(compile(file.read(), version_file, 'exec'))
    version = locals().get('__version__', None)
    if version is None:
        warnings.warn(
            f'__version__ is not contained by version file and '
            f'default version {DEFAULT_VERSION} is writen into {version_file}')
        version = DEFAULT_VERSION
        with open(version_file, 'a+', encoding='utf-8') as file:
            file.write(f'__version__ = \'{version}\'\n')
    assert isinstance(version, str), 'invalid version format'
    return version


def get_version(func_name=None):
    """Get version information from version files"""
    pillar_verfile = f'{REPO_PATH}/pillar/version.py'
    version = parse_version_info(pillar_verfile)
    if func_name in [None, 'all']:
        return version
    func_verfile = f'{REPO_PATH}/functions/{func_name}/version.py'
    func_version = parse_version_info(func_verfile)
    version = f'{version}+{func_name}.{func_version}'
    return version


def parse_requirements(fname='requirements.txt', with_version=True):
    """Parse the package dependencies listed in a requirements file but strips
    specific versioning information.

    Args:
        fname (str): path to requirements file
        with_version (bool, default=False): if True include version specs

    Returns:
        List[str]: list of requirements items

    CommandLine:
        python -c "import setup; print(setup.parse_requirements())"
    """
    require_fpath = fname

    def parse_line(line):
        """Parse information from a line in a requirements text file."""
        if line.startswith('-r '):
            # Allow specifying requirements in other files
            target = line.split(' ')[1]
            for info in parse_require_file(target):
                yield info
        else:
            info = dict(line=line)
            if line.startswith('-e '):
                info['package'] = line.split('#egg=')[1]
            elif '@git+' in line:
                info['package'] = line
            else:
                # Remove versioning from the package
                pat = '(' + '|'.join(['>=', '==', '>']) + ')'
                parts = re.split(pat, line, maxsplit=1)
                parts = [p.strip() for p in parts]

                info['package'] = parts[0]
                if len(parts) > 1:
                    op, rest = parts[1:]
                    if ';' in rest:
                        # Handle platform specific dependencies
                        # http://setuptools.readthedocs.io/en/latest/setuptools.html#declaring-platform-specific-dependencies
                        version, platform_deps = map(str.strip,
                                                     rest.split(';'))
                        info['platform_deps'] = platform_deps
                    else:
                        version = rest  # NOQA
                    info['version'] = (op, version)
            yield info

    def parse_require_file(fpath):
        with open(fpath, 'r', encoding='utf-8') as file:
            for line in file.readlines():
                line = line.strip()
                if line and not line.startswith('#'):
                    for info in parse_line(line):
                        yield info

    def gen_packages_items():
        if os.path.exists(require_fpath):
            for info in parse_require_file(require_fpath):
                parts = [info['package']]
                if with_version and 'version' in info:
                    parts.extend(info['version'])
                if not sys.version.startswith('3.4'):
                    # apparently package_deps are broken in 3.4
                    platform_deps = info.get('platform_deps')
                    if platform_deps is not None:
                        parts.append(';' + platform_deps)
                item = ''.join(parts)
                yield item

    packages = list(gen_packages_items())
    return packages

def make_cuda_exts(function):
    if function is None:
        return []

    extensions = []
    functions_paths = []

    # get function path
    if function == 'all':
        functions_path = os.path.join(os.getcwd(), 'functions')
        function_names = filter(lambda fp: os.path.isdir(os.path.join(functions_path, fp)), os.listdir(functions_path))
        functions_paths = map(lambda fp: os.path.join(functions_path, fp), function_names)
        functions_paths = list(functions_paths)
    else:
        function_path = os.path.join(os.getcwd(), 'functions', function)
        functions_paths = [function_path]

    # get ops path
    for func_pth in functions_paths:
        function_name = os.path.basename(func_pth)
        
        if not os.path.exists(os.path.join(func_pth, 'ops')):
            continue

        # get ops names
        ops_names = os.listdir(os.path.join(func_pth, 'ops'))

        # get all source files of ops
        for ops_name in ops_names:
            src_path = os.path.join(func_pth, 'ops', ops_name, 'src')
            if not os.path.exists(src_path):
                continue

            src_file_paths = list(map(lambda x: 'src/' + x, os.listdir(src_path)))

            # create extension by make_cuda_ext
            extension = make_cuda_ext(
                name=ops_name+"_ext",
                module=f"functions.{function_name}.ops.{ops_name}",
                sources=src_file_paths,
            )

            extensions.append(extension)
    return extensions

def make_cuda_ext(name,
                  module,
                  sources,
                  sources_cuda=[],
                  extra_args=[],
                  extra_include_path=[]):
    #extra_args = ['-std=c++14']
    define_macros = []
    extra_compile_args = {'cxx': [] + extra_args}

    if torch.cuda.is_available() or os.getenv('FORCE_CUDA', '0') == '1':
        define_macros += [('WITH_CUDA', None)]
        extension = CUDAExtension
        extra_compile_args['nvcc'] = extra_args + [
            '-D__CUDA_NO_HALF_OPERATORS__',
            '-D__CUDA_NO_HALF_CONVERSIONS__',
            '-D__CUDA_NO_HALF2_OPERATORS__',
        ]
        sources += sources_cuda
    else:
        print('Compiling {} without CUDA'.format(name))
        extension = CppExtension
        # raise EnvironmentError('CUDA is required to compile MMDetection!')

    return extension(
        name='{}.{}'.format(module, name),
        sources=[os.path.join(*module.split('.'), p) for p in sources],
        include_dirs=extra_include_path,
        define_macros=define_macros,
        extra_compile_args=extra_compile_args)

if __name__ == '__main__':
    args, setup_args = parse_args()
    if not setup_args:
        setup_args.append('bdist_wheel')  # default mode to setup

    cmpnts = add_plugins(args.function, setup_args)

    setup(
        name='pillar',
        version=get_version(args.function),
        description='AutoPillar Toolbox and Benchmark',
        long_description=readme(),
        long_description_content_type='text/markdown',
        packages=find_packages(exclude=cmpnts),
        include_package_data=True,
        python_requires='>=3.6',
        package_data={'functions.*.ops': ['*/*.so']},
        classifiers=[
            'Development Status :: 4 - Beta',
            'License :: OSI Approved :: Apache Software License',
            'Operating System :: OS Independent',
            'Programming Language :: Python :: 3',
            'Programming Language :: Python :: 3.6',
            'Programming Language :: Python :: 3.7',
            'Programming Language :: Python :: 3.8',
        ],
        license='Apache License 2.0',
        build_requires=parse_requirements(),
        ext_modules=make_cuda_exts(args.function),  # make_cuda_ext
        cmdclass={'build_ext': BuildExtension},
        zip_safe=False,
        entry_points='''
            [console_scripts]
            pillar=pillar.cli:cli
        ''',
        script_args=setup_args)
