from os import path as osp

import cv2
import numpy as np
# from petrel_client.client import Client

from pillar.datasets import PIPELINES
from .alaug import CameraRoi


def get_m(shape, perspective_trans):
    h, w = shape[0], shape[1]
    src_pts = [[w * perspective_trans[1] - 1, h * perspective_trans[0] - 1],
               [
                   w - w * perspective_trans[1] - 1,
                   h * perspective_trans[0] - 1
               ], [0 - w * perspective_trans[2], h - 1],
               [w - 1 + w * perspective_trans[2], h - 1]]
    dst_pts = [[0, 0], [w - 1, 0], [0, h - 1], [w - 1, h - 1]]
    m = cv2.getPerspectiveTransform(np.array(src_pts, dtype="float32"),
                                    np.array(dst_pts, dtype="float32"))
    bm = cv2.getPerspectiveTransform(np.array(dst_pts, dtype="float32"),
                                     np.array(src_pts, dtype="float32"))
    return [m, bm]


@PIPELINES.register_module()
class LoadLanes(object):

    def __init__(self,
                 ceph_cfg="",
                 roi_method="none",
                 perspective_trans=None,
                 default_pers_trans=[42 / 100, 40 / 100, 1.5],
                 test_mode=False):
        self.initialized = False
        self.ceph_cfg = ceph_cfg
        self.default_maps = {}
        self.camera_roi = CameraRoi(roi_method)
        self.perspective_trans = perspective_trans
        if self.perspective_trans is not None:
            self._init_trans_matrix()
        self.default_pers_trans = default_pers_trans
        self.test_mode = test_mode

    def __call__(self, results):
        img_info = results["img_info"]
        try:
            cam_idx = "fov_" + img_info["camera_index"].split('_')[-1].lstrip(
                'fov')
        except:
            print("Warning: No cam idx!")
            cam_idx = "fov_120"
        img_prefix = results["img_prefix"]

        def abspath(file):
            if len(file) <= 0:
                return ""
            if "s3://" in file or file.startswith("/"):
                # xxx:s3://bucket/relpath
                # /prefix/relpath
                if file.startswith("shlg:s3://shlg_GAC"):
                    file = file.replace("shlg:s3://shlg_GAC", "s3://sh30_GAC")
                if file.startswith("shlg:s3://shlg_rdpro2_datasets"):
                    file = file.replace("shlg:s3://shlg_rdpro2_datasets",
                                        "s3://sh30_rdpro2_datasets")
                return file
            if len(img_prefix) <= 0 or file.startswith(img_prefix):
                return file
            return osp.join(img_prefix, file)

        def relpath(file):
            if len(file) <= 0:
                return "", ""
            if "s3://" in file:
                # xxx:s3://bucket/relpath -> relpath
                prefix, file = file.split("s3://", 1)
                bucket, file = file.split("/", 1)
                prefix = f"{prefix}s3://{bucket}"
            else:
                prefix = img_prefix if len(img_prefix) > 0 else "/"
                # /prefix/relpath -> prefix/relpath
                # /prefix/relpath -> relpath
                file = osp.relpath(file, prefix)
            return prefix, file

        filename = abspath(img_info["filename"])
        img_prefix, sub_img_name = relpath(filename)

        img, m = self.im_load(filename, is_anno=False, cam_idx=cam_idx)
        assert type(img) is np.ndarray, "File not found " + filename

        laneline = abspath(img_info.get("laneline", ""))
        laneline, m_ = self.im_load(laneline, cam_idx=cam_idx)
        if type(laneline) is np.ndarray:
            laneline_type = abspath(img_info.get("laneline_type", ""))
            laneline_type, m_ = self.im_load(laneline_type, cam_idx=cam_idx)
            laneline_type[laneline_type == 13] = 2
            laneline_color = abspath(img_info.get("laneline_color", ""))
            laneline_color, m_ = self.im_load(laneline_color, cam_idx=cam_idx)
        else:
            laneline_type = 255
            laneline_color = 255

        roadside = abspath(img_info.get("roadside", ""))
        roadside, m_ = self.im_load(roadside, cam_idx=cam_idx)
        if type(roadside) is np.ndarray:
            sidetype = abspath(img_info.get("sidetype", ""))
            sidetype, m_ = self.im_load(sidetype, cam_idx=cam_idx)
        else:
            sidetype = 255
            
        depth_map = abspath(img_info.get("depth_map", ""))
#        print(f"depth_map is: {depth_map}")
        depth_map, m = self.im_load(depth_map, cam_idx=cam_idx)
        if not type(depth_map) is np.ndarray:
            depth_map = 255

        ori_shape = img.shape
        results = dict(
            filename=filename,
            img_prefix=img_prefix,
            sub_img_name=sub_img_name,
            img=img,
            img_shape=ori_shape,
            ori_shape=ori_shape,
            roi=img_info.get("roi", {}),
            bk_roi=img_info.get("bk_roi", {}),
            laneline_mask=laneline,
            laneline_label=laneline_type,
            laneline_color=laneline_color,
            roadside_mask=roadside,
            roadside_label=sidetype,
            depth_map=depth_map,
            break_points=img_info.get("break_points", []),
            scene=img_info.get("scene", []),
            camera_index=img_info.get("camera_index", ""),
            pers_trans_m=m,
        )
        results = self.camera_roi(results)
        return results

    def _init_trans_matrix(self, ):
        trans_dict = {}
        for f_k in self.perspective_trans:
            tmp_dict = {}
            for r_k in self.perspective_trans[f_k]:
                tmp_dict[r_k] = get_m(
                    [int(r_k.split('_')[2]),
                     int(r_k.split('_')[1])], self.perspective_trans[f_k][r_k])
                trans_dict[f_k] = tmp_dict
        self.perspective_trans = trans_dict

    def _init_memcached(self):
        if not self.initialized:
            # self.mclient = Client(self.ceph_cfg)
            self.mclient = None
            self.initialized = True

    def im_load(self,
                filename,
                mode=cv2.IMREAD_UNCHANGED,
                is_anno=True,
                cam_idx='fov_120'):
        m = 0
        if len(filename) <= 0 or "ignore" in filename:
            return 255, m
        if "zero" in filename:
            return 0, m
        if "s3://" in filename:
            self._init_memcached()
            value_buf = self.mclient.get(filename)
            assert value_buf is not None, f"not exists {filename}"
            img_array = np.frombuffer(value_buf, np.uint8)
            image = cv2.imdecode(img_array, mode)
        else:
            image = cv2.imread(filename, mode).astype(np.uint8)
        assert image is not None, f"not exists {filename}"

        if self.test_mode and is_anno:
            return image, m

        if self.perspective_trans is not None:
            h, w = image.shape[0], image.shape[1]
            r_k = 'r_' + str(w) + '_' + str(h)
            f_k = cam_idx
            if r_k in self.perspective_trans[f_k].keys():
                m = self.perspective_trans[f_k][r_k]
            else:
                m = get_m([h, w], self.default_pers_trans)
                self.perspective_trans[f_k][r_k] = m
            if is_anno:
                image = cv2.warpPerspective(image,
                                            m[0], (w, h),
                                            flags=cv2.INTER_NEAREST)
            else:
                image = cv2.warpPerspective(image,
                                            m[0], (w, h),
                                            flags=cv2.INTER_LINEAR)

        return image, m
