import copy
import os
import os.path as osp
import time
import re
import configparser

import torch
import mmcv
from mmcv import __version__ as mmcv_version
from mmcv import Config
from mmcv.cnn import fuse_conv_bn
from mmcv.runner import (get_dist_info, init_dist, load_checkpoint,
                         wrap_fp16_model)
from mmcv.utils import get_git_hash
import mme
from mme import __version__ as mme_version
from mme.apis import set_random_seed, train_model, multi_gpu_test, single_gpu_test
from mme.datasets import build_dataloader
from mme.models import build_algorithm, build_model
from mme.models.builder import build_model_wrapper
from mme.utils import collect_env, get_root_logger, setup_multi_processes, get_dataloader_cfg
import clearml

from pillar.datasets import build_dataset
from pillar.apis import init_model, concat_dataset, inference_model
from .base_backend import BaseAlgorithmBackend
from .builder import ALGORITHM_BACKEND
from .utils import generate_datalist, digit_version


@ALGORITHM_BACKEND.register_module()
class OpenMMLab(BaseAlgorithmBackend):
    def __init__(self, args):
        super().__init__(args)
        cfg = Config.fromfile(self.args.config)
        if self.args.cfg_options is not None:
            cfg.merge_from_dict(self.args.cfg_options)
        self.cfg = cfg

        setup_multi_processes(self.cfg)

        # import modules from string list.
        if self.cfg.get('custom_imports', None):
            from mmcv.utils import import_modules_from_strings
            import_modules_from_strings(**self.cfg['custom_imports'])
        # set cudnn_benchmark
        if self.cfg.get('cudnn_benchmark', False):
            torch.backends.cudnn.benchmark = True

        # init distributed env first, since logger depends on the dist info.
        if self.args.launcher == 'none':
            self.distributed = False
        else:
            self.distributed = True
            init_dist(self.args.launcher, **self.cfg.dist_params)
        
        self.rank, self.world_size = get_dist_info()

        if self.rank == 0:
            self._init_clearml()
            print(self.args)

        mme.utils.DEFAULT_SCOPE = self.cfg.get('default_scope', None)
    
    def _init_clearml(self):
        parser = configparser.ConfigParser()
        parser.read(osp.join(osp.dirname(__file__), '../client.conf'))
        clearml.Task.set_credentials(**parser['clearml'])
        try:
            api_host = parser['clearml']['api_host']
            backinfo = os.system(f'curl -m 1 -s -o /dev/null {api_host}')
            if backinfo:
                raise RuntimeError(f'clearml server is unreachable: {backinfo}')
        except Exception as e:
            print(f'connect clearml server error: {e}, turn off clearml logger hook')
            self._remove_clearml_logger_hook()
            return

        task_name = '.'.join(osp.basename(self.args.config).split('.')[:-1])
        task = clearml.Task.init(
            project_name=f'pillar/{self.args.function}',
            task_name=task_name,
            reuse_last_task_id=False,
            auto_connect_frameworks=False
        )
        task.connect(self.args, name='Args')
        task.connect_configuration(self.args.config, name='Configuration')
        self._add_clearml_logger_hook()

    def _add_clearml_logger_hook(self):
        if self.cfg.get('custom_hooks', None) is None:
            self.cfg.custom_hooks = []
        assert isinstance(self.cfg.custom_hooks, list), \
            f'custom_hooks expect list type, but got {type(self.cfg.custom_hooks)}'
        
        if not any(['ClearMLLoggerHook' in hook.type for hook in self.cfg.custom_hooks]):
            clearml_logger_hook = dict(
                type='pillar.ClearMLLoggerHook', 
                interval=100
            )
            self.cfg.custom_hooks.append(clearml_logger_hook)
    
    def _remove_clearml_logger_hook(self):
        if self.cfg.get('custom_hooks', None) is None:
            return
        assert isinstance(self.cfg.custom_hooks, list), \
            f'custom_hooks expect list type, but got {type(self.cfg.custom_hooks)}'

        hooks = []
        for hook in self.cfg.custom_hooks:
            if 'ClearMLLoggerHook' in hook.type:
                continue
            hooks.append(hook)

        self.cfg.custom_hooks = hooks

    def train(self):
        # work_dir is determined in this priority: CLI > segment in file > filename
        if self.args.work_dir is not None:
            # update configs according to CLI self.args if self.args.work_dir is not None
            self.cfg.work_dir = self.args.work_dir
        elif self.cfg.get('work_dir', None) is None:
            # use config filename as default work_dir if self.cfg.work_dir is None
            self.cfg.work_dir = osp.join('./work_dirs',
                                    osp.splitext(osp.basename(self.args.config))[0])
        
        if self.args.resume_from is not None:
            self.cfg.resume_from = self.args.resume_from
        
        if self.args.gpu_ids is not None:
            self.cfg.gpu_ids = self.args.gpu_ids
        else:
            self.cfg.gpu_ids = range(1) if self.args.gpus is None else range(self.args.gpus)
        
        if self.distributed:
            # re-set gpu_ids with distributed training mode
            self.cfg.gpu_ids = range(self.world_size)

        if self.rank == 0:
            task = clearml.Task.current_task()
            if task is not None:
                task.set_task_type(clearml.TaskTypes.training)

        # create work_dir
        mmcv.mkdir_or_exist(osp.abspath(self.cfg.work_dir))
        # dump config
        self.cfg.dump(osp.join(self.cfg.work_dir, osp.basename(self.args.config)))
        # init the logger before other steps
        timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        log_file = osp.join(self.cfg.work_dir, f'{timestamp}.log')
        logger = get_root_logger(log_file=log_file, log_level=self.cfg.log_level)

        # init the meta dict to record some important information such as
        # environment info and seed, which will be logged
        meta = dict()
        # log env info
        env_info_dict = collect_env()
        env_info = '\n'.join([(f'{k}: {v}') for k, v in env_info_dict.items()])
        dash_line = '-' * 60 + '\n'
        logger.info('Environment info:\n' + dash_line + env_info + '\n' +
                    dash_line)
        meta['env_info'] = env_info
        meta['config'] = self.cfg.pretty_text
        # log some basic info
        logger.info(f'Distributed training: {self.distributed}')
        logger.info(f'Config:\n{self.cfg.pretty_text}')

        # set random seeds
        if self.args.seed is not None:
            logger.info(f'Set random seed to {self.args.seed}, '
                        f'deterministic: {self.args.deterministic}')
            set_random_seed(self.args.seed, deterministic=self.args.deterministic)
        self.cfg.seed = self.args.seed
        meta['seed'] = self.args.seed
        meta['exp_name'] = osp.basename(self.args.config)

        datasets = [build_dataset(self.cfg.data.train)]
        if len(self.cfg.workflow) == 2:
            val_dataset = copy.deepcopy(self.cfg.data.val)
            # TODO: concat dataset has no pipeline
            val_dataset.pipeline = self.cfg.data.train.pipeline
            datasets.append(build_dataset(val_dataset))
        if self.cfg.checkpoint_config is not None:
            # save mme version, config file content and class names in
            # checkpoints as meta data
            self.cfg.checkpoint_config.meta = dict(mme_version=mme_version +
                                            get_git_hash()[:7])
            # TODO: use other methods to save meta info
            if self.cfg.test_setting['repo'] in ['mmcls', 'mmdet', 'mmseg']:
                self.cfg.checkpoint_config.meta['CLASSES'] = datasets[0].CLASSES
            if self.cfg.test_setting['repo'] == 'mmseg':
                self.cfg.checkpoint_config.meta['PALETTE'] = datasets[0].PALETTE

        if 'algorithm' in self.cfg:
            model = build_algorithm(self.cfg.algorithm)
        else:
            model = build_model(self.cfg.model)
        
        if self.args.sync_bn:
            model = torch.nn.SyncBatchNorm.convert_sync_batchnorm(model)
        
        model.init_weights()

        # TODO: use other methods to set CLASSES
        # add an attribute for visualization convenience
        if self.cfg.test_setting['repo'] in ['mmcls', 'mmseg', 'mmdet']:
            model.CLASSES = datasets[0].CLASSES
        train_model(
            model,
            datasets,
            self.cfg,
            distributed=self.distributed,
            validate=(not self.args.no_validate),
            timestamp=timestamp,
            meta=meta)

    def inference(self):
        if self.rank == 0:
            task = clearml.Task.current_task()
            if task is not None:
                task.set_task_type(clearml.TaskTypes.inference)
        mmcv.mkdir_or_exist(self.args.work_dir)

        # build the model and load checkpoint
        model = init_model(self.cfg, self.args.checkpoint, device=self.args.ckpt_device)
        model_wrapper_cfg = self.cfg.get('model_wrapper', dict())
        if not self.distributed:
            model_wrapper_cfg.setdefault('type', 'mmcv.MMDataParallel')
            model_wrapper_cfg.update(module=model, device_ids=[0])
            model = build_model_wrapper(model_wrapper_cfg)
        else:
            model_wrapper_cfg.setdefault('type', 'mmcv.MMDistributedDataParallel')
            model_wrapper_cfg.update(
                module=model.cuda(), device_ids=[torch.cuda.current_device()])
            model_wrapper_cfg.setdefault('broadcast_buffers', False)
            model = build_model_wrapper(model_wrapper_cfg)

        replace_petrel_conf_path = None
        if digit_version(mmcv_version) >= digit_version('1.7.1'):
            replace_petrel_conf_path = self.args.petrel_conf_path
        # generate data list
        data_list, anno_num = generate_datalist(self.args.data, self.args.img_root, replace_petrel_conf_path)

        # build the dataset
        dataset = concat_dataset(
            self.cfg, data_list, bool(anno_num), self.args.img_root, replace_petrel_conf_path)

        # build the dataloader
        batch_size = self.cfg.data.samples_per_gpu if self.args.batch_size is None else self.args.batch_size
        worker_num = self.cfg.data.workers_per_gpu if self.args.worker_num is None else self.args.worker_num
        data_loader = build_dataloader(
            dataset,
            samples_per_gpu=batch_size,
            workers_per_gpu=worker_num,
            dist=self.distributed,
            shuffle=False,
            round_up=True,
            pin_memory=self.args.pin_memory)
        
        # infer the model
        results = inference_model(model, data_loader, self.args.work_dir, self.args.function,
                                  post_process=self.args.post_process, show_results=self.args.show_results)

        if results is None:
            return None

        res_file = os.path.join(self.args.work_dir, 'inference_results.pkl')
        mmcv.dump(results, res_file)
        print(f'\nSaved inference results to file: {res_file}')

    def test(self):
        print("====================INSIDE BUILD OPNEMMLAB==================")
        assert self.args.out or self.args.eval or self.args.format_only, \
            ('Please specify at least one operation (save/eval/format the '
            'results / save the results) with the argument "--out", "--eval"'
            ', "--format-only"')

        if self.args.eval and self.args.format_only:
            raise ValueError(
                '--eval and --format_only cannot be both specified')

        if self.args.out is not None and not self.args.out.endswith(
            ('.pkl', '.pickle', '.json')):
            raise ValueError('The output file must be a pkl or json file.')

        if self.args.format_only:
            assert self.cfg.test_setting.repo in ['mmseg', 'mmdet'], \
                ('Only mmdet and mmseg support format_only! '
                f'But got {self.cfg.test_setting.repo}')
        if self.rank == 0:
            task = clearml.Task.current_task()
            if task is not None:
                task.set_task_type(clearml.TaskTypes.testing)

        # allows not to create
        if self.args.work_dir is not None and self.rank == 0:
            mmcv.mkdir_or_exist(osp.abspath(self.args.work_dir))
            timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            json_file = osp.join(self.args.work_dir, f'eval_{timestamp}.json')

        # build the dataloader
        dataset = build_dataset(self.cfg.data.test)
        dataloader_cfg = get_dataloader_cfg(self.cfg, 'test_loader')
        data_loader = build_dataloader(
            dataset, **dataloader_cfg, dist=self.distributed, shuffle=False)

        # build the model and load checkpoint
        self.cfg.model.train_cfg = None
        if self.cfg.model.get('pretrained') is not None:  # mmpose
            self.cfg.model.pretrained = None

        if 'algorithm' in self.cfg:
            model = build_algorithm(self.cfg.algorithm)
        else:
            model = build_model(self.cfg.model)

        fp16_cfg = self.cfg.get('fp16', None)
        if fp16_cfg is not None:
            wrap_fp16_model(model)
        checkpoint = load_checkpoint(model,
                                     self.args.checkpoint,
                                     map_location='cpu')
        if self.args.fuse_conv_bn:
            model = fuse_conv_bn(model)

        # Different repos save different meta info in checkpoint
        # TODO: use other method to replace this
        if self.cfg.test_setting.repo in ['mmcls', 'mmseg', 'mmdet']:
            if 'CLASSES' in checkpoint.get('meta', {}):
                model.CLASSES = checkpoint['meta']['CLASSES']
            else:
                model.CLASSES = getattr(dataset, 'CLASSES', None)

        if self.cfg.test_setting.repo == 'mmseg':
            if 'PALETTE' in checkpoint.get('meta', {}):
                model.PALETTE = checkpoint['meta']['PALETTE']
            else:
                print(
                    '"PALETTE" not found in meta, use dataset.PALETTE instead')
                model.PALETTE = dataset.PALETTE

        if self.args.load_file is not None:
            if not self.args.load_file.endswith(('.pkl', '.pickle')):
                raise ValueError('The infer result file must be a pkl file.')
            outputs = mmcv.load(self.args.load_file)
        else:
            model_wrapper_cfg = self.cfg.get('model_wrapper', dict())
            if not self.distributed:
                model_wrapper_cfg.setdefault('type', 'mmcv.MMDataParallel')
                model_wrapper_cfg.update(module=model, device_ids=[0])
                model = build_model_wrapper(model_wrapper_cfg)
                outputs = single_gpu_test(model, data_loader, self.cfg.test_setting)
            else:
                model_wrapper_cfg.setdefault('type', 'mmcv.MMDistributedDataParallel')
                model_wrapper_cfg.update(
                    module=model.cuda(), device_ids=[torch.cuda.current_device()])
                model_wrapper_cfg.setdefault('broadcast_buffers', False)
                model = build_model_wrapper(model_wrapper_cfg)
                outputs = multi_gpu_test(model, data_loader, self.cfg.test_setting)

        if self.rank == 0:
            if self.args.out:
                print(f'\nwriting results to {self.args.out}')
                mmcv.dump(outputs, self.args.out)
            eval_kwargs = {} if self.args.eval_options is None else self.args.eval_options
            #if self.args.format_only:
                #dataset.format_results(outputs, **eval_kwargs)
            if self.args.eval:
                # mmpose need res_folder arguments.
                # TODO: remove this afer using unified eval API.
                dataset.format_results(outputs, self.args.work_dir, **eval_kwargs)
                eval_kwargs.update(metric=self.args.eval)
                scope = self.cfg.data.test.type.split('.')[0]
                if self.cfg.test_setting.repo == 'mmpose' or scope == 'pillar':
                    assert self.args.work_dir is not None, \
                        'work_dir is required for mmpose or pillar evaluation '
                    eval_kwargs['res_folder'] = self.args.work_dir
                metric = dataset.evaluate(outputs, **eval_kwargs)
                print(f'\nEvaluation Metric:\n{metric}')
                metric_dict = dict(config=self.args.config, metric=metric)
                if self.args.work_dir is not None and self.rank == 0:
                    #mmcv.dump(metric_dict, json_file)
                    #mmcv.dump(metric_dict, json_file, indent=2)
                    with open(json_file, "w") as f:
                        f.write(self.args.config + "\n")
                        f.write(self.args.checkpoint + "\n")
                        f.write(metric)
