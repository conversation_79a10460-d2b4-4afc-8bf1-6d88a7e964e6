import math
import random
import collections

import cv2
import numpy as np
import albumentations as al

from pillar.datasets import PIPELINES


class RandomCenterCrop(al.RandomResizedCrop):

    def __init__(
        self,
        height,
        width,
        scale=(0.08, 1.0),
        ratio=(0.75, 1.3333333333333333),
        interpolation=cv2.INTER_LINEAR,
        always_apply=False,
        p=1.0,
    ):
        super(RandomCenterCrop, self).__init__(
            height=height,
            width=width,
            scale=scale,
            ratio=ratio,
            interpolation=interpolation,
            always_apply=always_apply,
            p=p,
        )

    def get_params_dependent_on_targets(self, params):
        img = params["image"]
        area = img.shape[0] * img.shape[1]

        for _attempt in range(10):
            target_area = random.uniform(*self.scale) * area
            log_ratio = (math.log(self.ratio[0]), math.log(self.ratio[1]))
            aspect_ratio = math.exp(random.uniform(*log_ratio))

            w = int(round(math.sqrt(target_area *
                                    aspect_ratio)))  # skipcq: PTC-W0028
            h = int(round(math.sqrt(target_area /
                                    aspect_ratio)))  # skipcq: PTC-W0028

            if 0 < w <= img.shape[1] and 0 < h <= img.shape[0]:
                i = (img.shape[0] - h) // 2
                j = (img.shape[1] - w) // 2
                return {
                    "crop_height": h,
                    "crop_width": w,
                    "h_start": i * 1.0 / (img.shape[0] - h + 1e-10),
                    "w_start": j * 1.0 / (img.shape[1] - w + 1e-10),
                }

        # Fallback to central crop
        in_ratio = img.shape[1] / img.shape[0]
        if in_ratio < min(self.ratio):
            w = img.shape[1]
            h = int(round(w / min(self.ratio)))
        elif in_ratio > max(self.ratio):
            h = img.shape[0]
            w = int(round(h * max(self.ratio)))
        else:  # whole image
            w = img.shape[1]
            h = img.shape[0]
        i = (img.shape[0] - h) // 2
        j = (img.shape[1] - w) // 2
        return {
            "crop_height": h,
            "crop_width": w,
            "h_start": i * 1.0 / (img.shape[0] - h + 1e-10),
            "w_start": j * 1.0 / (img.shape[1] - w + 1e-10),
        }


class HorizontalFlip(al.HorizontalFlip):
    """
    车道线"左虚右实"与"左实右虚"在flip之后需要交换label
    flip_kwargs = {1: [[7, 8]]}
    """

    def __init__(self, flip_kwargs={}, type="", **kwargs):
        super().__init__(**kwargs)
        self.flip_kwargs = flip_kwargs

    def flip_label(self, x: np.ndarray, a: int, b: int):
        y = np.where(x == a, b, x)
        z = np.where(x == b, a, y)
        return z

    def apply_with_params(self, params, force_apply: bool = False, **kwargs):
        res = super().apply_with_params(params, force_apply, **kwargs)
        for k, v in self.flip_kwargs.items():
            for a, b in v:
                res["masks"][k] = self.flip_label(res["masks"][k], a, b)
        return res

    def apply_to_keypoint(self, keypoint, rows, cols):
        x, y = keypoint[:2]
        return ((cols - 1) - x, y, *keypoint[2:])

class Perspective(al.Perspective):

    def __init__(self, point_params=None, **kwargs):
        super().__init__(**kwargs)
        if point_params is None:
            point_params = dict(
                tl=dict(x=0.0, y=0.0),
                tr=dict(x=1.0, y=0.0),
                bl=dict(x=0.0, y=1.0),
                br=dict(x=1.0, y=1.0),
            )
        self.point_params = point_params

    def get_params_from_points(self, params, points):
        tl, tr, br, bl = points

        # compute the width of the new image, which will be the
        # maximum distance between bottom-right and bottom-left
        # x-coordiates or the top-right and top-left x-coordinates
        min_width = None
        max_width = None
        while min_width is None or min_width < 2:
            width_top = np.sqrt(((tr[0] - tl[0])**2) + ((tr[1] - tl[1])**2))
            width_bottom = np.sqrt(((br[0] - bl[0])**2) + ((br[1] - bl[1])**2))
            max_width = int(max(width_top, width_bottom))
            min_width = int(min(width_top, width_bottom))
            if min_width < 2:
                step_size = (2 - min_width) / 2
                tl[0] -= step_size
                tr[0] += step_size
                bl[0] -= step_size
                br[0] += step_size

        # compute the height of the new image, which will be the maximum distance between the top-right
        # and bottom-right y-coordinates or the top-left and bottom-left y-coordinates
        min_height = None
        max_height = None
        while min_height is None or min_height < 2:
            height_right = np.sqrt(((tr[0] - br[0])**2) + ((tr[1] - br[1])**2))
            height_left = np.sqrt(((tl[0] - bl[0])**2) + ((tl[1] - bl[1])**2))
            max_height = int(max(height_right, height_left))
            min_height = int(min(height_right, height_left))
            if min_height < 2:
                step_size = (2 - min_height) / 2
                tl[1] -= step_size
                tr[1] -= step_size
                bl[1] += step_size
                br[1] += step_size

        # now that we have the dimensions of the new image, construct
        # the set of destination points to obtain a "birds eye view",
        # (i.e. top-down view) of the image, again specifying points
        # in the top-left, top-right, bottom-right, and bottom-left order
        # do not use width-1 or height-1 here, as for e.g. width=3, height=2
        # the bottom right coordinate is at (3.0, 2.0) and not (2.0, 1.0)
        dst = np.array(
            [[0, 0], [max_width, 0], [max_width, max_height], [0, max_height]],
            dtype=np.float32)

        # compute the perspective transform matrix and then apply it
        m = cv2.getPerspectiveTransform(points, dst)

        if self.fit_output:
            m, max_width, max_height = self._expand_transform(m, (h, w))

        return {
            "matrix": m,
            "max_height": max_height,
            "max_width": max_width,
            "interpolation": self.interpolation,
        }

    def get_params_dependent_on_targets(self, params):
        point_params = self.point_params
        point_params = {
            q: {
                k: np.random.uniform(*v) if isinstance(v, (list, tuple)) else v
                for k, v in xy.items()
            }
            for q, xy in point_params.items()
        }
        h, w = params["image"].shape[:2]
        points = {
            q: [xy["x"] * w, xy["y"] * h]
            for q, xy in point_params.items()
        }
        points = [points[q] for q in ["tl", "tr", "br", "bl"]]
        points = np.array(points, dtype=np.float32)
        return self.get_params_from_points(params, points)


@PIPELINES.register_module()
class Mirror:

    def __init__(
        self,
        types=["mirror", "blur"],
        scale=0.5,
        p=0.5,
    ):
        assert 0 <= scale <= 0.5
        assert 0 <= p <= 1
        self.types = types
        self.scale = scale
        self.p = p

    def lane_mask(self, mask):
        H, W = mask.shape
        left = np.ones(H, dtype=np.int) * (W - 1)
        right = np.zeros(H, dtype=np.int)
        for i in range(H):
            xs = np.where(mask[i])[0]
            if len(xs) <= 0:
                continue
            left[i] = min(xs.min(), left[i])
            right[i] = max(xs.max(), right[i])
        left_post = left + 0
        right_post = right + 0
        for i in range(H - 2, -1, -1):
            left_post[i] = min(left_post[i + 1], left_post[i])
            right_post[i] = max(right_post[i + 1], right_post[i])
        left_prev = left + 0
        right_prev = right + 0
        for i in range(1, H):
            left_prev[i] = min(left_prev[i - 1], left_prev[i])
            right_prev[i] = max(right_prev[i - 1], right_prev[i])
        left = np.maximum(left_prev, left_post)
        right = np.minimum(right_prev, right_post)
        for i in range(1, H):
            left[i] = min(left[i - 1], left[i])
            right[i] = max(right[i - 1], right[i])
        new_mask = np.zeros_like(mask)
        for i in range(H):
            if left[i] >= right[i]:
                continue
            new_mask[i][left[i]:right[i] + 1] = True
        return new_mask

    def mirror(self, img, mask):
        H, W = mask.shape
        y_min = np.where(mask)[0].min()
        y_min = max(y_min, 1)
        default = img[:y_min].reshape(y_min * W, -1).mean(0).astype(img.dtype)
        new_img = np.ones_like(img) * default
        for j in range(W):
            ys = np.where(mask[:, j])[0]
            if len(ys) <= 0:
                continue
            i = ys.min()
            l = min(i, H - i)
            ys = np.arange(i, i + l)
            y_ = np.arange(i - 1, i - 1 - l, -1)
            new_img[ys, j] = img[y_, j]
        new_img[~mask] = default
        return new_img

    def blur(self, img, mask):
        default = img[mask].mean().astype(img.dtype)
        new_img = np.ones_like(img) * default
        new_img[mask] = default
        return new_img

    def mixup(self, img, mirror, mask, s=0.5):
        img = img.astype(np.float32)
        mirror = mirror.astype(np.float32)
        img[mask] = img[mask] * (1 - s) + mirror[mask] * s
        img = img.astype(np.uint8)
        return img

    def __call__(self, data):
        if np.random.rand() >= self.p:
            return data
        laneline_mask = data["laneline_mask"] > 0
        roadside_mask = data["roadside_mask"] > 0
        valid_laneline = type(
            laneline_mask) is np.ndarray and laneline_mask.any()
        valid_roadside = type(
            roadside_mask) is np.ndarray and roadside_mask.any()
        if valid_laneline:
            if valid_roadside:
                mask = laneline_mask | roadside_mask
            else:
                mask = laneline_mask
        else:
            if valid_roadside:
                mask = roadside_mask
            else:
                return data
        mask = self.lane_mask(mask)
        if not mask.any():
            return data
        img = data["img"]
        fn_type = self.types[np.random.choice(len(self.types))]
        new_img = getattr(self, fn_type)(img, mask)
        img = self.mixup(img, new_img, mask, np.random.uniform(0, self.scale))
        data["img"] = img
        return data


# @PIPELINES.register_module()
class CameraRoi:

    def __init__(self, method="none"):
        assert method in ["none", "crop", "mask"]
        self.method = method

    def parse_roi(self, W, H, l=0.0, t=0.0, r=1.0, b=1.0):
        l = W * l if l <= 1 else l
        t = H * t if t <= 1 else t
        r = W * r if r <= 1 else r
        b = H * b if b <= 1 else b
        l, t, r, b = map(int, [l, t, r, b])
        assert 0 <= l and r <= W
        assert 0 <= t and b <= H
        return l, t, r, b

    def fmt_roi(self, l, t, r, b):
        return {
            "top": t,
            "left": l,
            "height": b - t,
            "width": r - l,
            "#keson_code": "RCT",
        }

    def resize(self, **kwargs):
        H, W = kwargs["image"].shape[:2]
        aug = al.Compose(
            [al.Resize(H, W, cv2.INTER_NEAREST)],
            keypoint_params=al.KeypointParams(format="xy"),
        )
        return aug(**kwargs)

    def roi_crop(self, roi, **kwargs):
        l, t, r, b = roi
        aug = al.Compose(
            [al.Crop(x_min=l, y_min=t, x_max=r, y_max=b)],
            keypoint_params=al.KeypointParams(format="xy"),
        )
        return aug(**kwargs)

    def roi_mask(self, roi, **kwargs):
        roi_mask = np.ones(kwargs["image"].shape[:2], dtype=np.bool)
        l, t, r, b = roi
        roi_mask[t:b, l:r] = False
        for m in kwargs.get("masks", []):
            m[roi_mask] = 0
        kwargs["keypoints"] = self.roi_filter(kwargs.get("keypoints", []), roi)
        return kwargs

    def roi_filter(self, kps, roi):
        l, t, r, b = roi
        return [(x, y) for x, y in kps if l <= x <= r and t <= y <= b]

    def aug(self, roi, **kwargs):
        kwargs = self.resize(**kwargs)
        if self.method == "mask":
            kwargs = self.roi_mask(roi=roi, **kwargs)
        if self.method == "crop":
            kwargs = self.roi_crop(roi=roi, **kwargs)
        return kwargs

    def __call__(self, data):
        H, W = data["img"].shape[:2]
        bk_roi = self.parse_roi(W, H, **data["bk_roi"])
        data["break_points"] = self.roi_filter(data.get("break_points", []),
                                               bk_roi)
        data["bk_roi"] = self.fmt_roi(*bk_roi)

        keys = [
            "laneline_mask",
            "laneline_label",
            "laneline_color",
            "roadside_mask",
            "roadside_label",
        ]
        keys = [k for k in keys if type(data[k]) is np.ndarray]
        kwargs = dict(
            image=data["img"],
            masks=[data[k] for k in keys],
            keypoints=data["break_points"],
        )

        roi = self.parse_roi(W, H, **data.get("roi", {}))
        data["roi"] = self.fmt_roi(0, 0, W, H)
        if self.method == "crop":
            data["roi"] = self.fmt_roi(*roi)

        kwargs = self.aug(roi, **kwargs)
        data["img"] = kwargs["image"]
        for k, m in zip(keys, kwargs["masks"]):
            data[k] = m
        data["break_points"] = kwargs["keypoints"]
        return data


@PIPELINES.register_module()
class Alaug(object):

    def __init__(self, transforms, flip_kwargs={}):
        assert isinstance(transforms, collections.abc.Sequence)
        # init as None
        self.__augmentor = None
        # put transforms in a list
        self.transforms = []
        self.bbox_params = None
        self.keypoint_params = None

        self.flip = None
        for transform in transforms:
            if isinstance(transform, dict):
                if transform["type"] == "Compose":
                    self.get_al_params(transform["params"])
                elif transform["type"] == "HorizontalFlip":
                    self.flip = transform
                else:
                    transform = self.build_transforms(transform)
                    if transform is not None:
                        self.transforms.append(transform)
            else:
                raise TypeError("transform must be a dict")
        self.build()
        self.flip_kwargs = flip_kwargs
        if len(self.flip_kwargs) <= 0:
            self.flip_kwargs = {"laneline_label": [[7, 8]]}

    def get_al_params(self, compose):
        if compose["bboxes"]:
            self.bbox_params = al.BboxParams(format="pascal_voc",
                                             min_area=0.0,
                                             min_visibility=0.0,
                                             label_fields=["bbox_labels"])
        if compose["keypoints"]:
            self.keypoint_params = al.KeypointParams(format="xy")
        # if compose["mask"]:
        #     self.mask_params = al.MaskDropout

    def build_transforms(self, transform):
        aug_type = transform["type"]
        if aug_type == "OneOf":
            transforms = transform["transforms"]
            transforms = list(map(self.build_transforms, transforms))
            return al.OneOf(transforms=transforms, p=transform["p"])
        params = {k: v for k, v in transform.items() if k != "type"}
        if aug_type == "RandomCenterCrop":
            return RandomCenterCrop(**params)
        if aug_type == "Perspective":
            return Perspective(**params)
        return getattr(al, aug_type)(**params)

    def build(self):
        if len(self.transforms) == 0:
            return
        self.__augmentor = al.Compose(
            self.transforms,
            bbox_params=self.bbox_params,
            keypoint_params=self.keypoint_params,
        )

    def cal_sum_list(self, itmes, index):
        sum = 0
        for i in range(index):
            sum += itmes[i]
        return sum

    def __call__(self, data):
        if self.__augmentor is None:
            return data
        keys = [
            "laneline_mask",
            "laneline_label",
            "laneline_color",
            "roadside_mask",
            "roadside_label",
            "depth_map",
        ]
        keys = [k for k in keys if type(data[k]) is np.ndarray]
        kwargs = dict(
            image=data["img"],
            masks=[data[k] for k in keys],
            keypoints=data.get("break_points", []),
        )

        kwargs = self.__augmentor(**kwargs)
        if self.flip is not None:
            flip_kwargs = {
                i: self.flip_kwargs[k]
                for i, k in enumerate(keys) if k in self.flip_kwargs
            }
            flip = HorizontalFlip(flip_kwargs, **self.flip)
            kwargs = flip(**kwargs)

        data["img"] = kwargs["image"]
        data["img_shape"] = data["img"].shape
        for k, m in zip(keys, kwargs["masks"]):
            data[k] = m
        data["break_points"] = kwargs["keypoints"]

        return data

    def __repr__(self):
        format_string = self.__class__.__name__ + "("
        for t in self.transforms:
            format_string += "\n"
            format_string += "    {0}".format(t)
        format_string += "\n)"
        return format_string
