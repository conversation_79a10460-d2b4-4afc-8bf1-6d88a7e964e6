"""
Creates a EfficientNetV2 Model as defined in:
<PERSON><PERSON>, Quoc V. Le. (2021). 
EfficientNetV2: Smaller Models and Faster Training
arXiv preprint arXiv:2104.00298.
import from https://github.com/d-li14/mobilenetv2.pytorch
"""

import torch
import torch.nn as nn
import math
from pillar.models import MODELS
from mmengine.logging import MMLogger
from mmcv.runner.checkpoint import CheckpointLoader

#__all__ = ['effnetv2_s', 'effnetv2_m', 'effnetv2_l', 'effnetv2_xl']


def _make_divisible(v, divisor, min_value=None):
    """
    This function is taken from the original tf repo.
    It ensures that all layers have a channel number that is divisible by 8
    It can be seen here:
    https://github.com/tensorflow/models/blob/master/research/slim/nets/mobilenet/mobilenet.py
    :param v:
    :param divisor:
    :param min_value:
    :return:
    """
    if min_value is None:
        min_value = divisor
    new_v = max(min_value, int(v + divisor / 2) // divisor * divisor)
    # Make sure that round down does not go down by more than 10%.
    if new_v < 0.9 * v:
        new_v += divisor
    return new_v


# SiLU (Swish) activation function
if hasattr(nn, 'SiLU'):
    SiLU = nn.SiLU
else:
    # For compatibility with old PyTorch versions
    class SiLU(nn.Module):
        def forward(self, x):
            return x * torch.sigmoid(x)

 
class SELayer(nn.Module):
    def __init__(self, inp, oup, reduction=4):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
                nn.Linear(oup, _make_divisible(inp // reduction, 8)),
                SiLU(),
                nn.Linear(_make_divisible(inp // reduction, 8), oup),
                nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y


def conv_3x3_bn(inp, oup, stride):
    return nn.Sequential(
        nn.Conv2d(inp, oup, 3, stride, 1, bias=False),
        nn.BatchNorm2d(oup),
        SiLU()
    )


def conv_1x1_bn(inp, oup):
    return nn.Sequential(
        nn.Conv2d(inp, oup, 1, 1, 0, bias=False),
        nn.BatchNorm2d(oup),
        SiLU()
    )


class MBConv(nn.Module):
    def __init__(self, inp, oup, stride, expand_ratio, use_se):
        super(MBConv, self).__init__()
        assert stride in [1, 2]

        hidden_dim = round(inp * expand_ratio)
        self.identity = stride == 1 and inp == oup
        if use_se:
            self.conv = nn.Sequential(
                # pw
                nn.Conv2d(inp, hidden_dim, 1, 1, 0, bias=False),
                nn.BatchNorm2d(hidden_dim),
                SiLU(),
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, 3, stride, 1, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                SiLU(),
                SELayer(inp, hidden_dim),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )
        else:
            self.conv = nn.Sequential(
                # fused
                nn.Conv2d(inp, hidden_dim, 3, stride, 1, bias=False),
                nn.BatchNorm2d(hidden_dim),
                SiLU(),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )


    def forward(self, x):
        if self.identity:
            return x + self.conv(x)
        else:
            return self.conv(x)

@MODELS.register_module()
class EffNetV2(nn.Module):
    def __init__(self, cfgs, init_cfg, num_classes=1000, width_mult=1.):
        super(EffNetV2, self).__init__()
        self.cfgs = cfgs
        self.init_cfg = init_cfg

        # building first layer
        input_channel = _make_divisible(24 * width_mult, 8)
        self.stage0 = conv_3x3_bn(3, input_channel, 2)
        
        # building inverted residual blocks
        block = MBConv
        stages = []
        for t, c, n, s, use_se in self.cfgs:
            stage = []
            output_channel = _make_divisible(c * width_mult, 8)
            for i in range(n):
                stage.append(block(input_channel, output_channel, s if i == 0 else 1, t, use_se))
                input_channel = output_channel
            stages.append(nn.Sequential(*stage))
        
        self.stages = nn.ModuleList(stages)

        # transition layers to match the input of neck
        self.out_convs = nn.ModuleList([
            nn.Conv2d(48, 64, kernel_size=1), 
            nn.Conv2d(64, 64, kernel_size=1), 
            nn.Conv2d(128, 128, kernel_size=1), 
            nn.Conv2d(256, 128, kernel_size=1) 
        ])

    def forward(self, x):
        feature_maps = []
        x = self.stage0(x)

        # collect intermediate feature maps
        for i, stage in enumerate(self.stages):
            x = stage(x)
            # select only specific stages for outputs
            if i in {1, 2, 3, 5}:  # indices corresponding to required outputs
                feature_maps.append(x)

        # adjust channels for selected feature maps
        for i, conv in enumerate(self.out_convs):
            feature_maps[i] = conv(feature_maps[i])

#        for f in feature_maps:
#            print(f.shape)

        return feature_maps

    def init_weights(self, pretrained=None):
        if self.init_cfg is None:
            for m in self.modules():
                if isinstance(m, nn.Conv2d):
                    n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                    m.weight.data.normal_(0, math.sqrt(2. / n))
                    if m.bias is not None:
                        m.bias.data.zero_()
                elif isinstance(m, nn.BatchNorm2d):
                    m.weight.data.fill_(1)
                    m.bias.data.zero_()
                elif isinstance(m, nn.Linear):
                    m.weight.data.normal_(0, 0.001)
                    m.bias.data.zero_()
        else:
            # load checkpoint
            assert 'checkpoint' in self.init_cfg, (
                "Only support specifying a `checkpoint` key in init_cfg for "
                f"{self.__class__.__name__}"
            )
            ckpt = CheckpointLoader.load_checkpoint(
                self.init_cfg['checkpoint'],
                logger=logger,
                map_location='cpu'
            )
            # some checkpoints might store under 'state_dict' or 'model'
            if 'state_dict' in ckpt:
                state_dict = ckpt['state_dict']
            elif 'model' in ckpt:
                state_dict = ckpt['model']
            else:
                state_dict = ckpt

            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warn(f"[EfficientNet] Missing keys: {missing_keys}")
            if unexpected_keys:
                logger.warn(f"[EfficientNet] Unexpected keys: {unexpected_keys}")
