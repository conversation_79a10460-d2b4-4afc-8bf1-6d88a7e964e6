import argparse
import os
import warnings

from mmcv import DictAction
#from mmengine.config import DictAction
from mme import __version__

from pillar.algorithm_backend.builder import build_algorithm_backend


def parse_args():
    parser = argparse.ArgumentParser(description='Pillar train a model')
    parser.add_argument('config', help='train config file path')
    parser.add_argument('--work-dir', help='the dir to save logs and models')
    parser.add_argument('--resume-from',
                        help='the checkpoint file to resume from')
    parser.add_argument(
        '--no-validate',
        action='store_true',
        help='whether not to evaluate the checkpoint during training')
    group_gpus = parser.add_mutually_exclusive_group()
    group_gpus.add_argument('--gpus',
                            type=int,
                            help='number of gpus to use'
                            '(only applicable to non-distributed training)')
    group_gpus.add_argument('--gpu-ids',
                            type=int,
                            nargs='+',
                            help='ids of gpus to use '
                            '(only applicable to non-distributed training)')
    parser.add_argument('--seed', type=int, default=None, help='random seed')
    parser.add_argument(
        '--deterministic',
        action='store_true',
        help='whether to set deterministic options for CUDNN backend.')
    parser.add_argument(
        '--options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file (deprecate), '
        'change to --cfg-options instead.')
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.')
    parser.add_argument('--launcher',
                        choices=['none', 'pytorch', 'slurm', 'mpi'],
                        default='none',
                        help='job launcher')
    parser.add_argument('--local_rank', type=int, default=0)
    parser.add_argument('--algorithm-backend',
                        choices=['OpenMMLab', 'Spring'],
                        default='OpenMMLab',
                        help='algorithm library backend')
    parser.add_argument('--function', type=str, help='pillar function name')
    parser.add_argument('--sync-bn',
                        action='store_true',
                        help='whether not to convert sync batchnorm')
    parser.add_argument('--petrel-conf-path',
                        type=str,
                        default='~/petreloss.conf',
                        help='config path of petrel client')
    # TODO: POD test argument, need to be merged with OpenMMLab
    parser.add_argument('-e',
                        '--evaluate',
                        dest='evaluate',
                        action='store_true',
                        help='evaluate model on validation set')
    parser.add_argument(
        '--fork-method',
        dest='fork_method',
        type=str,
        default='fork',
        choices=['spawn', 'fork'],
        help='method to fork subprocess, especially for dataloader')
    parser.add_argument('--async',
                        dest='asynchronize',
                        action='store_true',
                        help='whether to use asynchronize mode(linklink)')
    parser.add_argument('--ddp',
                        action='store_true',
                        help='Using DistributedDataParallel')
    parser.add_argument(
        '--nocudnn',
        dest='nocudnn',
        action='store_true',
        help=
        'Whether to use cudnn backend or not. Please disable cudnn when running on V100'
    )
    parser.add_argument(
        '--allow_dead_parameter',
        action='store_true',
        help=
        'dead parameter (defined in model but not used in forward pass) is allowed'
    )
    parser.add_argument('--test_gpu',
                        dest='test_gpu',
                        action='store_true',
                        help='test if gpus work properly before training')
    parser.add_argument('--display',
                        dest='display',
                        type=int,
                        default=20,
                        help='display intervel')
    parser.add_argument('--no_running_config',
                        action='store_true',
                        help='disable display running config')
    parser.add_argument('--phase', default='train', help="train phase")
    parser.add_argument('--opts',
                        help='options to replace yaml config',
                        default=None,
                        nargs=argparse.REMAINDER)

    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)

    if args.options and args.cfg_options:
        raise ValueError(
            '--options and --cfg-options cannot be both '
            'specified, --options is deprecated in favor of --cfg-options')
    if args.options:
        warnings.warn('--options is deprecated in favor of --cfg-options')
        args.cfg_options = args.options

    return args


def main():
    args = parse_args()
    backend_cfg = dict(type=args.algorithm_backend, args=args)
    backend = build_algorithm_backend(backend_cfg)
    backend.train()


if __name__ == '__main__':
    main()
