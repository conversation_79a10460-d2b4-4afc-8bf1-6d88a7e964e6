import subprocess

def run_script(script_name):
    try:
        result = subprocess.run(['python3', script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while running {script_name}")
        raise

if __name__ == "__main__":
    try:
        print("Running inference_on_onnx.py...")
        run_script('src/inference_on_onnx.py')
        
        print("Running compute_metrics.py...")
        run_script('src/compute_metrics.py')
        
    except Exception as e:
        print(f"An error occurred: {e}")
