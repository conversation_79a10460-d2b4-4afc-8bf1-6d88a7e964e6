import copy
import math
import random
from functools import cmp_to_key

import cv2
import numpy as np
import PIL.Image
import PIL.ImageDraw
from mmcv.parallel import DataContainer as DC
from shapely.geometry import Polygon, LineString

from pillar.datasets import PIPELINES


def gaussian2D(shape, sigma=1):
    m, n = [(ss - 1.) / 2. for ss in shape]
    y, x = np.ogrid[-m:m + 1, -n:n + 1]
    h = np.exp(-(x * x + y * y) / (2 * sigma * sigma))
    h[h < np.finfo(h.dtype).eps * h.max()] = 0
    return h


def draw_umich_gaussian(heatmap, center, radius, k=1):
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)
    x, y = int(center[0]), int(center[1])
    height, width = heatmap.shape[0:2]
    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)
    masked_heatmap = heatmap[y - top:y + bottom, x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom,
                               radius - left:radius + right]
    if np.size(masked_gaussian) > 0 and np.size(masked_heatmap) > 0:
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def cal_dis(p1, p2):
    return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)


def get_line_intersection(x, y, line):

    def in_line_range(val, start, end):
        s = min(start, end)
        e = max(start, end)
        if val >= s and val <= e and s != e:
            return True
        else:
            return False

    def choose_min_reg(val, ref):
        min_val = 1e5
        index = -1
        if len(val) == 0:
            return None
        else:
            for i, v in enumerate(val):
                if abs(v - ref) < min_val:
                    min_val = abs(v - ref)
                    index = i
        return val[index]

    reg_y = []
    reg_x = []

    for i in range(len(line) - 1):
        point_start, point_end = line[i], line[i + 1]
        if in_line_range(x, point_start[0], point_end[0]):
            k = (point_end[1] - point_start[1]) / (point_end[0] -
                                                   point_start[0])
            reg_y.append(k * (x - point_start[0]) + point_start[1])
    reg_y = choose_min_reg(reg_y, y)

    for i in range(len(line) - 1):
        point_start, point_end = line[i], line[i + 1]
        if in_line_range(y, point_start[1], point_end[1]):
            k = (point_end[0] - point_start[0]) / (point_end[1] -
                                                   point_start[1])
            reg_x.append(k * (y - point_start[1]) + point_start[0])
    reg_x = choose_min_reg(reg_x, x)
    return reg_x, reg_y


def convert_list(p, downscale=None):
    xy = list()
    if downscale is None:
        for i in range(len(p) // 2):
            xy.append((p[2 * i], p[2 * i + 1]))
    else:
        for i in range(len(p) // 2):
            xy.append((p[2 * i] / downscale, p[2 * i + 1] / downscale))
    return xy


def draw_label(
    mask,
    xy,
    val,
    shape_type="polygon",
    width=3,
):
    mask = PIL.Image.fromarray(mask)
    xy = [tuple(_) for _ in xy]
    if shape_type == "polygon":
        PIL.ImageDraw.Draw(mask).polygon(xy=xy, outline=val, fill=val)
    else:
        PIL.ImageDraw.Draw(mask).line(xy=xy, fill=val, width=width)
    mask = np.array(mask, dtype=np.uint8)
    return mask


def clamp_line(line, box, min_length=0):
    left, top, right, bottom = box
    loss_box = Polygon([[left, top], [right, top], [right, bottom],
                        [left, bottom]])
    line_coords = np.array(line).reshape((-1, 2))
    if line_coords.shape[0] < 2:
        return None
    try:
        line_string = LineString(line_coords)
        I = line_string.intersection(loss_box)
        if I.is_empty:
            return None
        if I.length < min_length:
            return None
        if isinstance(I, LineString):

            pts = list(I.coords)
            return pts
        # elif isinstance(I, MultiLineString):
        #     pts = []
        #     Istrings = list(I)
        #     for Istring in Istrings:
        #         pts += list(Istring.coords)
        #     return pts
    except:
        return None


def select_mask_points(ct, r, shape, max_sample=5):

    def in_range(pt, w, h):
        if pt[0] >= 0 and pt[0] < w and pt[1] >= 0 and pt[1] < h:
            return True
        else:
            return False

    h, w = shape[:2]
    valid_points = []
    r = max(int(r // 2), 1)
    ct = (int(ct[0]), int(ct[1]))
    start_x, end_x = ct[0] - r, ct[0] + r
    start_y, end_y = ct[1] - r, ct[1] + r
    for x in range(start_x, end_x + 1):
        for y in range(start_y, end_y + 1):
            if x == ct[0] and y == ct[1]:
                continue
            if in_range((x, y), w, h) and cal_dis((x, y), ct) <= r + 0.1:
                valid_points.append([x, y])
    if len(valid_points) > max_sample - 1:
        valid_points = random.sample(valid_points, max_sample - 1)
    valid_points.append([ct[0], ct[1]])
    return valid_points


def extend_line(line, dis=10):
    start = line[1]
    end = line[0]
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    norm = math.sqrt(dx**2 + dy**2)
    dx = dx / norm
    dy = dy / norm
    extend_point = np.array([[start[0] + dx * dis, start[1] + dy * dis]])
    extended = np.concatenate([extend_point, line])
    return extended


def sort_line_func(a, b):

    def get_line_intersection(y, line):

        def in_line_range(val, start, end):
            s = min(start, end)
            e = max(start, end)
            if s == e and val == s:
                return 1
            elif val >= s and val <= e and s != e:
                return 2
            else:
                return 0

        reg_x = []
        # 水平线的交点
        for i in range(len(line) - 1):
            point_start, point_end = line[i], line[i + 1]
            flag = in_line_range(y, point_start[1], point_end[1])
            if flag == 2:
                k = (point_end[0] - point_start[0]) / (point_end[1] -
                                                       point_start[1])
                reg_x.append(k * (y - point_start[1]) + point_start[0])
            elif flag == 1:
                reg_x.append((point_start[0] + point_end[0]) / 2)
        reg_x = min(reg_x)

        return reg_x

    line1 = np.array(copy.deepcopy(a))
    line2 = np.array(copy.deepcopy(b))
    line1_ymin = min(line1[:, 1])
    line1_ymax = max(line1[:, 1])
    line2_ymin = min(line2[:, 1])
    line2_ymax = max(line2[:, 1])
    if line1_ymax <= line2_ymin or line2_ymax <= line1_ymin:
        y_ref1 = (line1_ymin + line1_ymax) / 2
        y_ref2 = (line2_ymin + line2_ymax) / 2
        x_line1 = get_line_intersection(y_ref1, line1)
        x_line2 = get_line_intersection(y_ref2, line2)
    else:
        ymin = max(line1_ymin, line2_ymin)
        ymax = min(line1_ymax, line2_ymax)
        y_ref = (ymin + ymax) / 2
        x_line1 = get_line_intersection(y_ref, line1)
        x_line2 = get_line_intersection(y_ref, line2)

    if x_line1 < x_line2:
        return -1
    elif x_line1 == x_line2:
        return 0
    else:
        return 1


def nms_endpoints(lane_ends, thr):

    def cal_dis(p1, p2):
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    def search_groups(coord, groups, thr):
        for idx_group, group in enumerate(groups):
            for group_point in group:
                group_point_coord = group_point[1]
                if cal_dis(coord, group_point_coord) <= thr:
                    return idx_group
        return -1

    def update_coords(points_info, thr=4):
        groups = []
        for idx, coord in enumerate(points_info):
            idx_group = search_groups(coord, groups, thr)
            if idx_group < 0:
                groups.append([(idx, coord)])
            else:
                groups[idx_group].append((idx, coord))

        return groups

    results = []

    points = [item[0] for item in lane_ends]
    groups = update_coords(points, thr=thr)
    for group in groups:
        group_points = []
        lanes = []
        for idx, coord in group:
            group_points.append(coord)
            lanes.append(lane_ends[idx][1])
        group_points = np.array(group_points)
        center_x = (np.min(group_points[:, 0]) +
                    np.max(group_points[:, 0])) / 2
        center_y = (np.min(group_points[:, 1]) +
                    np.max(group_points[:, 1])) / 2
        center = (center_x, center_y)
        max_dis = 0
        for point in group_points:
            dis = cal_dis(center, point)
            if dis > max_dis:
                max_dis = dis
        lanes = sorted(lanes, key=cmp_to_key(sort_line_func))
        results.append([center, lanes, dis])

    return results


def gather_attr(attr_mask, x, y):
    if type(attr_mask) is not np.ndarray:
        # 全图是0或者255
        return attr_mask
    if len(y) == 1:
        # 只有一个点
        return attr_mask[y, x][0]
    attrs = attr_mask[y, x]
    bg = attrs == 0
    ig = attrs == 255
    if (bg | ig).all():
        # 线上全是0或者255
        return 0 if bg.all() else 255
    attrs = attrs[~(bg | ig)]
    # 统计众数
    attr = np.argmax(np.bincount(attrs))
    return attr


def expand_bg(dst, src, negs=[0]):
    bg_mask = np.stack([dst == n for n in negs])
    bg_mask = np.any(bg_mask, 0)
    dst = np.where(bg_mask, src, dst)
    return dst


def expand_mask_width(mask, expand=1, negs=[0]):
    for _ in range(expand):
        mask[1:, :] = expand_bg(mask[1:, :], mask[:-1, :], negs)
        mask[:-1, :] = expand_bg(mask[:-1, :], mask[1:, :], negs)
        mask[:, 1:] = expand_bg(mask[:, 1:], mask[:, :-1], negs)
        mask[:, :-1] = expand_bg(mask[:, :-1], mask[:, 1:], negs)
    return mask


@PIPELINES.register_module()
class CollectLkdClsLane:

    def __init__(
        self,
        down_scale,
        hm_down_scale=None,
        line_width=3,
        max_mask_sample=5,
        perspective=False,
        radius=2,
        filter_pts=0,
        interval=10,
        endpoint="down",  # "down", "top", "mid", "far", "near"
        attr_expand=0,  # <=0: no expand; >0: expand * times
        attr_range=0,  # <=0: single start point; 0< * <1: part points; >=1; all points
        keys=[
            'img', 'ignore_laneline', 'ignore_roadside', 'laneline_gt_hm',
            'roadside_gt_hm', 'laneline_mask', 'laneline_label',
            'laneline_color', 'roadside_mask', 'roadside_label', 'depth_map'
        ],
        meta_keys=[
            'filename', 'img_prefix', 'sub_img_name', 'img_shape', 'ori_shape',
            'roi', 'bk_roi', 'break_points', 'img_norm_cfg', 'hm_shape',
            'mask_shape', 'down_scale', 'hm_down_scale', 'laneline',
            'roadside', 'scene', 'camera_index', 'pers_trans_m'
        ],
        **kwargs,
    ):
        self.down_scale = down_scale
        self.hm_down_scale = hm_down_scale if hm_down_scale is not None else down_scale
        self.line_width = line_width
        self.max_mask_sample = max_mask_sample
        self.radius = radius
        self.filter_pts = filter_pts
        self.interval = interval
        self.endpoint = endpoint
        self.attr_expand = attr_expand
        self.attr_range = np.clip(attr_range, 0, 1)

        self.meta_keys = meta_keys
        self.keys = keys

    def min_dis_one_point(self, points, idx):
        min_dis = 1e6
        for i in range(len(points)):
            if i == idx:
                continue
            else:
                d = cal_dis(points[idx], points[i])
                if d < min_dis:
                    min_dis = d
        return min_dis

    def mask2point(self, anno_mask, anno_label=255, anno_color=255):
        instances = np.unique(anno_mask).tolist()
        if 0 in instances:
            instances.remove(0)
        if 255 in instances:
            instances.remove(255)

        points = []
        labels = []
        colors = []
        idx2insidx = []

        for instance in instances:
            idx2insidx.append(instance)
            instance_mask = anno_mask == instance
            y, x = np.where(instance_mask)

            # find a point and give it label and color
            num = len(y)
            num = np.clip(self.attr_range * num, 1, num)
            label = gather_attr(anno_label, x[-num:], y[-num:])
            color = gather_attr(anno_color, x[-num:], y[-num:])
            labels.append(label)
            colors.append(color)

            y_max = y.max()
            y_min = y.min()
            y = np.array([*range(y_min, y_max, self.interval), y_max])
            instance_mask = instance_mask[y]
            row_mask = instance_mask.any(-1)
            y = y[row_mask]
            instance_mask = instance_mask[row_mask]
            x = np.arange(0, instance_mask.shape[-1])
            x = (instance_mask * x).sum(-1) / instance_mask.sum(-1)
            point = np.stack([x, y], -1)
            points.append(point)
        return points, labels, colors, idx2insidx

    def kps2mask(self, mask, kps, gt_masks, valid_idx2insidx, mask_h, r=2):
        for m in gt_masks:
            m["break_target_weight"] = np.zeros(mask_h, np.bool)
            m["break_target"] = np.zeros(mask_h)
        valid_idx2insidx = {v: k for k, v in enumerate(valid_idx2insidx)}
        for x, y in kps:
            lane_id = mask[int(y), int(x)]
            if lane_id not in valid_idx2insidx:
                continue
            lane_id = valid_idx2insidx[lane_id]
            gt_masks[lane_id]["break_target_weight"][:] = True
            break_target = gt_masks[lane_id]["break_target"]
            y = int(np.round(y / self.down_scale))
            for j in range(max(y - r + 1, 0), min(y + r, len(break_target))):
                d = j - y
                break_target[j] = np.exp(-d * d / 2.8)

    def process_mask(
        self,
        mask,
        gt_hm,
        mask_w,
        mask_h,
        hm_w,
        hm_h,
        ratio_hm_mask,
        anno_label=255,
        anno_color=255,
    ):
        assert mask is not None
        gt_points, gt_labels, gt_colors, idx2insidx = self.mask2point(
            mask,
            anno_label,
            anno_color,
        )
        valid_gt = []
        valid_gt_points = []
        valid_idx2insidx = []
        for i in range(len(gt_points)):
            gt_point = gt_points[i]
            if 0 < self.filter_pts < len(gt_point):
                gt_point = gt_point[self.filter_pts:]
            pts = gt_point / self.down_scale
            pts = pts[::-1]
            if len(pts) > 1:
                valid_gt.append(pts)
                valid_gt_points.append(gt_point)
                valid_idx2insidx.append(idx2insidx[i])
        gt_points = valid_gt_points

        # draw gt_hm_lane
        gt_hm_lane_ends = []  # [hm end point,points]
        for l in valid_gt:
            if self.endpoint == "down":
                idx = 0
            elif self.endpoint == "top":
                idx = -1
            elif self.endpoint == "far":
                idx = int(len(l) * 0.75)
            elif self.endpoint == "near":
                idx = int(len(l) * 0.25)
            else:
                idx = len(l) // 2
            point = tuple(l[idx] * ratio_hm_mask)
            gt_hm_lane_ends.append([point, l])  # [end point,lane points]

        radius = [self.radius] * len(gt_hm_lane_ends)
        if len(gt_hm_lane_ends) >= 2:
            endpoints = [p[0] for p in gt_hm_lane_ends]
            for j in range(len(endpoints)):
                dis = self.min_dis_one_point(endpoints, j)
                if dis < 1.5 * radius[j]:
                    radius[j] = int(max(dis / 1.5, 1) + 0.49999)

        res = []
        for insidx, (end_point, line) in enumerate(gt_hm_lane_ends):
            gt_hm[0] = draw_umich_gaussian(gt_hm[0], end_point, radius[insidx])
            y_min = int(np.min(line[:, 1]))
            y_max = int(np.max(line[:, 1]))
            mask_points = select_mask_points(
                end_point,
                radius[insidx],
                (hm_h, hm_w),
                self.max_mask_sample,
            )
            pos = np.zeros((mask_h), np.float32)
            pos_mask = np.zeros((mask_h), np.float32)
            reg = np.zeros((1, mask_h, mask_w), np.float32)
            reg_mask = np.zeros((1, mask_h, mask_w), np.float32)
            lane_range = np.zeros((1, mask_h), np.int64)
            m = np.zeros((mask_h, mask_w), np.uint8)
            polygon = extend_line(line)
            polygon_map = draw_label(
                m,
                polygon,
                1,
                "line",
                width=self.line_width + 9,
            ) > 0
            for y in range(polygon_map.shape[0]):
                for x in np.where(polygon_map[y, :])[0]:
                    # TODO 适用插值优化该部分
                    reg_x, _ = get_line_intersection(x, y, line)
                    # kps and kps_mask:
                    if reg_x is not None:
                        offset = reg_x - x
                        reg[0, y, x] = offset
                        if abs(offset) < 10:
                            reg_mask[0, y, x] = 1
                        if y_min <= y <= y_max:
                            pos[y] = reg_x
                            pos_mask[y] = 1
                        lane_range[:, y] = 1
            info = dict(
                reg=reg,  # gt position of (x,y)
                reg_mask=reg_mask,  # index reg loss valid or not
                points=mask_points,  # valid start point
                row=pos,  # row wise position
                row_mask=pos_mask,  # row wise position valid or not
                range=lane_range,  # index this row has lane
                label=0,
                y_max=y_max,
                y_min=y_min,
                type=gt_labels[insidx],
                color=gt_colors[insidx],
            )
            res.append(info)

        return res, gt_points, valid_idx2insidx

    def target(self, results):
        output_h = int(results["img_shape"][0])
        output_w = int(results["img_shape"][1])
        mask_h = int(output_h // self.down_scale)  # 8x
        mask_w = int(output_w // self.down_scale)
        hm_h = int(output_h // self.hm_down_scale)  # 16x
        hm_w = int(output_w // self.hm_down_scale)
        ratio_hm_mask = self.down_scale / self.hm_down_scale

        results["hm_shape"] = [hm_h, hm_w]
        results["mask_shape"] = [mask_h, mask_w]
        results["down_scale"] = self.down_scale
        results["hm_down_scale"] = self.hm_down_scale

        def resize(im):
            if type(im) is np.ndarray:
                im = cv2.resize(
                    im,
                    (mask_w, mask_h),
                    interpolation=cv2.INTER_NEAREST,
                )
                im = expand_mask_width(im, self.attr_expand)
            else:
                im = np.ones((mask_h, mask_w), np.uint8) * im
            return im

        mask = results["laneline_mask"]
        label = results["laneline_label"]
        color = results["laneline_color"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, gt_points, valid_idx2insidx = self.process_mask(
                mask, gt_hm, mask_w, mask_h, hm_w, hm_h, ratio_hm_mask, label,
                color)

            self.kps2mask(mask, results.get("break_points", []), gt_masks,
                          valid_idx2insidx, mask_h)

            lane = dict(key_points=gt_points, gt_masks=gt_masks)
            results["laneline"] = lane
            results["ignore_laneline"] = False
        else:
            results["laneline"] = dict(key_points=[], gt_masks=[])
            results["ignore_laneline"] = True
        results["laneline_mask"] = resize(mask)
        results["laneline_label"] = resize(label)
        results["laneline_color"] = resize(color)
        results["laneline_gt_hm"] = gt_hm

        mask = results["roadside_mask"]
        label = results["roadside_label"]
        gt_hm = np.zeros((1, hm_h, hm_w), np.float32)
        if type(mask) is np.ndarray:
            gt_masks, gt_points, _ = self.process_mask(mask, gt_hm, mask_w,
                                                       mask_h, hm_w, hm_h,
                                                       ratio_hm_mask, label)

            roadside = dict(key_points=gt_points, gt_masks=gt_masks)
            results["roadside"] = roadside
            results["ignore_roadside"] = False
        else:
            results["roadside"] = dict(key_points=[], gt_masks=[])
            results["ignore_roadside"] = True
        results["roadside_mask"] = resize(mask)
        results["roadside_label"] = resize(label)
        results["roadside_gt_hm"] = gt_hm

        return True

    def __call__(self, results):
        data = {}
        img_meta = {}
        valid = self.target(results)
        if not valid:
            return None
        for key in self.meta_keys:
            # if key in results.keys():
            img_meta[key] = results[key]
        data["img_metas"] = DC(img_meta, cpu_only=True)
        for key in self.keys:
            # if key in results.keys():
            data[key] = results[key]

        return data
