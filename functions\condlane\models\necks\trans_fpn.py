import math

import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule, xavier_init, build_norm_layer
from mmcv.utils import print_log

from pillar.models import MODELS


class PositionEmbeddingSine(nn.Module):
    """
    This is a more standard version of the position embedding, very similar to the one
    used by the Attention is all you need paper, generalized to work on images.
    """

    def __init__(self,
                 num_pos_feats=64,
                 temperature=10000,
                 normalize=False,
                 scale=None):
        super().__init__()
        self.num_pos_feats = num_pos_feats
        self.temperature = temperature
        self.normalize = normalize
        if scale is not None and normalize is False:
            raise ValueError("normalize should be True if scale is passed")
        if scale is None:
            scale = 2 * math.pi
        self.scale = scale

    def forward(self, mask):
        assert mask is not None
        not_mask = ~mask
        y_embed = not_mask.cumsum(1, dtype=torch.float32)
        x_embed = not_mask.cumsum(2, dtype=torch.float32)
        if self.normalize:
            eps = 1e-6
            y_embed = y_embed / (y_embed[:, -1:, :] + eps) * self.scale
            x_embed = x_embed / (x_embed[:, :, -1:] + eps) * self.scale

        dim_t = torch.arange(self.num_pos_feats,
                             dtype=torch.float32,
                             device=mask.device)
        dim_t = self.temperature**(2 * (dim_t // 2) / self.num_pos_feats)

        pos_x = x_embed[:, :, :, None] / dim_t
        pos_y = y_embed[:, :, :, None] / dim_t
        pos_x = torch.stack(
            (pos_x[:, :, :, 0::2].sin(), pos_x[:, :, :, 1::2].cos()),
            dim=4).flatten(3)
        pos_y = torch.stack(
            (pos_y[:, :, :, 0::2].sin(), pos_y[:, :, :, 1::2].cos()),
            dim=4).flatten(3)
        pos = torch.cat((pos_y, pos_x), dim=3).permute(0, 3, 1, 2)
        return pos


def build_position_encoding(hidden_dim, shape):
    mask = torch.zeros(shape, dtype=torch.bool)
    pos_module = PositionEmbeddingSine(hidden_dim // 2)
    pos_embs = pos_module(mask)
    return pos_embs


class AttentionLayer(nn.Module):
    """ Position attention module"""

    def __init__(
            self,
            in_dim,
            out_dim,
            ratio=4,
            stride=1,
            norm_cfg=dict(type="BN2d", requires_grad=True),
            act_cfg=dict(type="ReLU"),
    ):
        super(AttentionLayer, self).__init__()
        self.chanel_in = in_dim
        self.pre_conv = ConvModule(in_dim,
                                   out_dim,
                                   kernel_size=3,
                                   stride=stride,
                                   padding=1,
                                   norm_cfg=norm_cfg,
                                   act_cfg=act_cfg,
                                   inplace=False)
        self.query_conv = nn.Conv2d(in_channels=out_dim,
                                    out_channels=out_dim // ratio,
                                    kernel_size=1)
        self.key_conv = nn.Conv2d(in_channels=out_dim,
                                  out_channels=out_dim // ratio,
                                  kernel_size=1)
        self.value_conv = nn.Conv2d(in_channels=out_dim,
                                    out_channels=out_dim,
                                    kernel_size=1)
        self.final_conv = ConvModule(out_dim,
                                     out_dim,
                                     kernel_size=3,
                                     padding=1,
                                     norm_cfg=norm_cfg,
                                     act_cfg=act_cfg)
        self.softmax = nn.Softmax(dim=-1)
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, x, pos=None):
        """
            inputs :
                x : inpput feature maps( B X C X H X W)
            returns :
                out : attention value + input feature
                attention: B X (HxW) X (HxW)
        """
        x = self.pre_conv(x)
        m_batchsize, _, height, width = x.size()
        if pos is not None:
            # print(x.shape)
            # print(pos.shape)
            x += pos
        proj_query = self.query_conv(x).view(m_batchsize, -1,
                                             width * height).permute(0, 2, 1)
        proj_key = self.key_conv(x).view(m_batchsize, -1, width * height)

        energy = torch.bmm(proj_query, proj_key)
        attention = self.softmax(energy)
        attention = attention.permute(0, 2, 1)
        proj_value = self.value_conv(x).view(m_batchsize, -1, width * height)
        out = torch.bmm(proj_value, attention)
        out = out.view(m_batchsize, -1, height, width)
        proj_value = proj_value.view(m_batchsize, -1, height, width)
        out_feat = self.gamma * out + x
        out_feat = self.final_conv(out_feat)
        return out_feat


class TransConvEncoderModule(nn.Module):

    def __init__(
            self,
            in_dim,
            attn_in_dims,
            attn_out_dims,
            strides,
            ratios,
            downscale=True,
            pos_shape=None,
            norm_cfg=dict(type="BN2d", requires_grad=True),
            act_cfg=dict(type="ReLU"),
    ):
        super(TransConvEncoderModule, self).__init__()
        if downscale:
            stride = 2
        else:
            stride = 1
        # self.first_conv = ConvModule(in_dim, 2*in_dim, kernel_size=3, stride=stride, padding=1)
        # self.final_conv = ConvModule(attn_out_dims[-1], attn_out_dims[-1], kernel_size=3, stride=1, padding=1)
        attn_layers = []
        for dim1, dim2, stride, ratio in zip(attn_in_dims, attn_out_dims,
                                             strides, ratios):
            attn_layers.append(
                AttentionLayer(dim1, dim2, ratio, stride, norm_cfg, act_cfg))
        if pos_shape is not None:
            self.attn_layers = nn.ModuleList(attn_layers)
        else:
            self.attn_layers = nn.Sequential(*attn_layers)
        self.pos_shape = pos_shape
        if pos_shape is not None:
            self.pos_embeds = [None for _ in attn_out_dims]
            self.attn_out_dims = attn_out_dims

    def forward(self, src):
        # src = self.first_conv(src)
        if self.pos_shape is None:
            src = self.attn_layers(src)
        else:
            for i, layer in enumerate(self.attn_layers):
                pos = self.pos_embeds[i]
                if pos is None:
                    dim = self.attn_out_dims[i]
                    pos_shape = [src.shape[0]] + self.pos_shape[1:]
                    pos = build_position_encoding(dim, pos_shape).cuda()
                    self.pos_embeds[i] = pos
                src = layer(src, pos.to(src.device))
        # src = self.final_conv(src)
        return src


"""
trans_cfg=dict(
            in_dim=512,
            attn_in_dims=[512, 64],
            attn_out_dims=[64, 64],
            strides=[1, 1],
            ratios=[4, 4],
            pos_shape=(batch_size, 10, 25),
        )
"""


class NextConvBlock(nn.Module):

    def __init__(
            self,
            in_channel,
            out_channel,
            group,
            kernel=[14, 3],
            expand_ratio=2,
            is_dilation=False,
            kernel_size=1,
            norm_cfg=dict(type="BN2d", requires_grad=True),
            ActType='ReLU',
            depthwise_conv=True
    ):
        super(NextConvBlock, self).__init__()

        mid_channel = int(in_channel * expand_ratio)
        if is_dilation:
            self.conv1 = nn.Conv2d(
                in_channel,
                mid_channel,
                kernel,
                dilation=2,
                padding=((kernel[0] + (kernel[0] - 1) * (2 - 1) - 1) // 2,
                         (kernel[1] + (kernel[1] - 1) * (2 - 1) - 1) // 2),
                groups=group if depthwise_conv else 1,
                bias=False)
        else:
            self.conv1 = nn.Conv2d(
                in_channel,
                mid_channel,
                kernel,
                padding=(kernel[0] // 2, kernel[1] // 2),
                groups=group if depthwise_conv else 1,
                bias=False,
            )
        _, self.bn1 = build_norm_layer(norm_cfg, mid_channel)
        self.relu = nn.ReLU(inplace=True)
        padding = int(kernel_size // 2)
        self.conv2 = nn.Conv2d(
            mid_channel,
            mid_channel,
            kernel_size=kernel_size,
            padding=padding,
        )
        self.conv3 = nn.Conv2d(
            mid_channel,
            out_channel,
            kernel_size=kernel_size,
            padding=padding,
        )

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(self.conv2(out))
        out = self.conv3(out)
        out = out + x
        return out


class NextConvBlockPlus(nn.Module):

    def __init__(
            self,
            in_channel,
            out_channel,
            kernel=[14, 3],
            expand_ratio=2,
            is_dilation=False,
            kernel_size=1,
            norm_cfg=dict(type="BN2d", requires_grad=True),
            ActType='ReLU',
            depthwise_conv=True
    ):
        super(NextConvBlockPlus, self).__init__()

        padding = int(kernel_size // 2)
        mid_channel = int(in_channel * expand_ratio)
        if is_dilation:
            self.conv1 = nn.Conv2d(
                in_channel,
                in_channel,
                kernel,
                dilation=2,
                padding=((kernel[0] + (kernel[0] - 1) * (2 - 1) - 1) // 2,
                         (kernel[1] + (kernel[1] - 1) * (2 - 1) - 1) // 2),
                groups=in_channel if depthwise_conv else 1,
                bias=False,
            )
        else:
            self.conv1 = nn.Conv2d(
                in_channel,
                in_channel,
                kernel,
                padding=(kernel[0] // 2, kernel[1] // 2),
                groups=in_channel if depthwise_conv else 1,
                bias=False,
            )
        _, self.bn1 = build_norm_layer(norm_cfg, in_channel)
        if ActType == "PReLU":
            Activation = nn.PReLU
        elif ActType == "ReLU6":
            Activation = nn.ReLU6
        else:
            Activation = nn.ReLU
        self.relu = Activation(inplace=True)
        self.conv2 = nn.Conv2d(
            in_channel,
            mid_channel,
            kernel_size=kernel_size,
            padding=padding,
        )
        self.conv3 = nn.Conv2d(
            mid_channel,
            out_channel,
            kernel_size=kernel_size,
            padding=padding,
        )
        self.skip_link = (in_channel == out_channel)

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(self.conv2(out))
        out = self.conv3(out)
        if self.skip_link:
            out = out + x
        return out


class NextConvnet(nn.Module):

    def __init__(
            self,
            in_channel,
            channels,
            groups=[],
            kernels=[[15, 3], [7, 3]],
            expand_ratios=[2, 2],
            is_plus=False,
            is_dilation=[False, False],
            kernel_size=1,
            norm_cfg=dict(type="BN2d", requires_grad=True),
            ActType="ReLU",
            depthwise_conv=True,
    ):
        super(NextConvnet, self).__init__()
        blocks = []
        for i in range(len(channels)):
            if is_plus:
                blocks.append(
                    NextConvBlockPlus(
                        in_channel,
                        channels[i],
                        kernel=kernels[i],
                        expand_ratio=expand_ratios[i],
                        is_dilation=is_dilation[i],
                        kernel_size=kernel_size,
                        norm_cfg=norm_cfg,
                        ActType=ActType,
                        depthwise_conv=depthwise_conv
                    ))
            else:
                blocks.append(
                    NextConvBlock(
                        in_channel,
                        channels[i],
                        group=groups[i],
                        kernel=kernels[i],
                        expand_ratio=expand_ratios[i],
                        is_dilation=is_dilation[i],
                        kernel_size=kernel_size,
                        norm_cfg=norm_cfg,
                        ActType=ActType,
                        depthwise_conv=depthwise_conv
                    ))
            in_channel = channels[i]
        self.blocks = nn.ModuleList(blocks)

    def forward(self, x):
        out = x
        for block in self.blocks:
            out = block(out)
        return out


@MODELS.register_module()
class TransConvFPN(nn.Module):

    def __init__(
        self,
        in_channels,  #in_channels=[128, 256, 64]
        out_channels,  #out_channels=64
        num_outs,  #num_outs=3
        start_level=0,
        end_level=-1,
        add_extra_convs=False,
        extra_convs_on_inputs=True,
        relu_before_extra_convs=False,
        no_norm_on_lateral=False,
        conv_cfg=None,
        norm_cfg=dict(type="BN2d", requires_grad=True),
        lateral_norm_cfg=None,
        fpn_norm_cfg=None,
        act_cfg=None,
        trans_head_act_cfg=None,
        trans_idx=-1,  #trans_idx=-1
        trans_cfg=None,
        next_conv_cfg=None,
        attention=True,
        kernel_size=1,
        deconv=False,
        lateral_conv_kernel=1,
    ):
        super().__init__()
        assert isinstance(in_channels, list)
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_ins = len(in_channels)
        self.num_outs = num_outs
        self.relu_before_extra_convs = relu_before_extra_convs
        self.no_norm_on_lateral = no_norm_on_lateral
        self.fp16_enabled = False
        self.trans_cfg = trans_cfg
        self.trans_idx = trans_idx
        self.attention = attention
        if self.attention:
            self.trans_head = TransConvEncoderModule(
                **trans_cfg,
                norm_cfg=norm_cfg,
                act_cfg=trans_head_act_cfg,
            )
        else:
            self.next_conv_cfg = next_conv_cfg
            if next_conv_cfg is not None:
                self.dump_trans_head = NextConvnet(
                    **next_conv_cfg,
                    kernel_size=lateral_conv_kernel,
                    norm_cfg=norm_cfg,
                )
            else:
                self.dump_trans_head = ConvModule(
                    trans_cfg["in_dim"],
                    out_channels,
                    kernel_size,
                    padding=int(kernel_size // 2),
                    conv_cfg=conv_cfg,
                    norm_cfg=norm_cfg if not self.no_norm_on_lateral else None,
                    act_cfg=act_cfg,
                )

        if end_level == -1:
            self.backbone_end_level = self.num_ins
            assert num_outs >= self.num_ins - start_level
        else:
            # if end_level < inputs, no extra level is allowed
            self.backbone_end_level = end_level
            assert end_level <= len(in_channels)
            assert num_outs == end_level - start_level
        self.start_level = start_level
        self.end_level = end_level
        self.add_extra_convs = add_extra_convs
        self.extra_convs_on_inputs = extra_convs_on_inputs

        self.lateral_convs = nn.ModuleList()
        self.fpn_convs = nn.ModuleList()

        for i in range(self.start_level, self.backbone_end_level):
            l_conv = ConvModule(
                in_channels[i],
                out_channels,
                lateral_conv_kernel,
                padding=int(lateral_conv_kernel // 2),
                conv_cfg=conv_cfg,
                norm_cfg=lateral_norm_cfg
                if not self.no_norm_on_lateral else None,
                act_cfg=act_cfg,
            )
            fpn_conv = ConvModule(
                out_channels,
                out_channels,
                3,
                padding=1,
                conv_cfg=conv_cfg,
                norm_cfg=fpn_norm_cfg,
                act_cfg=act_cfg,
            )

            self.lateral_convs.append(l_conv)
            self.fpn_convs.append(fpn_conv)

        # add extra conv layers (e.g., RetinaNet)
        extra_levels = num_outs - (self.backbone_end_level - self.start_level)
        if add_extra_convs and extra_levels >= 1:
            for i in range(extra_levels):
                if i == 0 and self.extra_convs_on_inputs:
                    in_channels = self.in_channels[self.backbone_end_level - 1]
                else:
                    in_channels = out_channels
                extra_fpn_conv = ConvModule(
                    in_channels,
                    out_channels,
                    3,
                    stride=2,
                    padding=1,
                    conv_cfg=conv_cfg,
                    norm_cfg=norm_cfg,
                    act_cfg=act_cfg,
                )
                self.fpn_convs.append(extra_fpn_conv)

        self.upsamples = nn.ModuleList()
        if deconv:
            for i in range(self.start_level + 1, self.backbone_end_level):
                self.upsamples.append(
                    nn.ConvTranspose2d(
                        out_channels,
                        out_channels,
                        2,
                        2,
                    ))

    def load_pretrained_dict(self, pretrained=None):
        renamed_dict = {}
        pretrained_dict = torch.load(pretrained,
                                     map_location="cpu")["state_dict"]
        for key, value in pretrained_dict.items():
            new_key = ".".join(key.split(".")[1:])
            renamed_dict[new_key] = value
        return renamed_dict

    def check_keys(self, own_keys, other_keys, own_name):
        own_keys = set(own_keys)
        other_keys = set(other_keys)
        shared_keys = own_keys & other_keys
        unexpected_keys = other_keys - own_keys
        missing_keys = own_keys - other_keys

        info = "Loading {}:{} shared keys, {} unexpected keys, {} missing keys.".format(
            own_name, len(shared_keys), len(unexpected_keys),
            len(missing_keys))

        if len(missing_keys) > 0:
            info += "\nmissing keys are as follows:\n    {}".format(
                "\n    ".join(missing_keys))
        print_log(info, logger="root")

    def init_weights(self, pretrained=None):
        if isinstance(pretrained, str):
            print_log(f"neck: load model from: {pretrained}", logger="root")
            pretrained_dict = self.load_pretrained_dict(pretrained)
            self.load_state_dict(pretrained_dict, strict=False)
            self.check_keys(self.state_dict().keys(), pretrained_dict.keys(),
                            "neck")

        elif pretrained is None:
            for m in self.modules():
                if isinstance(m, nn.Conv2d):
                    xavier_init(m, distribution="uniform")

    def forward(self, src):
        assert len(src) >= len(self.in_channels)
        src = list(src)
        # for i,fea in  enumerate(src):
        #     print(f"{i} fea.shape {fea.shape}")
        if self.attention:
            trans_feat = self.trans_head(src[self.trans_idx])
        else:
            trans_feat = self.dump_trans_head(src[self.trans_idx])
        inputs = src[:-1]
        inputs.append(trans_feat)
        if len(inputs) > len(self.in_channels):
            for _ in range(len(inputs) - len(self.in_channels)):
                del inputs[0]

        # build laterals
        laterals = [
            lateral_conv(inputs[i + self.start_level])
            for i, lateral_conv in enumerate(self.lateral_convs)
        ]

        # build top-down path
        used_backbone_levels = len(laterals)
        for i in range(used_backbone_levels - 1, 0, -1):
            prev_shape = laterals[i - 1].shape
            if len(self.upsamples) > 0:
                up = self.upsamples[i - 1](laterals[i])
            else:
                up = F.interpolate(
                    laterals[i],
                    size=prev_shape[2:],
                    mode="nearest",
                )
            laterals[i - 1] += up

        # build outputs
        # part 1: from original levels
        outs = [
            self.fpn_convs[i](laterals[i]) for i in range(used_backbone_levels)
        ]
        # part 2: add extra levels
        if self.num_outs > len(outs):
            # use max pool to get more levels on top of outputs
            # (e.g., Faster R-CNN, Mask R-CNN)
            if not self.add_extra_convs:
                for i in range(self.num_outs - used_backbone_levels):
                    outs.append(F.max_pool2d(outs[-1], 1, stride=2))
            # add conv layers on top of original feature maps (RetinaNet)
            else:
                if self.extra_convs_on_inputs:
                    orig = inputs[self.backbone_end_level - 1]
                    outs.append(self.fpn_convs[used_backbone_levels](orig))
                else:
                    outs.append(self.fpn_convs[used_backbone_levels](outs[-1]))
                for i in range(used_backbone_levels + 1, self.num_outs):
                    if self.relu_before_extra_convs:
                        outs.append(self.fpn_convs[i](F.relu(outs[-1])))
                    else:
                        outs.append(self.fpn_convs[i](outs[-1]))

        return tuple(outs), trans_feat


@MODELS.register_module()
class TransConvFPN3x3(TransConvFPN):

    def __init__(self, **kwargs):
        super().__init__(lateral_conv_kernel=3, **kwargs)
