import os
import re

json_dir = "cpp_res" 
#json_dir = "/work/home/<USER>/niloofar/new/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/simplified_pipeline_for_mdc_side/cpp_metrics_computatation/cpp_res" #path to cpp results dir
filename_dir = "/work/home/<USER>/niloofar/new/data/large_new_data_batch3_test_cropped/filename" #path to dir of images whose names you want to rename the jsons to

def natural_sort_key(text):
    return [int(part) if part.isdigit() else part.lower() for part in re.split(r'(\d+)', text)]

filename_files = sorted(os.listdir(filename_dir), key=natural_sort_key)
json_files = sorted(os.listdir(json_dir), key=natural_sort_key)

if len(json_files) > len(filename_files):
    print("Error: There are more JSON files than images. Please check the directories.")
else:
    for json_file, new_name in zip(json_files, filename_files):
        old_path = os.path.join(json_dir, json_file)
        new_name_with_ext = os.path.splitext(new_name)[0] + ".json"
        new_path = os.path.join(json_dir, new_name_with_ext)

        os.rename(old_path, new_path)
        print(f"Renamed {json_file} to {new_name_with_ext}")