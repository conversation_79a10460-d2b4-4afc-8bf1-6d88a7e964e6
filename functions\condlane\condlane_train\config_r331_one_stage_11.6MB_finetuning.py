custom_imports = dict(imports=[
    'condlane.datasets', 'condlane.datasets.pipelines',
    'condlane.models.backbones', 'condlane.models.necks',
    'condlane.models.dense_heads', 'condlane.models.losses',
    'condlane.models.predictors'
])
test_setting = dict(
    repo='pillar',
    single_gpu_test=dict(show=False),
    multi_gpu_test=dict(gpu_collect=True))
find_unused_parameters = True
dist_params = dict(port='29507')
batch_size = 8
num_workers = 4
tokestrel = dict(
    model_name='ep20_r331_data277w_single_stage_relu_sm1_mirror_soft0.5',
    version='3.3.1',
    batch_size=dict(image=1, image2=1))
dataset_type = 'pillar.LkdCropClsDataset'
ceph_cfg = ''
train_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'
train_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'
val_ann_file = '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json'
val_img_prefix = '/home/<USER>/data/once_3d_lanes_data/'
num_lane_classes = 2
categories = dict(
    group=['lane_type', 'lane_color', 'roadside'],
    attr=dict(
        lane_type=[
            'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
            'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
            'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
            'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
            'FISHBONE_DASHED'
        ],
        lane_color=[
            'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE', 'BLUE'
        ],
        roadside=[
            'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
            'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
        ]))
view_distance = 5
inv_view_distance = 5
num_ref_point = 2
length_scale = 0
field_scale = None
maskAct = 'Hsigmoid'
laneline_indices = dict(length=27, offset=29, field=30, view=(33, 37))
roadside_indices = dict(length=28, offset=31, field=32, view=(37, 41))
gt_indices = dict(
    offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23))
img_scale = (864, 288)
h_crop = (0, 288)
camera_roi = dict(
    front_fov30=dict(t=0, b=1, l=0, r=1),
    front_fov120=dict(t=0, b=1, l=0, r=1))
img_norm_cfg = dict(
    mean=[75.3, 76.6, 77.6], std=[50.5, 53.8, 54.3], to_rgb=False)
proposal_heatmap_thr = 0.6
range_method = 'field'
post_processor = dict(
    key_points_size_thr=35,
    side_key_points_size_thr=35,
    range_method='field',
    cls_method='row_att',
    with_breakpoints=True,
    breakpoints_thr=0.6,
    range_thr=0,
    range_continue_tole=0,
    use_offset=True,
    nms_thr=0.0,
    categories=dict(
        group=['lane_type', 'lane_color', 'roadside'],
        attr=dict(
            lane_type=[
                'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                'FISHBONE_DASHED'
            ],
            lane_color=[
                'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                'BLUE'
            ],
            roadside=[
                'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
            ])),
    laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),
    roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),
    mask_upsample_rate=4,
    num_row_ref_point=2,
    inv_view_distance=5,
    length_scale=0,
    field_scale=None,
    with_hm=True,
    mask_sigma=0.5,
    mask_max_thr=0.0,
    th_len=10,
    v4_simplify=True,
    mask_ignores=30)
mask_down_scale = 4
hm_down_scale = 16
line_width = 4
radius = 3
max_mask_sample = 4
train_cfg = dict(out_scale=4)
test_cfg = dict(out_scale=4)
loss_weights = dict(
    hm_weight=2.5,
    kps_weight=0.4,
    row_weight=1.5,
    range_weight=2.0,
    lane_type_weight=[
        1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
    ],
    row_ce_weight=1.0,
    len_loss_weight=1.0,
    auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],
    d2_loss_weight=0.5,
    smooth_weight=0.0,
    view_distance=5,
    inv_view_distance=5,
    num_ref_point=2,
    length_scale=0,
    field_scale=None,
    field_bg_weight=[-1, 0.1],
    attr_bg_weight=0.1,
    mask_bg_weight=[-1, 0.1],
    maskAct='Hsigmoid',
    reg_smooth_l1=True)
norm_cfg = dict(type='BN2d', requires_grad=True)
model = dict(
    type='pillar.FieldLaneNet',
    train_cfg=dict(out_scale=4),
    test_cfg=dict(out_scale=4),
    num_classes=2,
    loss_condlane=dict(
        type='pillar.FieldLaneLoss',
        weights=dict(
            hm_weight=2.5,
            kps_weight=0.4,
            row_weight=1.5,
            range_weight=2.0,
            lane_type_weight=[
                1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
            ],
            row_ce_weight=1.0,
            len_loss_weight=1.0,
            auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],
            d2_loss_weight=0.5,
            smooth_weight=0.0,
            view_distance=5,
            inv_view_distance=5,
            num_ref_point=2,
            length_scale=0,
            field_scale=None,
            field_bg_weight=[-1, 0.1],
            attr_bg_weight=0.1,
            mask_bg_weight=[-1, 0.1],
            maskAct='Hsigmoid',
            reg_smooth_l1=True),
        property_group=[13, 6, 6]),
    backbone=dict(
        type='pillar.ConvNeXtTI',
        depths=[2, 3, 9, 3],
        dims=[64, 64, 128, 128],
        drop_path_rate=0.2,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        ActType='ReLU',
        layer_scale_init_value=0),
    neck=dict(
        type='pillar.TransConvFPN',
        in_channels=[64, 64, 128, 128],
        out_channels=64,
        num_outs=4,
        trans_idx=-1,
        trans_cfg=None,
        next_conv_cfg=dict(
            in_channel=128,
            channels=[128, 128],
            kernels=[[7, 7], [7, 7]],
            expand_ratios=[2, 2],
            is_plus=True,
            ActType='ReLU'),
        attention=False,
        kernel_size=7,
        deconv=False,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        lateral_norm_cfg=None,
        fpn_norm_cfg=None),
    decoder=dict(
        type='pillar.SegFormerHead',
        in_channels=[64, 64, 128, 128],
        in_index=[0, 1, 2, 3],
        feature_strides=[4, 8, 16, 32],
        channels=128,
        dropout_ratio=0.1,
        num_classes=150,
        align_corners=False,
        decoder_params=dict(embed_dim=768),
        loss_decode=dict(
            type='CrossEntropyLoss', use_sigmoid=False, loss_weight=1.0)),
    spatial_head=dict(
        type='pillar.DepthRegressionModel', in_channels=768, out_channels=1),
    head=dict(
        type='pillar.FieldLaneHead',
        in_channel=64,
        ActType='ReLU',
        maskAct='Hsigmoid',
        mask_property_group=dict(
            line_mask=(2, 0, 1),
            laneline_type=(13, 2, 1),
            laneline_color=(6, 2, 1),
            roadside_type=(6, 2, 1),
            roadside_length=(1, 0, 2),
            laneline_length=(1, 0, 2),
            roadside_offset=(2, 0, 1),
            laneline_offset=(2, 0, 1),
            roadside_field=(2, 0, 2),
            laneline_field=(2, 0, 2),
            roadside_view=(8, 0, 2),
            laneline_view=(8, 0, 2),
            roadside_inv_vector_field=(10, 0, 2),
            laneline_inv_vector_field=(10, 0, 2),
            roadside_inv_length=(1, 0, 1),
            laneline_inv_length=(1, 0, 1)),
        num_classes=2,
        with_break_point=False,
        hm_idx=2,
        mask_idx=0,
        proposal_heatmap_thr=0.6,
        range_method='field',
        expand=1,
        with_start_point_off=True,
        with_start_point_feat_group=True,
        with_nms_weight=True,
        norm_cfg=dict(type='BN2d', requires_grad=True),
        v4_simplify=True),
    post_processor=dict(
        key_points_size_thr=35,
        side_key_points_size_thr=35,
        range_method='field',
        cls_method='row_att',
        with_breakpoints=True,
        breakpoints_thr=0.6,
        range_thr=0,
        range_continue_tole=0,
        use_offset=True,
        nms_thr=0.0,
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        laneline_indices=dict(length=27, offset=29, field=30, view=(33, 37)),
        roadside_indices=dict(length=28, offset=31, field=32, view=(37, 41)),
        mask_upsample_rate=4,
        num_row_ref_point=2,
        inv_view_distance=5,
        length_scale=0,
        field_scale=None,
        with_hm=True,
        mask_sigma=0.5,
        mask_max_thr=0.0,
        th_len=10,
        v4_simplify=True,
        mask_ignores=30),
    gt_indices=dict(
        offset=(0, 2), field=(2, 4), length=4, weight=(5, 18), view=(15, 23)))
compose_params = dict(bboxes=False, keypoints=True, masks=True)
DIAMOND_KERNEL_5 = [[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],
                    [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]]
train_al_pipeline = [
    dict(
        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),
    dict(type='Resize', height=288, width=864, p=1)
]
val_al_pipeline = [
    dict(
        type='Compose', params=dict(bboxes=False, keypoints=True, masks=True)),
    dict(type='Resize', height=288, width=864, p=1)
]
train_pipeline = [
    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
    dict(
        type='pillar.Alaug',
        transforms=[
            dict(
                type='Compose',
                params=dict(bboxes=False, keypoints=True, masks=True)),
            dict(type='Resize', height=288, width=864, p=1)
        ]),
    dict(
        type='mmcls.Normalize',
        mean=[75.3, 76.6, 77.6],
        std=[50.5, 53.8, 54.3],
        to_rgb=False),
    dict(
        type='pillar.DepthCompletion',
        max_depth=100.0,
        custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1],
                       [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],
        extrapolate=False,
        blur_type='bilateral'),
    dict(
        type='pillar.CollectBilateralFiledLane',
        down_scale=4,
        hm_down_scale=16,
        property_down_rate=4,
        num_drop_remote_point=0,
        num_start_rows=3,
        bg_weight=0.01,
        soft_weight_len_alpha=0.5,
        soft_weight_off_alpha=0.5,
        soft_weight_x=True,
        soft_weight_y=True,
        draw_r=4,
        num_ref_point=2,
        inv_view_distance=5,
        max_mask_sample=4,
        line_width=4,
        radius=3,
        interval=5,
        filter_pts=1,
        endpoint='down',
        keys=[
            'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',
            'roadside_gt_hm', 'lane_masks', 'lane_label_mask',
            'lane_color_mask', 'roadside_masks', 'roadside_label_mask',
            'roadside_field', 'roadside_attribute_target', 'depth_map'
        ],
        meta_keys=[
            'filename', 'sub_img_name', 'mask_shape', 'hm_shape', 'ori_shape',
            'img_shape', 'down_scale', 'hm_down_scale', 'img_norm_cfg'
        ]),
    dict(type='pillar.DefaultFormatBundle')
]
val_pipeline = [
    dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
    dict(
        type='pillar.Alaug',
        transforms=[
            dict(
                type='Compose',
                params=dict(bboxes=False, keypoints=True, masks=True)),
            dict(type='Resize', height=288, width=864, p=1)
        ]),
    dict(
        type='mmcls.Normalize',
        mean=[75.3, 76.6, 77.6],
        std=[50.5, 53.8, 54.3],
        to_rgb=False),
    dict(
        type='pillar.CollectLkdClsLane',
        down_scale=4,
        hm_down_scale=16,
        radius=3),
    dict(type='pillar.DefaultFormatBundle')
]
data = dict(
    samples_per_gpu=8,
    workers_per_gpu=4,
    train=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.DepthCompletion',
                max_depth=100.0,
                custom_kernel=[[0, 0, 1, 0, 0], [0, 1, 1, 1,
                                                 0], [1, 1, 1, 1, 1],
                               [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]],
                extrapolate=False,
                blur_type='bilateral'),
            dict(
                type='pillar.CollectBilateralFiledLane',
                down_scale=4,
                hm_down_scale=16,
                property_down_rate=4,
                num_drop_remote_point=0,
                num_start_rows=3,
                bg_weight=0.01,
                soft_weight_len_alpha=0.5,
                soft_weight_off_alpha=0.5,
                soft_weight_x=True,
                soft_weight_y=True,
                draw_r=4,
                num_ref_point=2,
                inv_view_distance=5,
                max_mask_sample=4,
                line_width=4,
                radius=3,
                interval=5,
                filter_pts=1,
                endpoint='down',
                keys=[
                    'img', 'lane_gt_hm', 'lane_field', 'lane_attribute_target',
                    'roadside_gt_hm', 'lane_masks', 'lane_label_mask',
                    'lane_color_mask', 'roadside_masks', 'roadside_label_mask',
                    'roadside_field', 'roadside_attribute_target', 'depth_map'
                ],
                meta_keys=[
                    'filename', 'sub_img_name', 'mask_shape', 'hm_shape',
                    'ori_shape', 'img_shape', 'down_scale', 'hm_down_scale',
                    'img_norm_cfg'
                ]),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=False,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    val=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    test=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True))
evaluation = dict(
    type='mme.EvalHook',
    dataset=dict(
        type='pillar.LkdCropClsDataset',
        ann_file=
        '/home/<USER>/data/once_3d_lanes_data/updated_oncelane_data.json',
        img_prefix='/home/<USER>/data/once_3d_lanes_data/',
        pipeline=[
            dict(type='pillar.LoadLanes', ceph_cfg='', roi_method='crop'),
            dict(
                type='pillar.Alaug',
                transforms=[
                    dict(
                        type='Compose',
                        params=dict(bboxes=False, keypoints=True, masks=True)),
                    dict(type='Resize', height=288, width=864, p=1)
                ]),
            dict(
                type='mmcls.Normalize',
                mean=[75.3, 76.6, 77.6],
                std=[50.5, 53.8, 54.3],
                to_rgb=False),
            dict(
                type='pillar.CollectLkdClsLane',
                down_scale=4,
                hm_down_scale=16,
                radius=3),
            dict(type='pillar.DefaultFormatBundle')
        ],
        test_mode=True,
        camera_roi=dict(
            front_fov30=dict(t=0, b=1, l=0, r=1),
            front_fov120=dict(t=0, b=1, l=0, r=1)),
        categories=dict(
            group=['lane_type', 'lane_color', 'roadside'],
            attr=dict(
                lane_type=[
                    'NO_LANE', 'OTHER_LANE_TYPE', 'SOLID_LANE', 'DASHED_LANE',
                    'SHORT_THICK_DASHED_LANE', 'SPLIT_LANE', 'MERGE_LANE',
                    'LEFT_DASHED_RIGHT_SOLID', 'LEFT_SOLID_RIGHT_DASHED',
                    'DOUBLE_SOLID', 'DOUBLE_DASHED', 'FISHBONE_SOLID',
                    'FISHBONE_DASHED'
                ],
                lane_color=[
                    'NO_LANE', 'OTHER_LANE_COLOR', 'WHITE', 'YELLOW', 'ORANGE',
                    'BLUE'
                ],
                roadside=[
                    'NO_SIDE', 'ROAD_CURB_EDGE', 'FENCE_EDGE',
                    'WALL_EDGE_OR_PLANT_EDGE', 'MOVABLE_EDGE', 'OTHER_EDGE'
                ])),
        camera_filter=True),
    dataloader=dict(samples_per_gpu=16, workers_per_gpu=2),
    test_setting=dict(
        repo='pillar',
        single_gpu_test=dict(show=False),
        multi_gpu_test=dict(gpu_collect=True)),
    metric='accuracy',
    interval=2,
    by_epoch=True)
optimizer = dict(
    type='AdamW',
    lr=0.0001,
    weight_decay=0.0001,
    paramwise_cfg=dict(
        custom_keys=dict(
            backbone=dict(lr_mult=0.1),
            neck=dict(lr_mult=1.0),
            head=dict(lr_mult=1.0)),
        bias_decay_mult=0.0,
        norm_decay_mult=0.0))
optimizer_config = dict(grad_clip=dict(max_norm=3))
lr_config = dict(
    policy='CosineAnnealing',
    min_lr=1e-06,
    by_epoch=False,
    warmup='linear',
    warmup_iters=500,
    warmup_ratio=0.01,
    warmup_by_epoch=False)
runner = dict(type='mmcv.EpochBasedRunner', max_epochs=30)
checkpoint_config = dict(interval=1, by_epoch=True, max_keep_ckpts=1)
log_config = dict(interval=1, hooks=[dict(type='mmcv.TextLoggerHook')])
log_level = 'INFO'
auto_resume = False
resume_from = ''
load_from = ''
workflow = [('train', 200)]
work_dir = './condlane_train/'
gpu_ids = range(0, 1)
