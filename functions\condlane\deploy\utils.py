import os
import json
from argparse import ArgumentParser

import cv2
import numpy as np
import torch
from torch.nn import functional as F
import mme
from mmcv import Config

from pillar.apis import init_model
from .process import pre_process, parse_roi


def parse_args():
    parser = ArgumentParser(usage="""
    for dataset:
    python -m deploy todataset config.py /path/to/ckpt

    for deployment:
    python -m deploy tokestrel config.py /path/to/ckpt
    """)
    parser.add_argument(
        "cmd",
        default="tokestrel",
        choices=["tokestrel", "todataset", "vis_video"],
    )
    parser.add_argument("config", help="Config file")
    parser.add_argument("checkpoint", default=None, help="Checkpoint file")
    parser.add_argument(
        "-c",
        "--client_conf",
        default="/mnt/lustre/share/liuhao1/petreloss/petreloss_lane.conf",
        help="config for petrel client",
    )
    parser.add_argument("-r", "--random", type=bool, default=False)
    parser.add_argument(
        "--condconv",
        default="matmul",
        choices=["matmul", "muladd", "muladdsum"],
    )
    parser.add_argument("--crop", action="store_true")
    parser.add_argument("--maxpool", type=bool, default=True)
    parser.add_argument("--u8", action="store_true")
    parser.add_argument("--nhwc", action="store_true")
    parser.add_argument("-v", "--version", type=int, default=2, choices=[1, 2])
    parser.add_argument("--dup_i", type=int, default=1)
    parser.add_argument("-o", "--outdir", default="tokestrel")
    parser.add_argument("--video", default="")
    parser.add_argument("--camera", 
                        choices=['front_fov120', 'front_fov30', 'rear_fov60', 'rear_fov100'], 
                        default="front_fov120")
    args = parser.parse_args()
    if not os.path.exists(args.checkpoint):
        args.checkpoint = None
    return args


def get_data(config, client, crop=False, random=False, camera="front_fov120"):
    data_test = config.data.test
    print("data_test: ", data_test)
    img_prefix = data_test.get("img_prefix", data_test.get("dir_prefix", ""))
    ann_file = data_test.get("ann_file", data_test.get("data_root", ""))
    print("ann_file: ", ann_file) #new
    lines = open(ann_file).readlines()

    if random:
        np.random.shuffle(lines)
    batch_size = config.get("tokestrel", {}).get("batch_size", {}).get("image", 1)
    #print("batch_size: ", batch_size)
    def abspath(file):
        if "s3://" in file or file.startswith("/"):
            return file
        if len(img_prefix) <= 0 or file.startswith(img_prefix):
            return file
        return os.path.join(img_prefix, file)

    def relpath(file):
        if "s3://" in file:
            file = file.split("s3://", 1)[-1]
            file = file.split("/", 1)[-1]
        else:
            if len(img_prefix) <= 0:
                file = os.path.relpath(file, "/")
            else:
                file = os.path.relpath(file, img_prefix)
        return file

    data = []
    others = []
    for line in lines:
        line = json.loads(line) 
        print("line: ", line) #new
        if line.get('camera_index', 'front_fov120') != camera: continue #important
        fullpath = abspath(line["filename"])
        print("fullpath: ", fullpath) #new
        subpath = relpath(fullpath)
        print("subpath: ", subpath) #new
        if "s3://" in fullpath:
            img_bytes = client.get(fullpath)
            if img_bytes is None:
                raise FileNotFoundError(fullpath)
            img_array = np.frombuffer(img_bytes, np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            print("img: ", img) #new
        else:
            img = fullpath
        #print("img: ", img)
        print("config.camera_roi.camera: ", config.camera_roi.get(camera, {}))
        print("config.img_scale: ", config.img_scale)
        print("config.img_norm_cg.mean: ", config.img_norm_cfg.mean)
        print("config.img_norm_cfg.std: ", config.img_norm_cfg.std)
        print("crop: ", crop)
        image, info = pre_process(
            img,
            config.camera_roi.get(camera, {}),
            config.get("break_point_range", (0, 1)),
            config.img_scale,
            config.img_norm_cfg.mean,
            config.img_norm_cfg.std,
            not crop,
        )
        #print("=======> image.min(), image.max(): ", image.min(), image.max())
        data.append(image)
        others.append(info + [subpath])
        #print("load", fullpath)
        if len(data) == batch_size: break
    assert len(data), "No target camera in current data!"
    data = np.stack(data)
    return data, others


class WrapperModel(torch.nn.Module):

    def __init__(self, args, config):
        super().__init__()
        self.model_cfg = config
        self.checkpoint = args.checkpoint
        self.device = "cuda"
        self.set_model()
        self.roi = config.camera_roi.get(args.camera, {})
        self.with_crop = args.crop
        self.with_maxpool = args.maxpool
        self.dup_i = args.dup_i

    def set_model(self):
        mme.utils.DEFAULT_SCOPE = self.model_cfg.get('default_scope', None)

        # TODO: replace MMSyncBN with BN
        # import modules from string list.
        if self.model_cfg.get('custom_imports', None):
            from mmcv.utils import import_modules_from_strings
            import_modules_from_strings(**self.model_cfg['custom_imports'])
        # set cudnn_benchmark
        if self.model_cfg.get('cudnn_benchmark', False):
            torch.backends.cudnn.benchmark = True

        # build the model and load checkpoint
        self.model = init_model(self.model_cfg,
                                self.checkpoint,
                                device=self.device)

    def crop(self, image):
        #print("self.with_crop: ", self.with_crop)
        if not self.with_crop:
            return image
        weight = torch.eye(3).view(3, 3, 1, 1).cuda()
        image = F.conv2d(image, weight)
        H, W = image.shape[-2:]
        l, t, r, b = parse_roi(W, H, **self.roi)
        if 0 < t or b < H:
            image = image[..., t:b, :]
        if 0 < l or r < W:
            image = image[..., :, l:r]
        return image

    def cat_dup_i(self, data_dict):
        if self.dup_i <= 1:
            #print("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
            #print("self.dup_i=1: data_dict['image'].shape: ", data_dict["image"].shape)
            #print("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
            images = data_dict["image"]*1.0
            #images = torch.cat(images)
            print("images.shape: ", images.shape)
            from torchvision import transforms
            #print("images.min(), images.max():  ", images.min(), images.max())
            images = images.permute(0, 3, 1, 2)
            #print("images.shape: ", images.shape)
            images = transforms.Normalize(mean=[75.3, 76.6, 77.6], std=[50.5, 53.8, 54.3])(images)
            #return data_dict["image"]
            return images
        images = [data_dict["image"]]
        for i in range(1, self.dup_i):
            images.append(data_dict["image" + str(i + 1)])
            #images.append(None)
        images = torch.cat(images)*1.0
        #images = torch.cat(images)
        #print("images.shape: ", images.shape)
        from torchvision import transforms
        #print("images.min(), images.max():  ", images.min(), images.max())
        images = images.permute(0, 3, 1, 2)
        #print("images.shape: ", images.shape)
        images = transforms.Normalize(mean=[75.3, 76.6, 77.6], std=[50.5, 53.8, 54.3])(images)
        #print("images.min(), images.max():  ", images.min(), images.max())
        #print("images.shape: ", images.shape)
        #images = images.permute(0, 3, 1, 2)
        #print("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
        #print("self.dup_i>=1: images.shape: ", images.shape)
        #print("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
        return images

    def forward(self, *args, **kwargs):
        if self.tokestrel == "e2e":
            #print("------------------------------------e2e--------------------------------------------")
            #print("kwargs.keys(): ", kwargs)
            #print("args: ", args)
            image = self.cat_dup_i(kwargs)
            #print("image.shape: ",image.shape)
            cropped = self.crop(image)
            #print("----------------------------cropped.shape: ", cropped.shape)
            kwargs["image"] = cropped
            #import numpy as np
            #np.save("toskestrel_image.npy", kwargs["image"].cpu())
        if self.tokestrel in ["proposal", "net"]:
            #print("------------------------proposal, net-----------------------------------------")
            image = self.cat_dup_i(args[0])
            cropped = self.crop(image)
            args[0]["image"] = cropped
        outputs = self.model.forward_tokestrel(*args, **kwargs)
        #import numpy as np 
        #np.save("tokestrel_outputs.npy", outputs)
        if self.tokestrel in ["e2e", "proposal", "net"] and self.with_maxpool:
            if isinstance(outputs, dict):
                proposal = outputs
            else:
                proposal = outputs[0]
            if "proposal_heatmap" in outputs and "maxpool" not in proposal:
                """
                proposal["maxpool"] = F.max_pool2d(
                    proposal["proposal_heatmap"],
                    (3, 3),
                    stride=1,
                    padding=1,
                )
                """
                #print("proposal['proposal_heatmap'].min(): ", proposal["proposal_heatmap"].min(), "proposal['proposal_heatmap'].max(): ", proposal['proposal_heatmap'].max())
                #proposal["proposal_heatmap"] = F.relu6(proposal["proposal_heatmap"] + 3.0, inplace=True) / 6.0
                #print("proposal['proposal_heatmap'].min(): ", proposal["proposal_heatmap"].min(), "proposal['proposal_heatmap'].max(): ", proposal['proposal_heatmap'].max())
                hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(proposal["proposal_heatmap"].size(2))], 
                                         dtype=torch.float32).view(1, 1, proposal["proposal_heatmap"].size(2), 1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(24)], dtype=torch.float32).view(1, 1, 24, 1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(36)], dtype=torch.float32).view(1, 1, 36,1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(32)], dtype=torch.float32).view(1, 1, 32,1).cuda()
                proposal["maxpool"] = F.max_pool2d(
                    hm_weight * (F.relu6(proposal["proposal_heatmap"] + 3.0, inplace=False) / 6.0),
                    (3, 3),
                    stride=1,
                    padding=1,
                )
                z_1 = F.relu6(proposal["proposal_heatmap"] + 3.0, inplace=False)/6.0
                z_2 = z_1 * hm_weight
                z_3 = (z_2 >= proposal["maxpool"]).float() * z_2
                z_4 = z_3 / hm_weight
                proposal["heat_nms_weight_by_relu6_proposal_heatmap"] = z_4
                



                #z_55 = torch.tensor(np.where(z_4.cpu().numpy() >0.6))
                #print("z_55: ", [i.shape for i in z_55])
                #proposal["proposal_heatmap_inds"] = z_55 # torch.cat([torch.unsqueeze(i, dim=0) for i in z_55])
                #print("proposal['proposal_heatmap_inds'].shape: ", proposal["proposal_heatmap_inds"].shape)
                #proposal["proposal_heatmap_inds"] = torch.stack(z_5)
                #proposal["proposal_heatmap_inds"] = {"zkx": [z_55]}
                """
                weight_by_relu6_proposal_heatmap = hm_weight * (F.relu6(proposal["proposal_heatmap"] + 3.0, inplace=False) /6.0)
                heat_nms_weight_by_relu6_proposal_heatmap = (weight_by_relu6_proposal_heatmap >= proposal["maxpool"]).float() * weight_by_relu6_proposal_heatmap / hm_weight
                heat_nms_weight_by_relu6_proposal_heatmap = heat_nms_weight_by_relu6_proposal_heatmap.detach().cpu().numpy()
                proposal["heat_nms_weight_by_relu6_proposal_heatmap"] = torch.tensor(heat_nms_weight_by_relu6_proposal_heatmap)
                
                #proposal["proposal_heatmap_inds"] = torch.stack([i for i in torch.where(torch.tensor(heat_nms_weight_by_relu6_proposal_heatmap) > 0.6)])
                proposal["proposal_heatmap_inds"] = torch.stack( [i for i in torch.where(proposal["heat_nms_weight_by_relu6_proposal_heatmap"] > 0.6)] )
                print("proposal['heat_nms_weight_by_relu6_proposal_heatmap'].shape: ", proposal["heat_nms_weight_by_relu6_proposal_heatmap"].shape)
                print(proposal["heat_nms_weight_by_relu6_proposal_heatmap"][0, 0, :10, :10])
                print("proposal['proposal_heatmap_inds'].shape: ", proposal["proposal_heatmap_inds"].shape)
                """
            if "line_mask" in proposal and "mask_maxpool" not in proposal:
                """
                proposal["mask_maxpool"] = F.max_pool2d(
                    proposal["line_mask"],
                    (3, 3),
                    stride=1,
                    padding=1,
                )
                """
                #proposal["line_mask"] = F.relu6(proposal["line_mask"] + 3.0, inplace=True)/ 6.0
                hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(proposal["line_mask"].size(2))], 
                                         dtype=torch.float32).view(1, 1, proposal["line_mask"].size(2), 1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(96)],
                 #                        dtype=torch.float32).view(1, 1, 96,1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(144)],
                 #                   dtype=torch.float32).view(1, 1, 144,1).cuda()
                #hm_weight = torch.tensor([(i + 1) * (i + 1) for i in range(126)],
                #                    dtype=torch.float32).view(1, 1, 126,1).cuda()
                proposal["mask_maxpool"] = F.max_pool2d(
                    hm_weight *(F.relu6(proposal["line_mask"] + 3.0, inplace=False)/ 6.0),
                    (3, 3),
                    stride=1,
                    padding=1,
                )
                z_6 = F.relu6(proposal["line_mask"] + 3.0, inplace=True)/6.0
                z_7 = hm_weight * z_6
                z_8 = (z_7 >= proposal["mask_maxpool"]).float()
                z_9 = z_8 * z_7
                z_10 = z_9 / hm_weight
                proposal["heat_nms_weight_by_relu6_line_mask"] = z_10
                





                #z_11 = torch.tensor(np.where(z_10.cpu().numpy()>0.6))
                #proposal["line_mask_inds"] = torch.stack(z_11)
                #proposal["line_mask_inds"] = {"lyq": [z_11]}
                #print("z_11: ", [i.shape for i in z_11])
                #proposal["line_mask_inds"] = z_11 #torch.cat([torch.unsqueeze(i, dim=0) for i in z_11])
                #print("proposal['line_mask_inds'].shape: ", proposal["line_mask_inds"].shape)
                """ 
                weight_by_relu6_line_mask = hm_weight * (F.relu6(proposal["line_mask"] + 3.0, inplace=False) /6.0)
                heat_nms_weight_by_relu6_line_mask = (weight_by_relu6_line_mask >= proposal["mask_maxpool"]).float() * weight_by_relu6_line_mask / hm_weight
                heat_nms_weight_by_relu6_line_mask = heat_nms_weight_by_relu6_line_mask.detach().cpu().numpy()
                proposal["heat_nms_weight_by_relu6_line_mask"] = torch.tensor(heat_nms_weight_by_relu6_line_mask)
                #proposal["heat_nms_weight_by_relu6_proposal_heatmap"] = torch.tensor(heat_nms_weight_by_relu6_proposal_heatmap)
                proposal["line_mask_inds"] = torch.stack( [i for i in torch.where(proposal["heat_nms_weight_by_relu6_line_mask"] > 0.6)] )
                
                #proposal["line_mask_inds"] = torch.where(torch.tensor(heat_nms_weight_by_relu6_line_mask) > 0.6)
                """
            
            z_12 = torch.exp(-proposal["proposal_offset"])
            z_13 = torch.exp(proposal["proposal_offset"])
            z_14 =  torch.where(proposal["proposal_offset"] >=0 , 1/(1+z_12), z_13/(1+z_13))
            proposal["sigmoid_proposal_offset"] = z_14 * 2 - 1
            z_15 = torch.exp(-proposal["line_mask"])
            z_16 = torch.exp(proposal["line_mask"])
            z_17 = torch.where(proposal["line_mask"] >=0, 1/(1+z_15), z_16/(1+z_16))
            proposal["sigmoid_line_mask"] = z_17
            z_18 = proposal["laneline_field"]
            z_18_h, z_18_w = z_18.shape[-2:]
            z_18_num_row_point = z_18_w * 2
            proposal["laneline_vec_field"] = z_18.reshape(2, -1, 2, z_18_h, z_18_w).permute(0, 1, 3, 4, 2).reshape(2, -1, z_18_h, z_18_num_row_point) #batch_size=2
            #proposal["laneline_vec_field"] = z_18.reshape(1, -1, 2, z_18_h, z_18_w).permute(0, 1, 3, 4, 2).reshape(1, -1, z_18_h, z_18_num_row_point)
            z_19 = proposal["roadside_field"]
            z_19_h, z_19_w = z_19.shape[-2:]
            z_19_num_row_point = z_19_w * 2
            proposal["roadside_vec_field"] = z_19.reshape(2, -1, 2, z_19_h, z_19_w).permute(0, 1, 3, 4, 2).reshape(2, -1, z_19_h, z_19_num_row_point) #batch_size=2
            #proposal["roadside_vec_field"] = z_19.reshape(1, -1, 2, z_19_h, z_19_w).permute(0, 1, 3, 4, 2).reshape(1, -1, z_19_h, z_19_num_row_point)
            z_20 = proposal["laneline_inv_vector_field"]
            z_20_h, z_20_w = z_20.shape[-2:]
            z_20_num_row_point = z_20_w *2
            proposal["field_pass_simp_laneline_inv_vector_field"] = z_20.reshape(2, -1, 2, z_20_h, z_20_w).permute(0, 1, 3, 4, 2).reshape(2, -1, z_20_h, z_20_num_row_point) #batch_size=2
            #proposal["field_pass_simp_laneline_inv_vector_field"] = z_20.reshape(1, -1, 2, z_20_h, z_20_w).permute(0, 1, 3, 4, 2).reshape(1, -1, z_20_h, z_20_num_row_point)
            z_21 = proposal["roadside_length"]
            z_21_h = z_21.shape[-2]
            z_22 = z_21 + 3.0
            z_23 = F.relu6(z_22, inplace=False)
            z_24 = z_23 /6.0
            proposal["scale_length_roadside_length"] = z_24 * z_21_h
            del proposal["roadside_field"]
            del proposal["roadside_length"]
            del proposal["laneline_inv_vector_field"]
            del proposal["proposal_heatmap"]
            del proposal["proposal_offset"]
            del proposal["maxpool"]
            del proposal["mask_maxpool"]
            del proposal["line_mask"]
            #del proposal["laneline_field"]
        return outputs


def get_model(args):
    config = Config.fromfile(args.config)
    model = WrapperModel(args, config)
    model.eval()
    return model, config


def model_infer(model, batch, dup_i, *args, **kwargs):
    #import numpy as np
    #np.save("model_infer_batch.npy", batch)
    #print("1111111111111111111111122222222222222222, batch: ", batch)
    proposal_i = {"image": torch.from_numpy(batch).cuda()}
    #np.save("model_infer_proposal_i.npy", proposal_i)
    for i in range(1, dup_i):
        proposal_i["image" + str(i + 1)] = proposal_i["image"]
    proposal_o = model(*args, **kwargs, **proposal_i)
    #print("##################################################################")
    #print("proposal_o: ", [(i, proposal_o[i].shape) for i in proposal_o])
    #print("############################################################")
    outputs = {
        "proposal": {
            "input": proposal_i,
            "output": proposal_o,
        }
    }
    if not isinstance(proposal_o, dict):
        proposal_o, detect_i, detect_o = proposal_o
        outputs["proposal"]["output"] = proposal_o
        outputs["detect"] = {
            "input": detect_i,
            "output": detect_o,
        }
    else:
        outputs = {"net": outputs["proposal"]}
    return outputs
