import subprocess
import time
import os

def run_script(script_name):
    try:
        result = subprocess.run(['python3', script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while running {script_name}")
        raise
        
if __name__ == "__main__":
    try:
        print("Renaming json files..")
        run_script('src/1_rename_json.py')
        
        time.sleep(2)
        
        print("Combining jsons into a single file..")
        run_script('src/2_combined_output_json.py')
        
        time.sleep(4)
        
        print("Removing duplicate lines..")
        run_script('src/3_rem_dup_lines.py')
        
        time.sleep(4)
        
        print("Reformatting results structure..")
        run_script('src/4_reformat_comb_json_rr.py')
        
        time.sleep(4)
        
        print("Filtering ground_truth.txt file..")
        run_script('src/5_filter_gts_file.py')
        
        time.sleep(4)
        
        print("Computing performance metrics..")
        run_script('src/6_compute_metrics_with_gt.py')
        
        print("Cleaning files..")
        files = os.listdir('./cpp_res')
        for file in files:
            path = os.path.join('./cpp_res', file)
            #os.remove(path)
            #print(f"Deleted {path}")
        #print("Deleted ./cpp_res")
        os.remove('eval_files/filtered_ground_truth.txt')
        print("Deleted eval_files/filtered_ground_truth.txt")
        os.remove('eval_files/reformatted_cpp_results.txt')
        print("Deleted reformatted_cpp_results.txt")
        os.remove('cleaned_cpp_results_with_images.json')
        print("Deleted cleaned_cpp_results_with_images.json")
        os.remove('combined_cpp_results_with_images.json')
        print("Deleted combined_cpp_results_with_images.json")
        
        
    except Exception as e:
        print(f"An error occured: {e}")
        