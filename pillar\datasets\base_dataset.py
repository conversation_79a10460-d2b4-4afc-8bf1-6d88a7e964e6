from torch.utils.data import Dataset
from mme.datasets.pipelines import <PERSON>mpose

from .builder import DATASETS


@DATASETS.register_module()
class BaseDataset(Dataset):
    """Base dataset.

    Args:
        pipeline (list[dict]): Processing pipeline.
    """

    CLASSES = None

    PALETTE = None

    def __init__(self, pipeline, **kwargs):
        super().__init__()

        self.pipeline = Compose(pipeline)
        data_infos = kwargs.pop('data_infos', None)
        if data_infos is None:
            data_infos = self.load_annotations()
        assert isinstance(data_infos, (tuple, list)), \
            f'invalid type of data_infos: {type(data_infos)}'
        self.data_infos = data_infos

    def load_annotations(self):
        raise NotImplementedError

    def prepare_data(self, idx):
        data_info = self.data_infos[idx].copy()
        return self.pipeline(data_info)

    def __len__(self):
        return len(self.data_infos)

    def __getitem__(self, idx):
        return self.prepare_data(idx)

    def evaluate(self):
        raise NotImplementedError
