import ipdb
import numpy as np

from .condlane_post_processor import CondLanePostProcessor


def sigmoid(z):
    return np.where(z >= 0, 1 / (1 + np.exp(-z)), np.exp(z) / (1 + np.exp(z)))


def int_x(x, w):
    x = int(round(x))
    x = np.clip(x, 0, w - 1)
    return x


def gather_row(row, x_i, kernel=3, reduce=np.mean):
    W = row.shape[-1]
    values = [row[x_i]] * kernel
    for dx in range(1, 1 + int(kernel // 2)):
        if x_i + dx < W:
            values[+dx] = row[x_i + dx]
        if x_i - dx >= 0:
            values[-dx] = row[x_i - dx]
    if reduce is None:
        return values
    return reduce(values)


# def gather_x(offset, x_i, radius=3):
#     W = offset.shape[-1]
#     visit = set()

#     start_i = np.clip(x_i, radius, W - 1 - radius) - radius
#     stop_i = start_i + radius * 2
#     xs = np.arange(start_i, stop_i + 1)
#     x_f = np.mean(xs + offset[xs])
#     while (start_i, stop_i) not in visit:
#         visit.add((start_i, stop_i))
#         off = x_f - x_i
#         x_i = int_x(x_f, W)

#         sgn = 1 if off >= 0 else -1
#         x_i = x_i + sgn
#         while 0 <= x_i < W and 0 < (offset[x_i] * sgn) < 1:
#             x_i = x_i + sgn

#         start_i = np.clip(x_i, radius, W - 1 - radius) - radius
#         stop_i = start_i + radius * 2
#         xs = np.arange(start_i, stop_i + 1)
#         x_f = np.mean(xs + offset[xs])

#     return x_f


def gather_x(offset, x_i, radius=1):
    W = offset.shape[-1]
    visit = set()

    start_i = np.clip(x_i, radius, W - 1 - radius) - radius
    stop_i = start_i + radius * 2
    xs = np.arange(start_i, stop_i + 1)
    x_f = np.mean(xs + offset[xs])
    while (start_i, stop_i) not in visit:
        visit.add((start_i, stop_i))
        off = x_f - x_i
        x_i = int_x(x_f, W)

        sgn = 1 if off >= 0 else -1
        x_i = x_i + sgn
        while 0 <= x_i < W and 0 < (offset[x_i] * sgn) < 1:
            x_i = x_i + sgn

        start_i = np.clip(x_i, radius, W - 1 - radius) - radius
        stop_i = start_i + radius * 2
        xs = np.arange(start_i, stop_i + 1)
        x_f = np.mean(xs + offset[xs])

    return x_f


def smooth_cut(points, th=4):
    if len(points) < 4:
        return points
    pts = np.array(points)
    xs = pts[:, 0] + 0
    xs[1:] += pts[:-1, 0]
    xs[:-1] += pts[1:, 0]
    xs[1:-1] /= 3
    xs[0] = pts[0, 0]
    xs[-1] = 2 * pts[-2, 0] - pts[-3, 0]
    off = np.abs(pts[:, 0] - xs)
    while True:
        L = len(off)
        mu = np.mean(off)
        sigma = np.std(off)
        th_high = max(mu + sigma, th)
        th_low = max(mu, th)
        max_off = th_high
        for i in range(L - 1, 0, -1):
            max_off = max(max_off, off[i])
            if off[i - 1] <= th_low and max_off > th_high:
                off = off[:i]
                break
        if len(off) < 4 or len(off) >= L:
            return points[:len(off)]


def mono_step(x_i, y_i, off_field, vec_field=None, dy=-1):
    H, W = off_field.shape[-2:]
    # new row
    if vec_field is not None:
        x_f = x_i + gather_row(vec_field[y_i], x_i)
        x_i = int_x(x_f, W)
    y_i = y_i + dy
    # fix row
    x_f = gather_x(off_field[y_i], x_i)
    return [x_f, y_i]


def check_len(points, th_len, th_kps_min, th_kps_max):
    if len(points) < th_kps_min:
        return False
    if len(points) >= th_kps_max:
        return True
    line_len = np.linalg.norm(points[:-1] - points[1:], 2, -1).sum()
    return line_len >= th_len


class PointNode:

    def __init__(self, score, x_f, x_i, y_i):
        self.score = score
        self.x_f = x_f
        self.x_i = x_i
        self.y_i = y_i

    @property
    def data(self):
        return [self.x_f, self.y_i]

    def __repr__(self):
        return str(self.data)


def mask2points(
    mask,
    off_field,
    th_score=0.5,
    th_nms=1,
):
    H, W = mask.shape[-2:]
    W2 = off_field.shape[-1]
    super_ratio = W2 / W
    nms = mask
    for _ in range(th_nms):
        nms[:, 1:] = np.where(nms[:, 1:] >= nms[:, :-1], nms[:, 1:], 0)
        nms[:, :-1] = np.where(nms[:, :-1] >= nms[:, 1:], nms[:, :-1], 0)
    ys, xs = np.where(nms > th_score)
    points = [{} for _ in range(H)]
    for x_i, y_i in zip(xs, ys):
        x_f = gather_x(off_field[y_i], int(x_i * super_ratio))
        x_i = int_x(x_f, W2)
        x_i_h = int_x(x_f / super_ratio, W)
        points[y_i][x_i] = PointNode(mask[y_i, x_i_h], x_f, x_i, y_i)
    for y_i in range(H):
        if len(points[y_i]) <= 1:
            continue
        nodes = sorted(points[y_i].values(), key=lambda _: _.score)
        keep = []
        while len(nodes) > 0:
            keep.append(nodes.pop())
            x_i = keep[-1].x_i
            th = th_nms * super_ratio
            nodes = [node for node in nodes if abs(x_i - node.x_i) > th]
        points[y_i] = {node.x_i: node for node in keep}
    return points


def mono_walk(
    x_f,
    y_i,
    off_field,
    len_field,
    visit,
    off_forward=None,
    off_backward=None,
    dy=-1,
    th=2,
):
    H, W2 = off_field.shape[-2:]
    W = len_field.shape[-1]
    super_ratio = W2 / W
    line = [[x_f, y_i]]
    x_i = int_x(x_f, W2)
    x_i_h = int_x(x_f / super_ratio, W)
    len_cur = gather_row(len_field[y_i], x_i_h)
    while 0 <= y_i + dy < H and len_cur >= 1:
        # step to next row
        if off_forward is None and len(line) > 1:
            x_i = int_x(2 * line[-1][0] - line[-2][0], W2)
        next_step = mono_step(
            x_i,
            y_i,
            off_field,
            off_forward,
            dy=dy,
        )
        x_f, y_i = next_step
        x_i = int_x(x_f, W2)
        if 0 > x_i or x_i >= W2:
            break
        if visit[y_i, x_i] > 0:
            break
        # step back to current row
        if off_backward is not None:
            back_step = mono_step(
                x_i,
                y_i,
                off_field,
                off_backward,
                dy=-dy,
            )
            off = abs(x_f - line[-1][0])
            if abs(back_step[0] - line[-1][0]) > min(th, off):
                break
        line = line + [next_step]
        # row len
        x_i_h = int_x(x_f / super_ratio, W)
        len_cur = gather_row(len_field[y_i], x_i_h)
    line = smooth_cut(line)
    return line


def bi_walk(
    points,
    off_field,
    len_forward,
    len_backward,
    off_forward=None,
    off_backward=None,
    th_dis=2,
    th_nms=2,
    th_len=1 / 8,
):
    H, W2 = off_field.shape[-2:]
    W = len_forward.shape[-1]
    super_ratio = W2 / W
    visit = np.zeros([H, W2], dtype=np.uint8)
    lines = []
    for y_i in range(H - 1, -1, -1):
        pts = sorted(points[y_i].values(), key=lambda _: _.score, reverse=True)
        for pt in pts:
            if visit[pt.y_i, pt.x_i] > 0:
                continue
            line_prev = mono_walk(
                pt.x_f,
                pt.y_i,
                off_field,
                len_forward,
                visit,
                off_forward,
                off_backward,
                dy=-1,
                th=th_dis,
            )
            line_post = mono_walk(
                pt.x_f,
                pt.y_i,
                off_field,
                len_backward,
                visit,
                off_backward,
                off_forward,
                dy=+1,
                th=th_dis,
            )
            line = np.array(line_prev[::-1] + line_post[1:])
            if line.shape[0] < 2:
                continue
            pts = line / [super_ratio, 1]
            if not check_len(pts, th_len, 5, 35):
                continue
            lines.append(pts)
            visit = update_visit(visit, line, +1, th_nms)
    return lines


def update_visit(visit, line, value=+1, th=2):
    H, W = visit.shape[-2:]
    L = len(line)
    starts = [x - th for x, y in line]
    stops = [x + th for x, y in line]
    for i, (x, y) in enumerate(line):
        y = int(y)
        start = starts[i]
        stop = stops[i]
        if i > 0:
            start = min(start, starts[i - 1])
            stop = max(stop, stops[i - 1])
        if i < L - 1:
            start = min(start, starts[i + 1])
            stop = max(stop, stops[i + 1])
        start = max(int(np.floor(start)), 0)
        stop = min(int(np.ceil(stop)), W - 1)
        visit[y, start:stop + 1] += value
        if i == 0 and y > 0:
            visit[y - 1, start:stop + 1] += value
        if i == L - 1 and y < H - 1:
            visit[y + 1, start:stop + 1] += value
    return visit


def mask2lines(
    line_mask,
    off_field,
    len_forward,
    len_backward,
    off_forward=None,
    off_backward=None,
    th_score=0.5,
    th_nms=2,
    th_dis=8,
    th_len=1 / 8,
    **kwargs,
):
    W2 = off_field.shape[-1]
    W = line_mask.shape[-1]
    super_ratio = W2 / W
    points = mask2points(
        line_mask,
        off_field,
        th_score=th_score,
        th_nms=th_nms,
    )
    lines = bi_walk(
        points,
        off_field,
        len_forward,
        len_backward,
        off_forward,
        off_backward,
        th_dis=th_dis * super_ratio,
        th_nms=th_nms * super_ratio,
        th_len=th_len,
    )
    return lines


class CondLanePostProcessorBeta(CondLanePostProcessor):

    def gather_attr(self, points, attr_field, ratio=1):
        H, W = attr_field.shape[-2:]
        x = points[:, 0] / ratio
        y = points[:, 1] / ratio
        x = np.clip(x.astype(np.int), 0, W - 1)
        y = np.clip(y.astype(np.int), 0, H - 1)
        attr_scores = attr_field[..., y, x]
        l = attr_scores.shape[-1]
        return attr_scores[..., int(0.25 * l):int(0.75 * l)].mean(-1)

    def mask2lines(self, field, attributes, label, **kwargs):
        lanes = []
        N = attributes.shape[0]
        for n in range(N):
            field_n = {k: v[n] for k, v in field.items()}
            lines = mask2lines(
                **field_n,
                **kwargs,
            )
            lines = [_ + self.pos_fix for _ in lines]
            for points in lines:
                score = self.gather_attr(points, field_n["line_mask"])
                attr_scores = self.gather_attr(
                    points,
                    attributes[n],
                    self.mask_upsample_rate,
                )
                lane = dict(
                    image_id=n,
                    label_id=label,
                    key_points=points,
                    confidence=float(score),
                    attribute=self.attr_group(attr_scores),
                )
                lanes.append(lane)
        return lanes

    def reshape_field(self, x):
        N, _, H, W = x.shape
        x = x.reshape(N, -1, self.num_row_ref_point, H, W)
        x = x[:, 0]
        x = x.transpose(0, 2, 3, 1)
        x = x.reshape(N, H, -1)
        x = x * self.num_row_ref_point
        return x

    def scale_length(self, x):
        x = x.squeeze(1)
        H = x.shape[-2]
        if self.length_scale is not None:
            x = np.clip(x * (H / self.length_scale), 0)
        else:
            x = sigmoid(x) * H
        return x

    def __call__(self, proposal_indices, cls_score, output):
        # laneline = {
        #     k.replace("laneline_", ""): self.reshape_field(v)
        #     for k, v in output.items() if k.startswith("laneline_")
        # }
        # laneline["line_mask"] = sigmoid(laneline["line_mask"])
        laneline = dict(
            line_mask=sigmoid(output["line_mask"][:, 0]),
            off_field=self.reshape_field(output["laneline_offset"]),
            # off_forward=self.reshape_field(output["laneline_field"]),
            # off_backward=self.reshape_field(
            #     output["laneline_inv_vector_field"]),
            len_forward=self.scale_length(output["laneline_length"]),
            len_backward=self.scale_length(output["laneline_inv_length"]),
        )
        lanelines = self.mask2lines(
            laneline,
            output["attributes"],
            0,
            **dict(
                th_score=0.5,
                th_nms=2,
                th_dis=8,
                th_len=30,
            ),
        )
        # roadside = {
        #     k.replace("roadside_", ""): self.reshape_field(v)
        #     for k, v in output.items() if k.startswith("roadside_")
        # }
        # roadside["line_mask"] = sigmoid(roadside["line_mask"])
        roadside = dict(
            line_mask=sigmoid(output["line_mask"][:, 1]),
            off_field=self.reshape_field(output["roadside_offset"]),
            # off_forward=self.reshape_field(output["roadside_field"]),
            # off_backward=self.reshape_field(
            #     output["roadside_inv_vector_field"]),
            len_forward=self.scale_length(output["roadside_length"]),
            len_backward=self.scale_length(output["roadside_inv_length"]),
        )
        roadsides = self.mask2lines(
            roadside,
            output["attributes"],
            1,
            **dict(
                th_score=0.5,
                th_nms=5,
                th_dis=8,
                th_len=30,
            ),
        )
        if self.nms_thr > 0:
            roadsides = self.line_nms(roadsides)
            lanelines = self.line_nms(lanelines)
        return lanelines + roadsides
