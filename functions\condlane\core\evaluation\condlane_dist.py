import cv2
import numpy as np

from .fastdtw.fastdtw import fastdtw
from .condlane_draw import draw_match

MAX_DIST = 1e+9


def count_distance(pred, list_y, list_x, start_y, end_y, bias):
    all_dis = []
    miss_points = 0
    select_y = list_y[(list_y <= start_y) & (list_y >= end_y)]
    select_x = list_x[np.where((list_y <= start_y) & (list_y >= end_y))]
    list_y_unique = np.unique(select_y)
    for y in list_y_unique:
        idx = np.where(select_y == y)
        x = select_x[idx[0]]
        # x_sort = np.sort(x)
        x_half = x[len(x) // 2]
        pp = pred[y, x_half - bias:x_half + bias]
        px = np.where(pp)
        if len(px[0]):
            dis = abs(bias - np.mean(px))
            all_dis.append(dis)
        else:
            miss_points += 1
    return all_dis, miss_points


def lane_distance(pred, gt):
    min_x = []
    for gt_region in gt:
        list_y, list_x = np.where(gt_region)
        idx = np.argmax(list_y)
        min_x.append(list_x[idx])
    _, h, w = gt.shape
    # select mid lane
    bias = [abs(w // 2 - i) for i in min_x]
    sort_id = np.argsort(bias)
    select_gt = gt[sort_id[:2]]

    ret = dict(
        close=dict(dist=[], miss=0),
        mid=dict(dist=[], miss=0),
        far=dict(dist=[], miss=0),
    )
    pred = np.any(pred, 0)
    for gt_region in select_gt:
        list_y, list_x = np.where(gt_region)
        if len(list_y) > 0:
            close_dis, close_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h,
                h * 5 // 6,
                20,
            )
            ret["close"]["dist"].extend(close_dis)
            ret["close"]["miss"] += close_miss_points

            mid_dis, mid_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h * 5 // 6,
                h * 4 // 6,
                10,
            )
            ret["mid"]["dist"].extend(mid_dis)
            ret["mid"]["miss"] += mid_miss_points

            far_dis, far_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h * 4 // 6,
                h * 3 // 6,
                6,
            )
            ret["far"]["dist"].extend(far_dis)
            ret["far"]["miss"] += far_miss_points

    return ret


def distance_bias(gt_map, pred_map, vrange, step=2):
    total_dis = []
    if not vrange.any():
        return total_dis
    min_y, max_y = np.where(vrange)[0][[0, -1]]
    for st in range(min_y + step, max_y - step, step):
        gt_x = np.where(gt_map[st])[0]
        if len(gt_x) <= 0:
            continue
        gt_x = np.mean(gt_x)
        p1 = (np.mean(np.where(gt_map[st - step // 2])), st - step // 2)  # x,y
        p2 = (np.mean(np.where(gt_map[st + step // 2])), st + step // 2)  # x,y
        ratio = (p1[1] - p2[1]) / (p1[0] - p2[0] + 1e-5)
        if np.isnan(ratio):
            continue
        if ratio > 2 or ratio < -2:
            pred_x = np.where(pred_map[st])[0]
            if len(pred_x) <= 0:
                continue
            pred_x = np.mean(pred_x)
            dis = abs(gt_x - pred_x)
            total_dis.append([dis, st])
        else:
            for y in range(st - step // 2, st + step // 2):
                x = gt_x - ratio * (y - st)
                if x >= gt_map.shape[1] or x < 0 or np.isnan(x):
                    continue
                if pred_map[y][int(x)] > 0:
                    dis = np.sqrt((y - st)**2 + (x - gt_x)**2)
                    total_dis.append([dis, y])
    if len(total_dis) <= 0:
        return total_dis
    total_dis = np.array(total_dis)
    arg = np.argsort(total_dis[:, 1])
    total_dis = total_dis[arg[::-1], 0]
    return total_dis


def calc_bias(preds, gts):
    M, N = len(preds), len(gts)
    dists = np.zeros([M, N, 4])
    pred_range = preds.any(-1)
    gt_range = gts.any(-1)
    for i, pred in enumerate(preds):
        for j, gt in enumerate(gts):
            vrange = pred_range[i] & gt_range[j]
            dist = distance_bias(gt, pred, vrange, step=10)
            if len(dist) <= 0:
                dists[i, j] = MAX_DIST
            else:
                if len(dist) < 6:
                    dists[i, j] = np.mean(dist)
                else:
                    L = len(dist) // 3
                    dist = [dist, dist[:L], dist[L:-L], dist[-L:]]
                    dist = list(map(np.mean, dist))
                    dists[i, j] = dist
    return dists


def calc_iou(pred_maps, gt_maps):
    inter = np.sum(pred_maps[:, None] & gt_maps[None, :], (2, 3))
    union = np.sum(pred_maps[:, None] | gt_maps[None, :], (2, 3))
    iou_mat = inter.astype(np.float) / union
    return iou_mat


def calc_dtw(preds, gts, roi=None):
    M, N = len(preds), len(gts)
    dists = np.zeros([M, N, 4])
    if roi is not None:
        H, W = roi["height"], roi["width"]
        vis = np.zeros([M, N, H, W, 3], dtype=np.uint8)
    for i, pred in enumerate(preds):
        for j, gt in enumerate(gts):
            dist, path = fastdtw(pred, gt, dist=2)
            if len(dist) < 6:
                dists[i, j] = np.mean(dist)
            else:
                L = len(dist) // 3
                dist = [dist, dist[:L], dist[L:-L], dist[-L:]]
                dist = list(map(np.mean, dist))
                dists[i, j] = dist
            if roi is not None:
                draw_match(vis[i, j], pred, gt, path, 4)
    if roi is not None:
        vis = np.transpose(vis, [0, 2, 1, 3, 4]).reshape(M * H, N * W, 3)
        cv2.imwrite("dtw.png", vis)
    return dists
