import os
import json
from copy import deepcopy
import numpy as np
from scipy.interpolate import interp1d
from scipy.optimize import linear_sum_assignment
from functools import partial
from multiprocessing import Pool
from tqdm import tqdm
import cv2
from sklearn.metrics import confusion_matrix
from prettytable import PrettyTable, MSWORD_FRIENDLY

MAX_DIST = 1e+9

def reconstruct_results(out_dir):
    results = {"gt": [], "pred": []}
    
    gt_path = os.path.join(out_dir, "ground_truth_batch2_cropped_b3e2.txt")	#change
    pred_path = os.path.join(out_dir, "runner_result864_288.txt") #change

    with open(gt_path, "r") as f:
        gt_list = []
        for line in f:
            gt_list.append(json.loads(line.strip()))
        results["gt"].append(gt_list)
    
    with open(pred_path, "r") as f:
        pred_list = []
        for line in f:
            pred_list.append(json.loads(line.strip()))
        results["pred"].append(pred_list)
    
    return results

def parse_results(filepath, pkl_key="gt"):
    if isinstance(filepath, str):
        if filepath.endswith(".pkl"):
            pkl = pickle.load(open(filepath, "rb"))
            lines = []
            for _ in pkl:
                lines.extend(_[pkl_key])
        else:
            lines = [json.loads(_) for _ in open(filepath)]
    else:
        lines = filepath
    return lines

def draw_lines(img, lane, color, width):
    for i in range(len(lane) - 1):
        cv2.line(img, tuple(lane[i]), tuple(lane[i + 1]), color, width)
    return img

def draw_maps(lanes, roi, width, scale):
    h = int(np.ceil(roi["height"] / scale))
    w = int(np.ceil(roi["width"] / scale))
    maps = np.zeros((len(lanes), h, w), dtype=np.uint8)
    for idx, points in enumerate(lanes):
        points = (points / scale).astype(np.int32).tolist()
        draw_lines(maps[idx], points, 1, width)
    return maps.astype(np.bool_)

def distance_bias(gt_map, pred_map, vrange, step=2):
    total_dis = []
    if not vrange.any():
        return total_dis
    min_y, max_y = np.where(vrange)[0][[0, -1]]
    for st in range(min_y + step, max_y - step, step):
        gt_x = np.where(gt_map[st])[0]
        if len(gt_x) <= 0:
            continue
        gt_x = np.mean(gt_x)
        p1 = (np.mean(np.where(gt_map[st - step // 2])), st - step // 2)  # x,y
        p2 = (np.mean(np.where(gt_map[st + step // 2])), st + step // 2)  # x,y
        ratio = (p1[1] - p2[1]) / (p1[0] - p2[0] + 1e-5)
        if np.isnan(ratio):
            continue
        if ratio > 2 or ratio < -2:
            pred_x = np.where(pred_map[st])[0]
            if len(pred_x) <= 0:
                continue
            pred_x = np.mean(pred_x)
            dis = abs(gt_x - pred_x)
            total_dis.append([dis, st])
        else:
            for y in range(st - step // 2, st + step // 2):
                x = gt_x - ratio * (y - st)
                if x >= gt_map.shape[1] or x < 0 or np.isnan(x):
                    continue
                if pred_map[y][int(x)] > 0:
                    dis = np.sqrt((y - st)**2 + (x - gt_x)**2)
                    total_dis.append([dis, y])
    if len(total_dis) <= 0:
        return total_dis
    total_dis = np.array(total_dis)
    arg = np.argsort(total_dis[:, 1])
    total_dis = total_dis[arg[::-1], 0]
    return total_dis

def calc_bias(preds, gts):
    M, N = len(preds), len(gts)
    dists = np.zeros([M, N, 4])
    pred_range = preds.any(-1)
    gt_range = gts.any(-1)
    for i, pred in enumerate(preds):
        for j, gt in enumerate(gts):
            vrange = pred_range[i] & gt_range[j]
            dist = distance_bias(gt, pred, vrange, step=10)
            if len(dist) <= 0:
                dists[i, j] = MAX_DIST
            else:
                if len(dist) < 6:
                    dists[i, j] = np.mean(dist)
                else:
                    L = len(dist) // 3
                    dist = [dist, dist[:L], dist[L:-L], dist[-L:]]
                    dist = list(map(np.mean, dist))
                    dists[i, j] = dist
    return dists

def calc_iou(pred_maps, gt_maps):
    inter = np.sum(pred_maps[:, None] & gt_maps[None, :], (2, 3))
    union = np.sum(pred_maps[:, None] | gt_maps[None, :], (2, 3))
    iou_mat = inter.astype(np.float64) / union
    return iou_mat

def calc_f1(mask, n_pred, n_gt, **kwargs):
    tp = mask.sum()
    prec = tp / n_pred
    rec = tp / n_gt
    ret = {}
    ret.update(
        f1=2 * prec * rec / (prec + rec),
        prec=prec,
        rec=rec,
    )
    for k, v in kwargs.items():
        v = v[mask]
        v = v[v < MAX_DIST]  # ignore inf
        ret[f"{k}_mean"] = np.mean(v)
        if k == "bias":
            ret[f"{k}_std"] = np.std(v)
    ret.update(
        tp=tp,
        fp=n_pred - tp,
        fn=n_gt - tp,
    )
    return ret

def lane_distance(pred, gt):
    min_x = []
    for gt_region in gt:
        list_y, list_x = np.where(gt_region)
        idx = np.argmax(list_y)
        min_x.append(list_x[idx])
    _, h, w = gt.shape
    # select mid lane
    bias = [abs(w // 2 - i) for i in min_x]
    sort_id = np.argsort(bias)
    select_gt = gt[sort_id[:2]]

    ret = dict(
        close=dict(dist=[], miss=0),
        mid=dict(dist=[], miss=0),
        far=dict(dist=[], miss=0),
    )
    pred = np.any(pred, 0)
    for gt_region in select_gt:
        list_y, list_x = np.where(gt_region)
        if len(list_y) > 0:
            close_dis, close_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h,
                h * 5 // 6,
                20,
            )
            ret["close"]["dist"].extend(close_dis)
            ret["close"]["miss"] += close_miss_points

            mid_dis, mid_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h * 5 // 6,
                h * 4 // 6,
                10,
            )
            ret["mid"]["dist"].extend(mid_dis)
            ret["mid"]["miss"] += mid_miss_points

            far_dis, far_miss_points = count_distance(
                pred,
                list_y,
                list_x,
                h * 4 // 6,
                h * 3 // 6,
                6,
            )
            ret["far"]["dist"].extend(far_dis)
            ret["far"]["miss"] += far_miss_points

    return ret

def count_distance(pred, list_y, list_x, start_y, end_y, bias):
    all_dis = []
    miss_points = 0
    select_y = list_y[(list_y <= start_y) & (list_y >= end_y)]
    select_x = list_x[np.where((list_y <= start_y) & (list_y >= end_y))]
    list_y_unique = np.unique(select_y)
    for y in list_y_unique:
        idx = np.where(select_y == y)
        x = select_x[idx[0]]
        # x_sort = np.sort(x)
        x_half = x[len(x) // 2]
        pp = pred[y, x_half - bias:x_half + bias]
        px = np.where(pp)
        if len(px[0]):
            dis = abs(bias - np.mean(px))
            all_dis.append(dis)
        else:
            miss_points += 1
    return all_dis, miss_points

def argmax(data, keys=None):
    if isinstance(data, dict):
        data = [data[k] for k in keys]
    return np.argmax(data)

def curve_interp(points):
    n = len(points)
    if points[0, 1] < points[-1, 1]:
        points = points[::-1]
    t = np.arange(n)
    fx = interp1d(t, points[:, 0])
    fy = interp1d(t, points[:, 1])
    length = np.linalg.norm(points[:-1] - points[1:], 2, -1).sum()
    m = int(length // 8)
    if m <= n:
        return points
    m = min(m, 72)
    t = np.linspace(0, n - 1, m)
    points = np.stack([fx(t), fy(t)], -1)
    return points

def format_dict(data, table_name=""):
    if len(data) <= 0:
        return f"{table_name}: NO PREDICTION OR GROUNDTRUTH !"
    table = PrettyTable()
    table.set_style(MSWORD_FRIENDLY)
    rows = []
    for row_name, row in data.items():
        rows.append([row_name] + ["%.6g" % _ for _ in row.values()])
    table.field_names = [table_name, *row.keys()]
    table.add_row(["-"] * len(table.field_names))
    for row in rows:
        table.add_row(row)
    return str(table)

def format_list(data, prec, rec, table_name=""):
    if len(data) <= 0:
        return f"{table_name}: NO PREDICTION OR GROUNDTRUTH !"
    table = PrettyTable()
    table.set_style(MSWORD_FRIENDLY)
    rows = []
    for r, row in enumerate(data):
        rows.append([r, "%.3g" % prec[r], "%.3g" % rec[r], *row])
    table.field_names = [table_name, "prec", "rec", *range(len(row))]
    table.add_row(["-"] * len(table.field_names))
    for row in rows:
        table.add_row(row)
    return str(table)

def match_gt_pred(ioumap, others, iou_th=0, strict=True):
    gt_idx = np.argmax(ioumap, 1)
    pred_idx = np.arange(len(gt_idx))
    ioumax = ioumap[pred_idx, gt_idx]
    mask = ioumax > iou_th
    ioumax = ioumax[mask]
    gt_idx[~mask] = -1

    if strict:
        assigned = set()
        for i, v in enumerate(gt_idx):
            if v >= 0 and v in assigned:
                gt_idx[i] = -1
            assigned.add(v)
    filt = gt_idx >= 0
    pred_idx = pred_idx[filt]
    gt_idx = gt_idx[filt]
    pairs = np.stack([pred_idx, gt_idx], -1)
    match = {k: v[pred_idx, gt_idx].tolist() for k, v in others.items()}
    return pairs, match

def lanes_match(dist, others):
    pred_idx, gt_idx = linear_sum_assignment(dist)
    pairs = np.stack([pred_idx, gt_idx], -1)
    match = {k: v[pred_idx, gt_idx].tolist() for k, v in others.items()}
    return pairs, match

def group_results(lines):
    results = {}
    for data in lines:
        data = deepcopy(data)
        key = data.get("image_path", data.get("id", None))
        assert key is not None

        points = data["key_points"]["points"]
        if len(points) <= 1:
            continue
        points = np.array([[_["x"], _["y"]] for _ in points])
        points = curve_interp(points)
        data["key_points"] = points

        breakpoints = data.get("break_points", {}).get("points", [])
        breakpoints = [[_["x"], _["y"]] for _ in breakpoints]
        data["break_points"] = breakpoints

        label = data["label"]
        results.setdefault(label, {})
        results[label].setdefault(key, [])
        results[label][key].append(data)
    return results

def eval_bk_point(pred, gt):
    ret = dict(
        n_pred=len(pred),
        n_gt=len(gt),
        dist=[],
    )
    if ret["n_gt"] <= 0 or ret["n_pred"] <= 0:
        return ret
    pred = np.array(pred)
    gt = np.array(gt)
    dist = np.linalg.norm(pred[:, None] - gt[None, :], 2, -1)
    pred_idx, gt_idx = linear_sum_assignment(dist)
    ret["dist"].extend(dist[pred_idx, gt_idx].tolist())
    return ret

def eval_parallel(
    gts,
    preds,
    categories,
    workers=0,
    types=["LANELINE", "ROADSIDE"],
):
    fn = partial(eval_single, categories=categories)

    results_dict = {}
    for label in types:
        if label not in gts.keys():
            continue
        inputs = [[gts[label][key],
                   preds.get(label, {}).get(key, [])] for key in gts[label]]
        if workers > 1:
            pool = Pool(workers)
            results = list(tqdm(pool.imap(fn, inputs), total=len(inputs)))
            pool.close()
            pool.join()
        else:
            results = [fn(_) for _ in tqdm(inputs)]
        results_dict[label] = results

    return results_dict

def eval_group(results_dict, categories):
    metrics = dict(
        lanes_matches={},
        points_matches={},
        bk_matches={},
        attributes={},
    )
    for label, results in results_dict.items():
        metric = collect_eval(results, categories)
        metrics["lanes_matches"][label] = metric["lanes_matches"]
        metrics["points_matches"][label] = metric["points_matches"]
        metrics["bk_matches"][label] = metric["bk_matches"]
        for k, v in metric["attributes"].items():
            metrics["attributes"][k] = v

    result_str = []
    for name in ["lanes_matches", "points_matches", "bk_matches"]:
        for k, v in metrics[name].items():
            result_str.append(format_dict(v, f"{name}_{k}"))
    for k, v in metrics["attributes"].items():
        result_str.append(
            format_list(v["confusion"], v["prec"], v["rec"], f"attr_{k}"))
    result_str = "\n\n".join(result_str)

    return metrics, result_str

def eval_single(input, categories):
    gt, pred = input
    assert len(gt) > 0
    roi = gt[0]["roi"]
    gt_pts = [_["key_points"] - [roi["left"], roi["top"]] for _ in gt]
    confidence = [_["confidence"] for _ in pred]
    idx = np.argsort(confidence)[::-1]
    pred_pts = [pred[i]["key_points"] - [roi["left"], roi["top"]] for i in idx]

    gt_maps = draw_maps(gt_pts, roi, 1, 1)
    pred_maps = draw_maps(pred_pts, roi, 1, 1)
    bias_dist = calc_bias(pred_maps, gt_maps)

    gt_maps = draw_maps(gt_pts, roi, 4, 4)
    pred_maps = draw_maps(pred_pts, roi, 4, 4)
    mask_iou_4_4 = calc_iou(pred_maps, gt_maps)

    gt_maps = draw_maps(gt_pts, roi, 16, 1)
    pred_maps = draw_maps(pred_pts, roi, 16, 1)
    mask_iou_16_1 = calc_iou(pred_maps, gt_maps)
    points_matches = lane_distance(pred_maps, gt_maps)

    # dtw_dist = calc_dtw(pred_pts, gt_pts, None)

    others = dict(
        iou_16_1=mask_iou_16_1,
        bias=bias_dist[..., 0],
        bias_n=bias_dist[..., 1],
        bias_m=bias_dist[..., 2],
        bias_f=bias_dist[..., 3],
        # dtw=dtw_dist[..., 0],
        # dtw_n=dtw_dist[..., 1],
        # dtw_m=dtw_dist[..., 2],
        # dtw_f=dtw_dist[..., 3],
        iou_4_4=mask_iou_4_4,
    )

    lanes_matches = dict(n_gt=len(gt), n_pred=len(pred))
    # _, match = lanes_match(dtw_dist[..., 0], others)
    # lanes_matches["dtw"] = match
    # _, match = lanes_match(bias_dist[..., 0], others)
    # lanes_matches["bias"] = match
    _, match = match_gt_pred(mask_iou_4_4, others)
    lanes_matches["iou_4_4"] = match
    pairs, match = lanes_match(1 - mask_iou_16_1, others)
    lanes_matches["iou_16_1"] = match

    attr_preds = {}
    attr_gts = {}
    bk_dist = []
    for n, (i, j) in enumerate(pairs):
        if match["iou_16_1"][n] < 0.1:
            continue
        pred_attr = pred[idx[i]]["attribute"]
        for k in pred_attr:
            gt_label = gt[j]["attribute"][k]
            if gt_label == 255:
                continue
            attr_gts.setdefault(k, [])
            attr_gts[k].append(gt_label)

            pred_label = argmax(pred_attr[k], categories[k])
            attr_preds.setdefault(k, [])
            attr_preds[k].append(pred_label)

        bk_dist.append(
            eval_bk_point(
                pred[idx[i]]["break_points"],
                gt[j]["break_points"],
            ))

    return dict(
        meta=dict(
            image_path=gt[0]["image_path"],
            img_prefix=gt[0]["img_prefix"],
            scene=gt[0]["scene"],
            camera_index=[gt[0]["camera_index"]],
            pred_idx=idx[pairs[:, 0]],
            gt_idx=pairs[:, 1],
        ),
        lanes_matches=lanes_matches,
        points_matches=points_matches,
        bk_matches=bk_dist,
        attributes=dict(pred=attr_preds, gt=attr_gts),
    )

def attr_metric(gts, preds, label_num, labels=None):
    if len(preds) <= 0 or len(gts) <= 0:
        return {}
    if labels is None:
        labels = np.arange(label_num)
    cm = confusion_matrix(gts, preds, labels=labels)
    TP = np.diag(cm.astype(np.float64))
    TP_FP = cm.sum(0)
    TP_FN = cm.sum(1)
    prec = np.where(TP_FP > 0, TP / TP_FP, -1)
    rec = np.where(TP_FN > 0, TP / TP_FN, -1)
    return dict(
        confusion=cm.tolist(),
        prec=prec.tolist(),
        rec=rec.tolist(),
    )

def group_by(results_dict, key):
    groups = {}
    for label, results in results_dict.items():
        for result in results:
            scenes = result["meta"][key]
            if isinstance(scenes, str):
                scenes = [scenes]
            for scene in scenes:
                groups.setdefault(scene, {})
                groups[scene].setdefault(label, [])
                groups[scene][label].append(result)
    groups = {k: copy.deepcopy(v) for k, v in groups.items()}
    return groups

def eval(
    gt_path,
    pred_path,
    categories,
    metric=[],
    workers=0,
):
    print("Parsing ...")
    gt_path = parse_results(gt_path, pkl_key="gt")
    pred_path = parse_results(pred_path, pkl_key="pred")
    gts = group_results(gt_path)
    preds = group_results(pred_path)

    print("Matching ...")
    results_dict = eval_parallel(gts, preds, categories, workers)

    print("Evaluating ...")
    metrics_all, markdown_all = eval_group(results_dict, categories)
    markdown = {"all": markdown_all}
    if metric is not None and len(metric) > 0:
        results_groups = {
            **group_by(results_dict, "camera_index"),
            **group_by(results_dict, "scene"),
        }
        for group in metric:
            if group not in results_groups:
                continue
            results = results_groups[group]
            _, markdown_grp = eval_group(results, categories)
            markdown[group] = markdown_grp

    if len(markdown) <= 1:
        markdown = markdown_all
    else:
        markdown = "\n".join([f"\n# {k}\n\n{v}" for k, v in markdown.items()])

    return markdown

def evaluate(results, metric=[], **kwargs):
    gts, preds = [], []
    for res in results["gt"]:
        gts.extend(res)
    for res in results["pred"]:
        preds.extend(res)
    return eval(gts, preds, categories["attr"], metric, 20)

def collect(results):
    if len(results) <= 0:
        return results
    if isinstance(results[0], dict):
        ret = {}
        for result in results:
            for k, v, in result.items():
                ret.setdefault(k, [])
                ret[k].append(v)
        ret = {k: collect(v) for k, v in ret.items()}
        return ret
    if isinstance(results[0], list):
        ret = []
        for result in results:
            ret.extend(result)
        return ret
    if isinstance(results[0], (str, np.ndarray)):
        return results
    return sum(results)

def collect_eval(results, categories):
    results = collect(results)

    lanes_matches = {}
    n_pred = results["lanes_matches"].pop("n_pred")
    n_gt = results["lanes_matches"].pop("n_gt")
    if n_pred > 0 and n_gt > 0:
        th_dict = dict(
            iou_16_1=[0.1],
            iou_4_4=[0.0],
            # bias=[4],
            # dtw=[50],
        )
        for k, v in results["lanes_matches"].items():
            for _ in v:
                v[_] = np.array(v[_])
            for th in th_dict[k]:
                if "iou" in k:
                    mask = v[k] > th
                else:
                    mask = v[k] < th
                ret = calc_f1(mask, n_pred, n_gt, **v)
                if k == "iou_4_4":
                    tp = (mask & (v["bias"] < 6)).sum()
                    prec = tp / n_pred
                    rec = tp / n_gt
                    ret.update(
                        f1=2 * prec * rec / (prec + rec),
                        prec=prec,
                        rec=rec,
                    )
                lanes_matches[f"{k}_{th:.3g}"] = ret

    bk_matches = {}
    if len(results["bk_matches"]) > 0:
        results["bk_matches"] = collect(results["bk_matches"])
        n_gt = results["bk_matches"]["n_gt"]
        n_pred = results["bk_matches"]["n_pred"]
        if n_gt > 0 and n_pred > 0:
            dist = np.array(results["bk_matches"]["dist"])
            for th in [40, 70, 100]:
                bk_matches[f"dist_{th:.3g}"] = calc_f1(
                    dist < th,
                    n_pred,
                    n_gt,
                    dist=dist,
                )

    points_matches = {}
    for k, v in results["points_matches"].items():
        if len(v["dist"]) <= 0:
            continue
        points_matches[k] = dict(
            dist_mean=np.mean(v["dist"]),
            dist_std=np.std(v["dist"], ddof=1),
            miss_points=v["miss"],
        )

    attributes = {}
    attr_preds = results["attributes"]["pred"]
    attr_gts = results["attributes"]["gt"]
    for k in attr_gts:
        attributes[k] = attr_metric(
            attr_gts[k],
            attr_preds[k],
            len(categories[k]),
        )
    metrics = dict(
        lanes_matches=lanes_matches,
        points_matches=points_matches,
        bk_matches=bk_matches,
        attributes=attributes,
    )
    return metrics

if __name__ == "__main__":
    categories = dict(
        group=["lane_type", "lane_color", "roadside"],
        attr={
            "lane_type": [
                "NO_LANE",
                "OTHER_LANE_TYPE",
                "SOLID_LANE",
                "DASHED_LANE",
                "SHORT_THICK_DASHED_LANE",
                "SPLIT_LANE",
                "MERGE_LANE",
                "LEFT_DASHED_RIGHT_SOLID",
                "LEFT_SOLID_RIGHT_DASHED",
                "DOUBLE_SOLID",
                "DOUBLE_DASHED",
                "FISHBONE_SOLID",
                "FISHBONE_DASHED",
            ],
            "lane_color": [
                "NO_LANE",
                "OTHER_LANE_COLOR",
                "WHITE",
                "YELLOW",
                "ORANGE",
                "BLUE",
            ],
            "roadside": [
                "NO_SIDE",
                "ROAD_CURB_EDGE",
                "FENCE_EDGE",
                "WALL_EDGE_OR_PLANT_EDGE",
                "MOVABLE_EDGE",
                "OTHER_EDGE",
            ],
        },
    )
    
    eval_kwargs = {}
    
    results = reconstruct_results("./eval_files")
    print("==========RESULTS LEN==============:", len(results))
    metric = evaluate(results, **eval_kwargs)
    print(metric)
    