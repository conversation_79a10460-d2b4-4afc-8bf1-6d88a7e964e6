import torch
import torch.nn as nn
from ..builder import MODELS
from mmpose.models.heads import TopdownHeatmapSimpleHead

@MODELS.register_module()
class IdentityHead(TopdownHeatmapSimpleHead):
    def __init__(self,
                 in_channels,
                 out_channels,
                 num_deconv_layers=3,
                 num_deconv_filters=(256, 256, 256),
                 num_deconv_kernels=(4, 4, 4),
                 extra=None,
                 in_index=0,
                 input_transform=None,
                 align_corners=False,
                 loss_keypoint=None,
                 train_cfg=None,
                 test_cfg=None
                ):
        super().__init__(in_channels=in_channels, out_channels=out_channels, loss_keypoint=loss_keypoint, train_cfg=train_cfg, test_cfg=test_cfg, 
        extra=extra, in_index=in_index, input_transform=input_transform, align_corners=align_corners, num_deconv_layers=num_deconv_layers, num_deconv_kernels=num_deconv_kernels, num_deconv_filters=num_deconv_filters)
        self.final_layer = nn.Identity()

    def forward(self, x):
        x = self.final_layer(x)
        return x