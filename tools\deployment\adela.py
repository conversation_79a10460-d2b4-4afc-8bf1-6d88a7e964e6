import os
import re
import sys
import time
import json


def os_popen(stmt, *parm):
    re = os.popen(stmt).readlines()
    result = []
    for i in range(0, len(re) - 1):
        res = re[i].strip('\n')
        result.append(res)
    if parm == ():
        return result
    else:
        line = int(parm[0]) - 1
        return result[line]


class AdelaClient:
    def __init__(self, project_id, deployments, parameters, meta, work_dir):
        self.project_id = project_id
        self.deployments = deployments
        self.parameters = parameters
        self.meta = meta
        self.work_dir = work_dir

    def generate_jsons(self):
        # parameters
        self.parameters_json_path = os.path.join(self.work_dir, 'parameters.json')
        json.dump(self.parameters, open(self.parameters_json_path, 'w'))

        # meta
        self.meta_json_path = os.path.join(self.work_dir, 'meta.json')
        json.dump(self.meta, open(self.meta_json_path, 'w'))

        # deployments
        self.deployments_json_paths = {}
        for deployment in self.deployments:
            platform = deployment.platform
            deployment_json_path = os.path.join(
                self.work_dir, f'deployment-{platform}.json')
            self.deployments_json_paths[platform] = deployment_json_path
            json.dump(deployment, open(deployment_json_path, 'w'))

        # release
        self.release_json_path = os.path.join(self.work_dir, 'release.json')
        cmd = f'python -m adela.make_json {self.work_dir} -o {self.release_json_path}'
        result = os.system(cmd)
        if result:
            print(f'generate release.json error: {result}')
            sys.exit(0)
    
    def release(self):
        cmd = f'python -m adela.cmd -p {self.project_id} -ra {self.release_json_path}'
        result = os_popen(cmd)
        result = [res.strip() for res in result]
        release_ids = []
        for res in result:
            ret = re.findall(r"\'id\': \d+\.?\d*", res)
            if ret:
                ret = re.findall(r"\d+\.?\d*", res)
                release_ids.append(int(ret[0]))
        if not release_ids:
            print(f'get release id error, result: {result}')
            sys.exit(0)
        # adela results contains project_id, user_id, release_id
        # release_id is the max one
        release_id = max(release_ids)
        return release_id
    
    def deploy_model(self, release_id):        
        deploy_id_dict = {}
        results = {}
        for platform, deployment_json_path in self.deployments_json_paths.items():                        
            cmd = f'python -m adela.cmd -p {self.project_id} -r {release_id} -da {deployment_json_path}'
            result = os_popen(cmd)
            result = [res.strip() for res in result]
            results[platform] = result
            for res in result:
                ret = re.findall(r"\'id\': \d+\.?\d*", res)
                if ret:
                    ret = re.findall(r"\d+\.?\d*", res)
                    deploy_id_dict[platform] = int(ret[0])
                    break
        assert len(deploy_id_dict) == len(self.deployments), \
            'some of the deployments failed, more info: {results}'
        
        return deploy_id_dict

    def get_deploy_status(self, deploy_id_dict):
        success_cnt = 0
        for platform, deploy_id in deploy_id_dict.items():
            status = 'UNDEPLOYMENT'
            while 'SUCCESS' not in status.upper() and 'FAILURE' not in status.upper():
                print(
                    f'deploy status: {status}, wait platform: {platform} model convert success...',
                    flush=True)
                
                time.sleep(10)
                cmd = f'python -m adela.cmd -p {self.project_id} -d {deploy_id}'
                result = os_popen(cmd)
                result = [res.strip() for res in result]
                for res in result:
                    ret = re.match(r"\'status\': \'[a-zA-Z_]+[\w]*\'", res)
                    if ret:
                        status = ret.group()
            if 'SUCCESS' in status.upper():
                success_cnt += 1
                print(f'platform: {platform} deploy success', flush=True)
            else:
                print(f'platform: {platform} deploy failed, more info: {result}', flush=True)
        
        return success_cnt

    def download_model(self, deploy_id_dict):
        model_id_dict = {}
        for platform, deploy_id in deploy_id_dict.items():
            cmd = f'python -m adela.cmd -p {self.project_id} -d {deploy_id} -ma'
            result = os_popen(cmd)
            result = [res.strip() for res in result]
            for res in result:
                ret = re.findall(r"\'id\': \d+\.?\d*", res)
                if ret:
                    ret = re.findall(r"\d+\.?\d*", res)
                    model_id_dict[platform] = int(ret[0])
                    break

        for platform, model_id in model_id_dict.items():
            cmd = f'python -m adela.cmd -p {self.project_id} -md {model_id}'
            os_popen(cmd)

        current_path = os.path.abspath('./')
        os.system(f'mv {current_path}/*.model {self.work_dir}')  
    
    def run(self):
        self.generate_jsons()
        release_id = self.release()
        deploy_id_dict = self.deploy_model(release_id)
        success_cnt = self.get_deploy_status(deploy_id_dict)
        if success_cnt:
            self.download_model(deploy_id_dict)
