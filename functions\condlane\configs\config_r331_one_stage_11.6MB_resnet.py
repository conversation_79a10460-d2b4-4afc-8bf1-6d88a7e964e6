custom_imports = dict(imports=[
    "condlane.datasets",
    "condlane.datasets.pipelines",
    "condlane.models.backbones",
    "condlane.models.necks",
    "condlane.models.dense_heads",
    "condlane.models.losses",
    "condlane.models.predictors",
])

test_setting = dict(
    repo="pillar",
    single_gpu_test=dict(show=False),
    multi_gpu_test=dict(gpu_collect=True),
)
find_unused_parameters = True

# distribution
dist_params = dict(
    #backend="nccl",
    port="29507",
)
batch_size = 8      #8
num_workers = 4      #4

# tokestrel = dict(model_name="bi_vec_bg0.01_FieldMaskWeight_relu_sm1_mirror_soft0.5_rain",
                 # version="3.3.1",
                 # batch_size=dict(image=20, ))
tokestrel = dict(
    model_name="ep20_r331_data277w_single_stage_relu_sm1_mirror_soft0.5",
    version="3.3.1",
    batch_size=dict(
        image=1,
        image2=1,
    ),
)

# data
dataset_type = "pillar.LkdCropClsDataset"

ceph_cfg = ""


train_ann_file = "/home/<USER>/data/large_new_data_cropped_combined_train/large_new_data_cropped_combined_train.json"
train_img_prefix = "/home/<USER>/data/large_new_data_cropped_combined_train"
val_ann_file = "/home/<USER>/data/large_new_data_cropped_combined_test/large_new_data_cropped_combined_test.json"
val_img_prefix = "/home/<USER>/data/large_new_data_cropped_combined_test"

"""
train_ann_file = "/home/<USER>/condlane/data/small_data/train_data.json"
train_img_prefix = "/home/<USER>/condlane/data/small_data"
val_ann_file = "/home/<USER>/condlane/data/small_data/train_data.json"
val_img_prefix = "/home/<USER>/condlane/data/small_data"
"""
# categories
num_lane_classes = 2
categories = dict(
    group=["lane_type", "lane_color", "roadside"],
    attr={
        "lane_type": [
            "NO_LANE",
            "OTHER_LANE_TYPE",
            "SOLID_LANE",
            "DASHED_LANE",
            "SHORT_THICK_DASHED_LANE",
            "SPLIT_LANE",
            "MERGE_LANE",
            "LEFT_DASHED_RIGHT_SOLID",
            "LEFT_SOLID_RIGHT_DASHED",
            "DOUBLE_SOLID",
            "DOUBLE_DASHED",
            "FISHBONE_SOLID",
            "FISHBONE_DASHED",
        ],
        "lane_color": [
            "NO_LANE",
            "OTHER_LANE_COLOR",
            "WHITE",
            "YELLOW",
            "ORANGE",
            "BLUE",
        ],
        "roadside": [
            "NO_SIDE",
            "ROAD_CURB_EDGE",
            "FENCE_EDGE",
            "WALL_EDGE_OR_PLANT_EDGE",
            "MOVABLE_EDGE",
            "OTHER_EDGE",
        ],
    },
)

# field channels
view_distance = 5
inv_view_distance = 5
num_ref_point = 2
length_scale = 0
field_scale = None
maskAct = "Hsigmoid"    # sigmoid, Hsigmoid
laneline_indices = dict(
    length=27,
    offset=29,
    field=30,
    view=(33, 33 + view_distance - 1),
)
roadside_indices = dict(
    length=28,
    offset=31,
    field=32,
    view=(33 + (view_distance - 1), 33 + 2 * (view_distance - 1)),
)
gt_indices = dict(
    offset=(0, num_ref_point),
    field=(num_ref_point, 2 * num_ref_point),
    length=2 * num_ref_point,
    weight=(1 + 2 * num_ref_point, 1 + 2 * num_ref_point + 1 + (view_distance + 1) * num_ref_point),
    view=(3 + (view_distance + 1) * num_ref_point, 3 + 2 * view_distance * num_ref_point),
)

# preprocess
img_scale = (int(864), int(288))
h_crop = (0, int(288))

# camera_roi = dict(
    # # front_fov30=dict(t=0, b=1, l=0, r=1),
    # front_fov120=dict(t=h_crop[0] / img_scale[1],
                      # b=h_crop[1] / img_scale[1],
                      # l=0,
                      # r=1),
    # # rear_fov60=dict(t=0, b=1080, l=0, r=1920),
# )
camera_roi = dict(
    front_fov30=dict(t=0, b=1, l=0, r=1),
    front_fov120=dict(t=0, b=1, l=0, r=1),
    # rear_fov60=dict(t=0, b=1, l=0, r=1),
    # rear_fov100=dict(t=0, b=1, l=0, r=1)
)
img_norm_cfg = dict(
    mean=[75.3, 76.6, 77.6],
    std=[50.5, 53.8, 54.3],
    to_rgb=False,
)
# midprocess
proposal_heatmap_thr = 0.6
# postprocess
range_method = "field"
post_processor = dict(
    key_points_size_thr=35,
    side_key_points_size_thr=35,
    range_method=range_method,
    cls_method="row_att",
    with_breakpoints=True,
    breakpoints_thr=0.6,
    range_thr=0,
    range_continue_tole=0,
    use_offset=True,
    nms_thr=0.0,
    categories=categories,
    laneline_indices=laneline_indices,
    roadside_indices=roadside_indices,
    mask_upsample_rate=4,
    num_row_ref_point=num_ref_point,
    inv_view_distance=inv_view_distance,
    length_scale=length_scale,
    field_scale=field_scale,
    with_hm=True,
    mask_sigma=0.5,
    mask_max_thr=0.0,
    th_len=10,
    v4_simplify=True,
    mask_ignores=30
)

mask_down_scale = 4
hm_down_scale = 16
line_width = 4
radius = 3
max_mask_sample = 4
train_cfg = dict(out_scale=mask_down_scale)
test_cfg = dict(out_scale=mask_down_scale)

loss_weights = dict(
    hm_weight=2.5,
    kps_weight=0.4,
    row_weight=1.5,
    range_weight=2.,
    lane_type_weight=[
        1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
    ],
    row_ce_weight=1.0,
    len_loss_weight=1.0,
    auxiliary_mask_loss_weight=[0.5, 0.5, 0.5, 0.5],
    d2_loss_weight=0.5,
    smooth_weight=0.0,
    view_distance=view_distance,
    inv_view_distance=inv_view_distance,
    num_ref_point=num_ref_point,
    length_scale=length_scale,
    field_scale=field_scale,
    #length_bg_weight=[-1, 0.1],
    field_bg_weight=[-1, 0.1],
    attr_bg_weight=0.1,
    mask_bg_weight=[-1, 0.1],
    maskAct=maskAct,
    reg_smooth_l1=True
)
# model settings
norm_cfg = dict(type="BN2d", requires_grad=True)
model = dict(
    type="pillar.FieldLaneNet",
    pretrained="/home/<USER>/condlane/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage/functions/condlane/checkpoint/condlane_r331_one_stage_11.6MB.pth",
    pretrained_list=["neck", "head"],
    train_cfg=train_cfg,
    test_cfg=test_cfg,
    num_classes=num_lane_classes,
    loss_condlane=dict(
        type="pillar.FieldLaneLoss",
        weights=loss_weights,
        property_group=[13, 6, 6],
    ),
    backbone=dict(
        type="pillar.ResNetFPNBackbone",
        init_cfg=None,
    ),
    neck=dict(
        type="pillar.TransConvFPN",
        in_channels=[64, 64, 128, 128],
        out_channels=64,
        num_outs=4,
        trans_idx=-1,
        trans_cfg=None,
        next_conv_cfg=dict(
            in_channel=128,
            channels=[128, 128],
            kernels=[[7, 7], [7, 7]],
            expand_ratios=[2, 2],
            is_plus=True,
            ActType="ReLU",
            #depthwise_conv=True,
        ),
        attention=False,
        kernel_size=7,
        deconv=False,
        norm_cfg=norm_cfg,
        lateral_norm_cfg=None,
        fpn_norm_cfg=None,
    ),
    head=dict(
        type="pillar.FieldLaneHead",
        in_channel=64,
        ActType="ReLU",
        maskAct=maskAct,
        # mask_property_group=[2, 13, 6, 6, 2, 2, 2, view_distance -1, view_distance -1],
        # mask_property_name = ['line_mask', 'laneline_type', 'laneline_color', 'roadside_type', \
        #      'laneline_length','roadside_length', 'laneline_offset', 'roadside_offset', \
        #          'laneline_field', 'roadside_field', 'laneline_view', 'roadside_view'],
        mask_property_group=dict(
            line_mask=(2, 0, 1),
            laneline_type=(13, 2, 1),
            laneline_color=(6, 2, 1),
            roadside_type=(6, 2, 1),
            roadside_length=(1, 0, 2),
            laneline_length=(1, 0, 2),
            roadside_offset=(num_ref_point, 0, 1),
            laneline_offset=(num_ref_point, 0, 1),
            roadside_field=(num_ref_point, 0, 2),
            laneline_field=(num_ref_point, 0, 2),
            roadside_view=((view_distance - 1) * num_ref_point, 0, 2),
            laneline_view=((view_distance - 1) * num_ref_point, 0, 2),
            roadside_inv_vector_field=((inv_view_distance) * num_ref_point, 0, 2),
            laneline_inv_vector_field=((inv_view_distance) * num_ref_point, 0, 2),
            roadside_inv_length=(1, 0, 1),
            laneline_inv_length=(1, 0, 1),
        ),
        num_classes=num_lane_classes,
        with_break_point=False,
        hm_idx=2,
        mask_idx=0,
        proposal_heatmap_thr=proposal_heatmap_thr,
        range_method=range_method,
        expand=1,
        with_start_point_off=True,
        with_start_point_feat_group=True,
        with_nms_weight=True,
        norm_cfg=norm_cfg,
        v4_simplify=True,
    ),
    post_processor=post_processor,
    gt_indices=gt_indices,
)

# data pipeline settings
compose_params = dict(bboxes=False, keypoints=True, masks=True)

train_al_pipeline = [
    dict(type='Compose', params=compose_params),
    dict(type='Resize', height=600, width=1066, p=1),
    dict(
        type="RandomResizedCrop",
        height=img_scale[1],
        width=img_scale[0],
        scale=(0.6, 1.0),
        ratio=(1.5, 3.0),
        interpolation=0,  # cv2.INTER_NEAREST
    ),
    dict(
        type="ShiftScaleRotate",
        shift_limit=0.1,
        scale_limit=(-0.2, 0.2),
        rotate_limit=10,
        border_mode=0,
        p=0.5,
    ),
    dict(type="HorizontalFlip", p=0.5),
    dict(
        type="ColorJitter",
        brightness=0.5,
        contrast=0.5,
        saturation=0.5,
        hue=0.2,
        p=0.5,
    ),
    dict(
        type="OneOf",
        transforms=[
            dict(
                type="ImageCompression",
                compression_type=0,
                quality_lower=50,
                quality_upper=100,
                p=1.0,
            ),
            dict(
                type="ImageCompression",
                compression_type=1,
                quality_lower=50,
                quality_upper=100,
                p=1.0,
            ),
        ],
        p=0.5,
    ),
    dict(type='OneOf',
         transforms=[
             dict(type='Blur', blur_limit=3, p=1.0),
             dict(type='MedianBlur', blur_limit=3, p=1.0),
             dict(type="GaussianBlur", p=1.0)
         ],
         p=0.5),
    dict(type='Resize', height=h_crop[1] - h_crop[0], width=img_scale[0], p=1)
]

val_al_pipeline = [
    dict(type="Compose", params=compose_params),
    dict(type="Resize", height=h_crop[1] - h_crop[0], width=img_scale[0], p=1),
]

train_pipeline = [
    dict(type="pillar.LoadLanes", ceph_cfg=ceph_cfg, roi_method="crop"),
    dict(type="pillar.Mirror", scale=0.3, p=0.1),
    dict(type="pillar.Alaug", transforms=train_al_pipeline),
    dict(type="mmcls.Normalize", **img_norm_cfg),
    dict(type="pillar.CollectBilateralFiledLane",
         down_scale=mask_down_scale,
         hm_down_scale=hm_down_scale,
         property_down_rate=4,
         num_drop_remote_point=0,
         num_start_rows=3,
         bg_weight=0.01,
         soft_weight_len_alpha=0.5,
         soft_weight_off_alpha=0.5,
         soft_weight_x=True,
         soft_weight_y=True,
         draw_r=4,
         num_ref_point=num_ref_point,
         inv_view_distance=inv_view_distance,
         max_mask_sample=max_mask_sample,
         line_width=line_width,
         radius=radius,
         interval=5,
         filter_pts=1,
         endpoint="down",
         keys=[
             "img",
             "lane_gt_hm",
             "lane_field",
             "lane_attribute_target",
             "roadside_gt_hm",
             "lane_masks",
             "lane_label_mask",
             "lane_color_mask",
             "roadside_masks",
             "roadside_label_mask",
             "roadside_field",
             "roadside_attribute_target",
         ],
         meta_keys=[
             "filename",
             "sub_img_name",
             "mask_shape",
             "hm_shape",
             "ori_shape",
             "img_shape",
             "down_scale",
             "hm_down_scale",
             "img_norm_cfg",
         ]),
    dict(type="pillar.DefaultFormatBundle"),
]

val_pipeline = [
    dict(type="pillar.LoadLanes", ceph_cfg=ceph_cfg, roi_method="crop"),
    dict(type="pillar.Alaug", transforms=val_al_pipeline),
    dict(type="mmcls.Normalize", **img_norm_cfg),
    dict(
        type="pillar.CollectLkdClsLane",
        down_scale=mask_down_scale,
        hm_down_scale=hm_down_scale,
        radius=radius,
    ),
    # dict(
    #     type="pillar.CollectFiledLane",
    #     down_scale=mask_down_scale,
    #     hm_down_scale=hm_down_scale,
    #     property_down_rate=4,
    #     num_drop_remote_point=0,
    #     num_ref_point=num_ref_point,
    #     max_mask_sample=max_mask_sample,
    #     line_width=line_width,
    #     radius=radius,
    #     interval=5,
    #     filter_pts=1,
    #     endpoint="down",
    # ),
    dict(type="pillar.DefaultFormatBundle"),
]

data = dict(
    samples_per_gpu=batch_size,
    workers_per_gpu=num_workers,
    train=dict(
        type=dataset_type,
        ann_file=train_ann_file,
        img_prefix=train_img_prefix,
        pipeline=train_pipeline,
        test_mode=False,
        camera_roi=camera_roi,
        categories=categories,
        camera_filter=True,
    ),
    val=dict(
        type=dataset_type,
        ann_file=val_ann_file,
        img_prefix=val_img_prefix,
        pipeline=val_pipeline,
        test_mode=True,
        camera_roi=camera_roi,
        categories=categories,
        camera_filter=True,
    ),
    test=dict(
        type=dataset_type,
        ann_file=val_ann_file,
        img_prefix=val_img_prefix,
        pipeline=val_pipeline,
        test_mode=True,
        camera_roi=camera_roi,
        categories=categories,
        camera_filter=True,
    ),
)

evaluation = dict(
    type="mme.EvalHook",
    dataset=data["val"],
    dataloader=dict(samples_per_gpu=16, workers_per_gpu=2),
    test_setting=test_setting,
    metric="accuracy",
    interval=2,
    by_epoch=True,
)

optimizer = dict(
    type='AdamW',
    lr=0.001,  # Base learning rate
    weight_decay=0.001,
)

optimizer_config = dict(grad_clip=dict(max_norm=3))

lr_config = dict(
    policy='CosineAnnealing',
    min_lr=1e-6,
    by_epoch=False,
    warmup='linear',
    warmup_iters=500,
    warmup_ratio=0.01,
    warmup_by_epoch=False,
)

# # optimizer
# optimizer = dict(type='AdamW', lr=0.001, weight_decay=0.001,
#     paramwise_cfg=dict(bias_decay_mult=0., norm_decay_mult=0.,),
# )
# optimizer_config = dict(grad_clip=dict(max_norm=3))

# # learning policy
# lr_config = dict(
#     policy='CosineAnnealing',
#     min_lr=1e-6,
#     by_epoch=False,
#     warmup='linear',
#     warmup_iters=500,
#     warmup_ratio=0.01,
#     warmup_by_epoch=False,
# )

# runtime settings
runner = dict(type="mmcv.EpochBasedRunner", max_epochs=100)

checkpoint_config = dict(interval=1, max_keep_ckpts=1)
log_config = dict(interval=1, hooks=[dict(type="mmcv.TextLoggerHook"),],)

log_level = "INFO"
auto_resume = False
resume_from = ""
load_from = ""
workflow = [("train", 200)]
