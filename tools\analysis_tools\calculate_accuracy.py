import argparse
import os
from collections import defaultdict

import numpy as np
import pandas as pd

import mmcv
from mmcv import DictAction
from mmcls.datasets import build_dataset, BaseMPro
from mmcls.core import mAP


def parse_args():
    parser = argparse.ArgumentParser(
        description='calculate accuracy')
    parser.add_argument('config', help='test config file path')
    parser.add_argument('--options', nargs='+', action=DictAction)
    parser.add_argument('--data-list', required=True, nargs='+', type=str, help='list file to filter images')
    args = parser.parse_args()
    return args


def process(config_file, args):
    cfg = mmcv.Config.fromfile(config_file)
    if args.options is not None:
        cfg.merge_from_dict(args.options)

    # build the dataloader
    dataset = build_dataset(cfg.data.test)
    gt_labels_arr = dataset.get_gt_labels()

    # load test results
    res_root = config_file.replace('configs', 'work_dirs').replace('.py', '')
    res_dir = sorted(os.listdir(res_root))[-1]
    res_json = f'{res_root}/{res_dir}/evaluation_results.json'
    if not os.path.isfile(res_json):
        print('Testing should be executed before calculating accuracy')
        print(f'Skip Accuracy Calculation for {config_file}')
        return None, None
    res_outputs = mmcv.load(res_json)
    if 'mAP' not in res_outputs:
        pred_cls_scores = np.array(res_outputs['class_scores'])
        gt_labels_mat = np.zeros_like(pred_cls_scores)
        gt_labels_mat[np.arange(gt_labels_mat.shape[0]), gt_labels_arr] = 1.
        res_outputs['mAP'] = mAP(pred_cls_scores, gt_labels_mat)

    outputs = dict(
        class_scores=res_outputs.pop('class_scores'),
        pred_score=res_outputs.pop('pred_score'),
        pred_label=res_outputs.pop('pred_label'),
        pred_class=res_outputs.pop('pred_class'),
    )
    print(f'best result:\n{res_outputs}')

    if outputs['pred_score'] is None and outputs['pred_label'] is None:
        print('accuracy calculation is only available for single-label dataset')
        print(f'Skip Accuracy Calculation for {config_file}')
        return res_outputs, None
    assert gt_labels_arr.ndim == 1, 'invalid gt_labels'

    outputs['filename'] = [data_info['img_info']['filename'] for data_info in dataset.data_infos]
    outputs['gt_label'] = list(gt_labels_arr)

    outputs_list = list()
    for i in range(gt_labels_arr.shape[0]):
        output = dict()
        for k in outputs.keys():
            output[k] = outputs[k][i]
        output['correct'] = output['pred_label'] == output['gt_label']
        outputs_list.append(output)

    # sort result
    outputs_list = sorted(outputs_list, key=lambda x: x['pred_score'])

    # filter result
    outputs_dict, cls_count = defaultdict(list), defaultdict(lambda: defaultdict(lambda: 0))
    data_lists = set(args.data_list)
    data_lists.add(cfg.data.test.ann_file)
    for data_list in data_lists:
        with open(data_list, 'r') as f:
            annotations = f.read().splitlines()
        fileset = set()
        for annotation in annotations:
            filename = annotation.split(' ', 1)[0]
            fileset.add(filename)
            cls_id = annotation.rsplit(' ')[1:]
            if len(cls_id) > 1: # not implemented for multi-label dataset
                continue
            cls_count[data_list][cls_id[0]] += 1
        for output in outputs_list:
            if output['filename'] not in fileset:
                continue
            outputs_dict[data_list].append(output)

    accuracy = defaultdict(dict)
    for data_list, outputs in outputs_dict.items():
        correct_arr = np.array([output['correct'] for output in outputs])
        correct_num = correct_arr.sum()
        total_num = correct_arr.shape[0]
        list_acc = correct_num / total_num * 100.
        accuracy[data_list]['total'] = f'{list_acc:.8f}\n({correct_num}/{total_num})'

        class_arr = np.array([output['gt_label'] for output in outputs])
        for cls_id in range(len(dataset.CLASSES)):
            valid_indices = class_arr == cls_id
            correct_num = correct_arr[valid_indices].sum()
            valid_num = valid_indices.sum()
            cls_acc = correct_num / valid_num * 100.
            accuracy[data_list][dataset.CLASSES[cls_id]] = f'{cls_acc:.8f}\n({correct_num}/{valid_num})'
    print(f'accuracy:\n{accuracy}')
    return res_outputs, accuracy


def main():
    args = parse_args()
    if os.path.isfile(args.config):
        config_files = [args.config]
    elif os.path.isdir(args.config):
        config_files = list(map(lambda cfg_f: os.path.join(args.config, cfg_f), os.listdir(args.config)))
        config_files = list(filter(os.path.isfile, config_files))
        config_files.sort()
    else:
        raise Exception('invalid config')
    indicators_dict = dict(
        # TODO: remove BaseMPro
        single=['mAP', 'accuracy_total', *[f'accuracy_{cls_name}' for cls_name in BaseMPro.CLASSES], 'precision', 'recall', 'f1_score'],
        multi=['mAP', 'CP', 'CR', 'CF1', 'OP', 'OR', 'OF1', *([None] * 11)],
    )
    data_frame = dict()
    for config_file in config_files:
        print(f'\nProcessing Config File {config_file}')
        output, accuracy = process(config_file, args)
        multi_flag = accuracy is None
        if not multi_flag:
            accuracy_list = list()
            for data_list, list_acc in accuracy.items():
                if data_list in args.data_list:
                    continue
                accuracy_list.append(list_acc)
            assert len(accuracy_list) == 1, 'invalid accuracy'
            accuracy = accuracy_list[0]

        data_frame[config_file] = list()
        cfg_frame = data_frame[config_file]
        if multi_flag:
            indicators = indicators_dict['multi']
        else:
            indicators = indicators_dict['single']
            # accuracy
            accuracy_total = float(accuracy['total'].split('(')[0])
            assert abs(accuracy_total - output['accuracy_top-1']) < 1e-8, 'invalid accuracy: {} v.s. {}'.format(
                accuracy_total, output['accuracy_top-1'])
        for k in indicators:
            if k is None: # for same length
                cfg_frame.append(None)
                continue
            v = output[k] if k in output else accuracy[k.replace('accuracy_', '')]
            if isinstance(v, list):
                cfg_frame.extend(v)
            else:
                cfg_frame.append(v)
    data_frame = pd.DataFrame(data=data_frame)
    data_frame.to_csv('acc.csv', index=False)


if __name__ == '__main__':
    main()
