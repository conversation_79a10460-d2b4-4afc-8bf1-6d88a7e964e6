#!/bin/bash
#SBATCH -p xhhgexclu05   # -p to set the partition in Chinese cluster
#SBATCH -N 1   # -N to set the number of node
#SBATCH -n 16   # -n to set the kernel number. in 3090 list kernel number = 8*gpu
#SBATCH --gres=gpu:2    # --gres=gpu: to set the number of gpu you request
#SBATCH -J ftuning_all_batches  # -J to set the job name 
#SBATCH -o %j.o     # -o to set the file for saving the output log
#SBATCH -e %j.e    # -e to set the file for saving the error log
#SBATCH --time=200:00:00   # set the maximum runtime to 70 hours

# enviroment set part
source  /public/software/apps/anaconda3/5.2.0/etc/profile.d/conda.sh
conda activate env1
module load nvidia/cuda/11.6
export NCCL_IB_HCA=mlx5_0
export NCCL_SOCKET_IFNAME=ib0 #eno1
export HSA_FORCE_FINE_GRAIN_PCIE=1
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export NCCL_NET_GDR_LEVEL=0

nodes=( $( scontrol show hostnames $SLURM_JOB_NODELIST ) )
nodes_array=($nodes)
head_node=${nodes_array[0]}
echo  $head_node
head_node_ip=$(srun --nodes=1 --ntasks=1 -w "$head_node" hostname --ip-address)
echo head_node_ip
echo Node IP: $head_node_ip
NODE_RANK=$SLURM_NODEID
echo $NODE_RANK
# export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

# job content
sh ./condlane_train.sh 