from pillar.models import MODELS
from .condlanenet import CondLaneNet
import torch


@MODELS.register_module()
class FieldLaneNet(CondLaneNet):

    def __init__(
        self,
        gt_indices={},
        **kwargs,
    ):
        self.gt_indices = gt_indices
        super().__init__(**kwargs)

    def forward_train(self, img, depth_map, img_metas, **kwargs):
        neck_feats, decoder_feats = self.extract_feat(img)
        neck_feats, _ = neck_feats
        kwargs['depth_map'] = depth_map

        condlane_output = self.head.forward_train(neck_feats)
        spatial_output = self.spatial_head.forward_train(decoder_feats)
        
        #print(f"keys in kwargs in FieldLaneNet {kwargs.keys()}")
        
        losses = self.losses(condlane_output, spatial_output, img_metas, **kwargs) #todo: combine losses for condlane and spatial head
        return losses
