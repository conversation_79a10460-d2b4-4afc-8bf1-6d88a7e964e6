import torch
import torch.nn as nn
import torch.nn.init as init
from torchvision.models.detection.backbone_utils import resnet_fpn_backbone
from pillar.models import MODELS
from mmengine.logging import MMLogger
from mmcv.runner.checkpoint import CheckpointLoader

@MODELS.register_module()
class ResNetFPNBackbone(nn.Module):
    def __init__(self, init_cfg):
        super(ResNetFPNBackbone, self).__init__()
        self.init_cfg = init_cfg
        # load ResNet-FPN backbone
        self.resnet_fpn = resnet_fpn_backbone('resnet50', pretrained=False)

        # transition layers to match CondLane neck input dimensions
        self.transition_layers = nn.ModuleList([
            nn.Conv2d(256, 64, kernel_size=1, stride=1, padding=0),  # For 72x216
            nn.Conv2d(256, 64, kernel_size=1, stride=1, padding=0),  # For 36x108
            nn.Conv2d(256, 128, kernel_size=1, stride=1, padding=0), # For 18x54
            nn.Conv2d(256, 128, kernel_size=1, stride=1, padding=0), # For 9x27
        ])
        
    def init_weights(self, pretrained=None):
        """
        Initialize weights for the model.
        Args:
            init_cfg (str or None): Path to pretrained weights file. If None, use standard initialization.
        """
        if self.init_cfg is None:
            # standard initialization
            for m in self.modules():
                if isinstance(m, nn.Conv2d):
                    init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                    if m.bias is not None:
                        init.constant_(m.bias, 0)
                elif isinstance(m, nn.BatchNorm2d):
                    init.constant_(m.weight, 1)
                    init.constant_(m.bias, 0)

        else:
            # load checkpoint
            assert 'checkpoint' in self.init_cfg, (
                "Only support specifying a `checkpoint` key in init_cfg for "
                f"{self.__class__.__name__}"
            )
            ckpt = CheckpointLoader.load_checkpoint(
                self.init_cfg['checkpoint'],
                logger=logger,
                map_location='cpu'
            )
            # some checkpoints might store under 'state_dict' or 'model'
            if 'state_dict' in ckpt:
                state_dict = ckpt['state_dict']
            elif 'model' in ckpt:
                state_dict = ckpt['model']
            else:
                state_dict = ckpt

            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warn(f"[ResNet] Missing keys: {missing_keys}")
            if unexpected_keys:
                logger.warn(f"[ResNet] Unexpected keys: {unexpected_keys}")

    def forward(self, x):
        # extract feature maps from resnet-fpn
        features = self.resnet_fpn(x)  # features are a dictionary

        # select relevant pyramid levels ('0', '1', '2', '3')
        selected_features = [
            features['0'],  # 72x216
            features['1'],  # 36x108
            features['2'],  # 18x54
            features['3'],  # 9x27
        ]

        # apply transition layers to adjust channel dimensions
        output_features = [trans_layer(feat) for trans_layer, feat in zip(self.transition_layers, selected_features)]

        # print output shapes
#        for i, feat in enumerate(output_features):
#            print(f"Output tensor {i}: {feat.shape}")

        return output_features

