set -x 

CMD=${1:-"tokestrel"}
CONFIG=${2:-"./configs/config_r331_one_stage_11.6MB_mobilenetv3.py"}
#CONFIG=${2:-"./configs/config_r331_one_stage_11.6MB_finetuning.py"}
#CKPT=${3:-"./checkpoint/condlane_r331_one_stage_11.6MB.pth"}
CKPT=${3:-"./checkpoint/condlane_with_mobilenetv3_backbone_ftuned_for_100epochs.pth"}
ARGS=${@:4}

HOME="/home/<USER>/condlane/camera_condlane_python-condlane_v1.2/conditional-lane-detection-one-two-stage"
FUNCTION_HOME="${HOME}/functions"
CONDLANE_HOME="${FUNCTION_HOME}/condlane"
SYS_HOME='/home/<USER>/miniconda3/envs/env2/lib/python3.6/site-packages'
export PYTHONPATH=$HOME:$FUNCTION_HOME:$CONDLANE_HOME:$PYTHONPATH:$SYS_HOME

export WORK_DIR="./tokestrel"
mkdir -p $WORK_DIR
Client_conf=""

#python -m deploy $CMD $CONFIG "$CKPT"           \
#	--o $WORK_DIR --client_conf $Client_conf    \
#	--u8          --nhwc                        \
#	--dup_i=2     $ARGS                         \
#	2>&1 | tee $WORK_DIR/$CMD.log


python -m deploy $CMD $CONFIG "$CKPT"           \
	--o $WORK_DIR    \
	--u8          --nhwc                        \
	--dup_i=2     $ARGS                         \
	2>&1 | tee $WORK_DIR/$CMD.log

